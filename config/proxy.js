// proxy.js
const fs = require("fs");
const path = require("path");

const encoding = "utf-8";
/**
 * 获取配置文件内容 getContent('proxyConfig.json')
 * @param filename env.json
 * @returns {string}
 */
const getContent = (filename) => {
  const dir = path.resolve(process.cwd(), "config");
  return fs.readFileSync(path.resolve(dir, filename), { encoding });
};

const jsonParse = (obj) => {
  return Function('"use strict";return (' + obj + ")")();
};

/**
 * 获取配置选项 getConfig()
 * @returns {{}|*}
 */
const getConfig = () => {
  try {
    return jsonParse(getContent("proxyConfig.json"));
  } catch (e) {
    return {};
  }
};

module.exports = {
  proxy: {
    // 接口匹配规则自行修改
    "/crm": {
      target: "that must have a empty placeholder", // 这里必须要有字符串来进行占位
      changeOrigin: true,
      router: () => (getConfig() || {}).target || "",
      pathRewrite: {
        // ["^" + process.env.VUE_APP_BASE_API]: "/",
      },
      headers: {
        domain: getConfig().target,
        origin: getConfig().target,
      },
    },
    "/swmonitor": {
      target: "that must have a empty placeholder", // 这里必须要有字符串来进行占位
      changeOrigin: true,
      router: () => (getConfig() || {}).swmonitor || "",
      pathRewrite: {
        // ["^" + process.env.VUE_APP_BASE_API]: "/",
      },
      headers: {
        domain: getConfig().swmonitor,
        origin: getConfig().swmonitor,
      },
    },
    "/monitor": {
      target: "that must have a empty placeholder", // 这里必须要有字符串来进行占位
      changeOrigin: true,
      router: () => (getConfig() || {}).monitor || "",
      pathRewrite: {
        // ["^" + process.env.VUE_APP_BASE_API]: "/",
      },
      headers: {
        domain: getConfig().monitor,
        origin: getConfig().monitor,
      },
    },
    "/sansecplat": {
      target: "that must have a empty placeholder", // 这里必须要有字符串来进行占位
      changeOrigin: true,
      router: () => (getConfig() || {}).sansecplat || "",
      pathRewrite: {
        // ['^' + process.env.VUE_APP_BASE_API]: '/'
      },
      headers: {
        domain: getConfig().sansecplat,
        origin: getConfig().sansecplat,
      },
    },
    [process.env.VUE_APP_BASE_API]: {
      target: "that must have a empty placeholder", // 这里必须要有字符串来进行占位
      changeOrigin: true,
      router: () => (getConfig() || {}).target || "",
      pathRewrite: {
        // ["^" + process.env.VUE_APP_BASE_API]: "/",
      },
      headers: {
        domain: getConfig().target,
        origin: getConfig().target,
      },
    },
  },
};
