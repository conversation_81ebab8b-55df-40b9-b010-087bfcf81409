import { stubFalse } from "lodash";
export const menu = [
  {
    alwaysShow: false,
    component: "Layout",
    hidden: false,
    meta: {
      title: "首页",
      titleEn: "Home Page",
      icon: "home2",
      noCache: true,
      link: "",
      activeMenu: "",
    },
    name: "panelPage",
    path: "/home",
    children: [
      {
        alwaysShow: false,
        component: "homePage/index.vue",
        hidden: false,
        meta: {
          title: "首页",
          titleEn: "Home Page",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
        },
        name: "panelPage",
        path: "page",
        children: [],
        query: '{"viewName": "homePage"}',
      },
      {
        alwaysShow: false,
        component: "home/hwswServices/index.vue",
        hidden: false,
        meta: {
          title: "软硬件/服务组合报价说明",
          titleEn: "软硬件/服务组合报价说明",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "/home/<USER>/service",
        },
        name: "hwswService",
        path: "hwsw/service",
        children: [],
        query: "",
      },
      {
        alwaysShow: false,
        component: "home/hwswServices/add/index.vue",
        hidden: true,
        meta: {
          title: "新增",
          titleEn: "Add",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "/home/<USER>/service",
        },
        name: "hwswServiceOper",
        path: "hwsw/service/oper",
        children: [],
        query: "",
      },
    ],
    query: "",
  },
  {
    alwaysShow: true,
    component: "Layout",
    hidden: false,
    meta: {
      title: "信息查询",
      titleEn: "Information Search",
      icon: "search",
      noCache: true,
      link: "",
      activeMenu: "",
    },
    name: "infoSearch",
    path: "/infoSearch",
    children: [
      {
        alwaysShow: false,
        component: "infoSearch/knowledge/index.vue",
        hidden: false,
        meta: {
          title: "知识管理",
          titleEn: "Knowledge Management",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
        },
        name: "knowledgeManage",
        path: "knowledge",
        children: [],
        query: "",
      },
      {
        component: "infoSearch/product/index.vue",
        hidden: false,
        meta: {
          title: "产品管理",
          titleEn: "Product",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "/infoSearch/product/index",
        },
        name: "productManage",
        path: "product/index",
        children: [],
        query: "",
      },
      {
        component: "infoSearch/product/add/index.vue",
        hidden: true,
        meta: {
          title: "新增产品",
          titleEn: "Add Product",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "/infoSearch/product/index",
        },
        name: "ProductAdd",
        path: "product/add",
        children: [],
        query: "",
      },
    ],
    query: "",
  },
  {
    alwaysShow: false,
    component: "Layout",
    hidden: false,
    meta: {
      title: "工作汇报",
      titleEn: "Work Report",
      icon: "workReport",
      noCache: true,
      link: "",
      activeMenu: "",
    },
    name: "workReport",
    path: "/workReport",
    children: [
      {
        alwaysShow: false,
        component: "workReport/list/index.vue",
        hidden: false,
        meta: {
          title: "汇报列表",
          titleEn: "Report List",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
        },
        name: "workReportList",
        path: "list/index",
        children: [],
        query: "",
      },
    ],
    query: "",
  },
  {
    alwaysShow: false,
    component: "Layout",
    hidden: false,
    meta: {
      title: "业务跟进",
      titleEn: "Business Follow-Up",
      icon: "business",
      noCache: true,
      link: "",
      activeMenu: "",
    },
    name: "businessManage",
    path: "/business",
    children: [
      {
        alwaysShow: false,
        component: "business/customer/index.vue",
        hidden: false,
        meta: {
          title: "客户档案",
          titleEn: "Customer Profile",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "/business/customer/index",
        },
        name: "businessCustomerManage",
        path: "customer/index",
        children: [],
        query: "",
      },
      {
        alwaysShow: false,
        component: "business/customer/add/index.vue",
        hidden: true,
        meta: {
          title: "客户档案-新建",
          titleEn: "Add Customer Profile",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "/business/customer/index",
        },
        name: "businessCustomerAdd",
        path: "customer/add",
        children: [],
        query: "",
      },
      {
        alwaysShow: false,
        component: "business/contact/index.vue",
        hidden: false,
        meta: {
          title: "联系人",
          titleEn: "Contact",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "/business/contact/index",
        },
        name: "businessContact",
        path: "contact/index",
        children: [],
        query: "",
      },
      {
        alwaysShow: false,
        component: "business/contact/add/index.vue",
        hidden: true,
        meta: {
          title: "联系人-新建",
          titleEn: "Add Contact",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "/business/contact/index",
        },
        name: "businessContactAdd",
        path: "contact/add",
        children: [],
        query: "",
      },
      {
        alwaysShow: false,
        component: "business/projectOpportunity/index.vue",
        hidden: false,
        meta: {
          title: "项目机会跟进",
          titleEn: "Project Opportunity Follow-Up",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "/business/project/opportunity",
        },
        name: "businessOpportunity",
        path: "project/opportunity",
        children: [],
        query: "",
      },
      {
        alwaysShow: false,
        component: "business/projectOpportunity/add/index.vue",
        hidden: true,
        meta: {
          title: "项目机会跟进-新建",
          titleEn: "Add Project Opportunity Follow-Up",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "/business/project/opportunity",
        },
        name: "businessOpportunityAdd",
        path: "project/opportunity/add",
        children: [],
        query: "",
      },
      {
        alwaysShow: false,
        component: "business/projectAuth/index.vue",
        hidden: false,
        meta: {
          title: "项目授权",
          titleEn: "Project Authorization",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "/business/project/auth",
        },
        name: "businessProjectAuth",
        path: "project/auth",
        children: [],
        query: "",
      },
      {
        alwaysShow: false,
        component: "business/projectAuth/add/index.vue",
        hidden: true,
        meta: {
          title: "新增授权",
          titleEn: "Add Authorization",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "/business/project/auth",
        },
        name: "projectAuthOper",
        path: "project/auth/oper",
        children: [],
        query: "",
      },
      {
        alwaysShow: false,
        component: "business/bottomOrder/index.vue",
        hidden: false,
        meta: {
          title: "垫底订单",
          titleEn: "Bottoming Orders",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
        },
        name: "businessBottomOrder",
        path: "bottom/order",
        children: [],
        query: "",
      },
      {
        alwaysShow: false,
        component: "business/quotation/index.vue",
        hidden: false,
        meta: {
          title: "报价单",
          titleEn: "Quotation",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
        },
        name: "businessQuotation",
        path: "quotation/index",
        children: [],
        query: "",
      },
    ],
    query: "",
  },
  {
    alwaysShow: false,
    component: "Layout",
    hidden: false,
    meta: {
      title: "合同管理",
      titleEn: "Contract Management",
      icon: "contract",
      noCache: true,
      link: "",
      activeMenu: "",
    },
    name: "contractManagement",
    path: "/contract",
    children: [
      {
        alwaysShow: false,
        component: "contract/order/index.vue",
        hidden: false,
        meta: {
          title: "合同订单",
          titleEn: "Contract Orders",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "/contract/order/index",
        },
        name: "contractOrder",
        path: "order/index",
        children: [],
        query: "",
      },
      {
        alwaysShow: true,
        component: "contract/order/add/index.vue",
        hidden: true,
        meta: {
          title: "新增-合同订单",
          titleEn: "Add Contract Order",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "/contract/order/index",
        },
        name: "contractOrderAdd",
        path: "order/add",
        children: [],
        query: "",
      },
      {
        alwaysShow: false,
        component: "contract/saleInvoice/index.vue",
        hidden: false,
        meta: {
          title: "销售发票",
          titleEn: "Sales Invoices",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "/contract/sale/invoice",
        },
        name: "contractSaleInvoice",
        path: "sale/invoice",
        children: [],
        query: "",
      },
      {
        alwaysShow: false,
        component: "contract/saleInvoice/add/index.vue",
        hidden: true,
        meta: {
          title: "新增-销售发票",
          titleEn: "Add Sales Invoice",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "/contract/sale/invoice",
        },
        name: "saleInvoiceAdd",
        path: "sale/invoice/add",
        children: [],
        query: "",
      },
      //   {
      //     alwaysShow: false,
      //     component: "contract/paymentPlans/index.vue",
      //     hidden: false,
      //     meta: {
      //       title: "回款计划",
      //       titleEn: "Payment Plans",
      //       icon: "",
      //       noCache: true,
      //       link: "",
      //       activeMenu: "",
      //     },
      //     name: "contractPaymentPlan",
      //     path: "payment/plan",
      //     children: [],
      //     query: "",
      //   },
      {
        alwaysShow: false,
        component: "contract/receiptVoucher/index.vue",
        hidden: false,
        meta: {
          title: "收款单",
          titleEn: "Receipt Vouchers",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
        },
        name: "contractReceiptVoucher",
        path: "receipt/voucher",
        children: [],
        query: "",
      },
      {
        alwaysShow: false,
        component: "contract/internal/index.vue",
        hidden: false,
        meta: {
          title: "内部关联合同",
          titleEn: "Internal Contracts",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
        },
        name: "contractInternal",
        path: "/contract/internal/index",
        children: [],
        query: "",
      },
      {
        alwaysShow: false,
        component: "contract/internal/add/index.vue",
        hidden: true,
        meta: {
          title: "新增-内部关联合同",
          titleEn: "Add Internal Contracts",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "/contract/internal/index",
        },
        name: "contractInternalAdd",
        path: "internal/add",
        children: [],
        query: "",
      },
    ],
    query: "",
  },
  {
    alwaysShow: false,
    component: "Layout",
    hidden: false,
    meta: {
      title: "供货管理",
      titleEn: "Supply Management",
      icon: "supply",
      noCache: true,
      link: "",
      activeMenu: "",
    },
    name: "SupplyManage",
    path: "/supply",
    children: [
      {
        alwaysShow: false,
        component: "supply/exportDetail/index.vue",
        hidden: false,
        meta: {
          title: "出口明细表",
          titleEn: "Export Detail Sheet",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
        },
        name: "SupplyExportDetail",
        path: "export/detail",
        children: [],
        query: "",
      },
      {
        alwaysShow: false,
        component: "supply/transferOrder/index.vue",
        hidden: false,
        meta: {
          title: "调货单",
          titleEn: "Transfer Order",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
        },
        name: "SupplyTransferOrder",
        path: "transfer/order",
        children: [],
        query: "",
      },
      {
        alwaysShow: false,
        component: "supply/shippingNotice/index.vue",
        hidden: false,
        meta: {
          title: "发货通知单",
          titleEn: "Shipping Notice",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
        },
        name: "SupplyShippingNotice",
        path: "shipping/notice",
        children: [],
        query: "",
      },
      {
        alwaysShow: false,
        component: "supply/returnProduct/index.vue",
        hidden: false,
        meta: {
          title: "返回产品跟进",
          titleEn: "ReturnedProduct Follow-Up",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
        },
        name: "SupplyReturnProduct",
        path: "return/production",
        children: [],
        query: "",
      },
      {
        alwaysShow: false,
        component: "supply/compressionTest/index.vue",
        hidden: false,
        meta: {
          title: "供货取消及压缩测试",
          titleEn: "Supply Cancellation And Compression Testing",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
        },
        name: "SupplyCompressionTest",
        path: "compression/test",
        children: [],
        query: "",
      },
      {
        alwaysShow: false,
        component: "supply/special/index.vue",
        hidden: false,
        meta: {
          title: "特殊供货申请",
          titleEn: "Special Supply Application",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "/supply/special",
        },
        name: "SupplySpecial",
        path: "special",
        children: [],
        query: "",
      },
      {
        alwaysShow: false,
        component: "supply/special/add/index.vue",
        hidden: true,
        meta: {
          title: "新增殊供货申请",
          titleEn: "Add Special Supply Application",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "/supply/special",
        },
        name: "SupplySpecialAdd",
        path: "special/add",
        children: [],
        query: "",
      },
    ],
    query: "",
  },
  {
    alwaysShow: false,
    component: "Layout",
    hidden: false,
    meta: {
      title: "报表分析",
      titleEn: "Report Analysis",
      icon: "reportAnalysis",
      noCache: true,
      link: "",
      activeMenu: "",
    },
    name: "reportAnalysis",
    path: "/ReportAnalysis",
    children: [],
    query: "",
  },
  {
    alwaysShow: false,
    component: "Layout",
    hidden: false,
    meta: {
      title: "工单管理",
      titleEn: "Work Order Management",
      icon: "workOrder",
      noCache: true,
      link: "",
      activeMenu: "",
    },
    name: "workOrder",
    path: "/workOrder",
    children: [],
    query: "",
  },
  {
    alwaysShow: false,
    component: "Layout",
    hidden: false,
    meta: {
      title: "业务支持",
      titleEn: "Business Support",
      icon: "businessSupport",
      noCache: true,
      link: "",
      activeMenu: "",
    },
    name: "businessSupport",
    path: "/BusinessSupport",
    children: [
      {
        alwaysShow: false,
        component: "support/workHours/index.vue",
        hidden: false,
        meta: {
          title: "工时",
          titleEn: "Working Hours",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
        },
        name: "workHours",
        path: "work/hours",
        children: [],
        query: "",
      },
      {
        alwaysShow: false,
        component: "support/pmoApply/index.vue",
        hidden: false,
        meta: {
          title: "PMO申请",
          titleEn: "PMO Application",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
        },
        name: "pmoApply",
        path: "pmo/apply",
        children: [],
        query: "",
      },
      {
        alwaysShow: false,
        component: "support/productionTrack/index.vue",
        hidden: false,
        meta: {
          title: "产品试制跟踪",
          titleEn: "Product Trial Tracking",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
        },
        name: "productionTrack",
        path: "production/track",
        children: [],
        query: "",
      },
      {
        alwaysShow: false,
        component: "support/testProject/index.vue",
        hidden: false,
        meta: {
          title: "测试项目管理",
          titleEn: "Test Project Management",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
        },
        name: "testProject",
        path: "test/project",
        children: [],
        query: "",
      },
      {
        alwaysShow: false,
        component: "support/productionRequire/index.vue",
        hidden: false,
        meta: {
          title: "产品需求收集",
          titleEn: "Product Requirement Collection",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
        },
        name: "productionRequire",
        path: "production/require",
        children: [],
        query: "",
      },
    ],
    query: "",
  },

  {
    alwaysShow: false,
    component: "Layout",
    hidden: false,
    meta: {
      title: "用户和权限管理",
      titleEn: "User and Permission Management",
      icon: "userPermission",
      noCache: true,
      link: "",
      activeMenu: "",
    },
    name: "userPermission",
    path: "/userPermission",
    children: [],
    query: "",
  },
  {
    alwaysShow: false,
    component: "Layout",
    hidden: false,
    meta: {
      title: "test流程",
      titleEn: "Flow",
      icon: "userPermission",
      noCache: true,
      link: "",
      activeMenu: "",
    },
    name: "flow",
    path: "/flow",
    children: [
      {
        component: "flow/index.vue",
        hidden: false,
        meta: {
          title: "test流程",
          titleEn: "Quotation",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "/flow/index",
        },
        name: "flow",
        path: "index",
        children: [],
        query: "",
      },
    ],
    query: "",
  },
  {
    component: "infoSearch/knowledge/preview.vue",
    hidden: true,
    name: "previewFile",
    path: "/preview/online",
    query: "",
    meta: {
      title: "预览文件",
      noCache: true,
    },
  },
];
