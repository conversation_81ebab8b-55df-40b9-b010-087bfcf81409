import request from "@/utils/request";
import config from "../configs";
import { transFormData } from "@/utils/util";
import { getToken } from "@/utils/auth";
import { downloadJsonData } from "@/utils/request";

let baseUrl = `${config.PROJAPI}/quotation`;
// 分页查询
export function getServicePage(data) {
  return request({
    url: `${baseUrl}/page`,
    method: "post",
    data,
  });
}

// 新增
export function addService(data) {
  return request({
    url: `${baseUrl}/add`,
    method: "post",
    data,
  });
}

// 编辑
export function editService(data) {
  return request({
    url: `${baseUrl}/edit`,
    method: "post",
    data,
  });
}

// 删除
export function deleteService(data) {
  return request({
    url: `${baseUrl}/delete`,
    method: "post",
    data,
  });
}
