import request from "@/utils/request";

let baseurl = `${swGlobal.PROJAPI}/pt/common/api/user/v1`;

// webƽ̨��ȡ����Ȩ��
export function getOperAuth() {
  let baseurl = `${swGlobal.PLATFORM}/menu`;
  return request({
    url: `${baseurl}/queryCurrentUserMenus`,
    method: "get",
    params: { menuType: 1 },
  });
}

// �ܷ�ƽ̨ topbar
export function getUserInfo() {
  return request({
    url: `${baseurl}/getLoginUserInfo`,
    method: "post",
  });
}
