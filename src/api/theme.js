import request from "@/utils/request";

// 根据id获取主题(不需token)
export function getThemeById(id) {
  return request({
    url: "/theme/get",
    method: "get",
    params: {
      themeId: id,
    },
  });
}

export function getDefaultThemeObj() {
  return new Promise(function (resolve, reject) {
    let safeptTheme = {
      themeId: "1779093242421792768",
      themeNameZh: "数据安全平台1",
      themeNameEn: "数据安全平台",
      webTitle: "数据安全平台",
      tagsView: true,
      dynamicTitle: false,
      sidebarLogo: false, //隐藏左侧导航顶部logo
      fixedHeader: true, //顶部头部悬浮
      theme: "#1C6CDD",
      sideTheme: "theme-custom",
      menuColorCustom: "#666666",
      menuBackgroundCustom: "#ffffff",
      menuColorActiveCustom: "#0256FF",
      subMenuBackgroundCustom: "#ffffff",
      subMenuBackgroundActiveCustom: "#ffffff",
      subMenuHoverCustom: "#1C6CDD22",
      topBackgroundCustom: "#333333",
      topSvgCustom: "#c1c1c1",
      showMultModule: false, //多模块展示
    };
    let deviceptTheme = {
      themeId: "1779093242421792768",
      themeNameZh: "三未信安CRM系统",
      themeNameEn: "Sansec CRM System",
      webTitle: "三未信安CRM系统",
      tagsView: true,
      dynamicTitle: false,
      sidebarLogo: false, //隐藏左侧导航顶部logo
      fixedHeader: true, //顶部头部悬浮
      theme: "#1C6CDD",
      sideTheme: "theme-custom",
      menuColorCustom: "#666666",
      menuBackgroundCustom: "#ffffff",
      menuColorActiveCustom: "#0256FF",
      subMenuBackgroundCustom: "#ffffff",
      subMenuBackgroundActiveCustom: "#ECF2FF",
      subMenuHoverCustom: "#1C6CDD22",
      topBackgroundCustom: "#001529",
      topSvgCustom: "#c1c1c1",
      showMultModule: false, //多模块展示
    };
    resolve({
      requestId: null,
      code: "0",
      status: "0",
      message: "操作成功！",
      timestamp: null,
      costMillis: 0,
      // data: safeptTheme,
      data: deviceptTheme,
      // result: defaultWebTheme,
      success: true,
    });
  });
  return request({
    url: "/theme/default",
    method: "get",
  });
}

// 获取图片信息
export function getImgInfo(imageCode, themeId) {
  return request({
    url: "/sys/manage/image/download",
    method: "get",
    params: {
      imageCode,
      themeId,
    },
  });
}
