import request from "@/utils/request";
import config from "../configs";
import { downloadJsonData } from "@/utils/request";

let baseUrl = `${config.PROJAPI}/contract/order`;

export function getContractList(data) {
  return request({
    url: `${baseUrl}/queryToPage`,
    method: "post",
    data,
  });
}
// 在原本分页基础上增加产品信息
export function getContractProductList(data) {
  return request({
    url: `${baseUrl}/queryBaseAndProdToPage`,
    method: "post",
    data,
  });
}

export function getContractDetail(data) {
  return request({
    url: `${baseUrl}/queryById`,
    method: "post",
    data,
  });
}
export function addContract(data) {
  return request({
    url: `${baseUrl}/add`,
    method: "post",
    data,
  });
}
export function editContract(data) {
  return request({
    url: `${baseUrl}/update`,
    method: "post",
    data,
  });
}

export function deleteContract(data) {
  return request({
    url: `${baseUrl}/deleteById`,
    method: "post",
    data,
  });
}

export function importContract(data) {
  return request({
    url: `${baseUrl}/import`,
    method: "post",
    headers: {
      "Content-Type": "multipart/form-data",
    },
    data,
  });
}
export function exportContract(data) {
  return downloadJsonData(`${baseUrl}/export`, data);
  //   return request({
  //     url: `${baseUrl}/export`,
  //     method: "post",
  //     data,
  //   });
}
