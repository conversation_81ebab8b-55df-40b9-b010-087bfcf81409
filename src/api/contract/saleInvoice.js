import request from "@/utils/request";
import config from "../configs";
import { downloadJsonData } from "@/utils/request";

let baseUrl = `${config.PROJAPI}/invoice`;

export function getInvoiceList(data) {
  return request({
    url: `${baseUrl}/page`,
    method: "post",
    data,
  });
}

export function getInvoiceDetail(data) {
  return request({
    url: `${baseUrl}/info`,
    method: "post",
    data,
  });
}
export function addInvoice(data) {
  return request({
    url: `${baseUrl}/add`,
    method: "post",
    data,
  });
}
export function editInvoice(data) {
  return request({
    url: `${baseUrl}/update`,
    method: "post",
    data,
  });
}

export function deleteInvoice(data) {
  return request({
    url: `${baseUrl}/deleteById`,
    method: "post",
    data,
  });
}

export function importInvoice(data) {
  return request({
    url: `${baseUrl}/import`,
    method: "post",
    headers: {
      "Content-Type": "multipart/form-data",
    },
    data,
  });
}
export function exportInvoice(data) {
  return downloadJsonData(`${baseUrl}/export`, data);
  //   return request({
  //     url: `${baseUrl}/export`,
  //     method: "post",
  //     data,
  //   });
}

// 查询已提开票申请信息(通过合同编号)
export function getApplyInfoByContract(data) {
  return request({
    url: `${baseUrl}/queryApplyInfos`,
    method: "post",
    data,
    headers: {
      repeatSubmit: false,
    },
  });
}
