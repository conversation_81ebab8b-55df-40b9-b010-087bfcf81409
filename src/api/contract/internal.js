import request from "@/utils/request";
import config from "../configs";
import { downloadJsonData } from "@/utils/request";

let baseUrl = `${config.PROJAPI}/internal/contract`;

export function getInterContractList(data) {
  return request({
    url: `${baseUrl}/queryToPage`,
    method: "post",
    data,
  });
}

export function getInterContractDetail(data) {
  return request({
    url: `${baseUrl}/queryById`,
    method: "post",
    data,
  });
}
export function addInterContract(data) {
  return request({
    url: `${baseUrl}/add`,
    method: "post",
    data,
  });
}
export function editInterContract(data) {
  return request({
    url: `${baseUrl}/update`,
    method: "post",
    data,
  });
}

export function deleteInterContract(data) {
  return request({
    url: `${baseUrl}/deleteById`,
    method: "post",
    data,
  });
}

export function importInterContract(data) {
  return request({
    url: `${baseUrl}/import`,
    method: "post",
    headers: {
      "Content-Type": "multipart/form-data",
    },
    data,
  });
}
export function exportInterContract(data) {
  return downloadJsonData(`${baseUrl}/export`, data);
  //   return request({
  //     url: `${baseUrl}/export`,
  //     method: "post",
  //     data,
  //   });
}
