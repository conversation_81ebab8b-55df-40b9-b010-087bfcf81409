import request from "@/utils/request";
import config from "../configs";
import { downloadJsonData } from "@/utils/request";

let baseUrl = `${config.PROJAPI}/product/manage`;

export function getProductList(data) {
  return request({
    url: `${baseUrl}/queryToPage`,
    method: "post",
    data,
  });
}

export function getProductDetail(data) {
  return request({
    url: `${baseUrl}/searchById`,
    method: "post",
    data,
  });
}
// 新增/同步（前端不用）
export function addProduct(data) {
  return request({
    url: `${baseUrl}/sync`,
    method: "post",
    data,
  });
}
export function editProduct(data) {
  return request({
    url: `${baseUrl}/updateById`,
    method: "post",
    data,
  });
}

export function deleteProduct(data) {
  return request({
    url: `${baseUrl}/deleteById`,
    method: "post",
    data,
  });
}

export function importProduct(data) {
  return request({
    url: `${baseUrl}/import`,
    method: "post",
    headers: {
      "Content-Type": "multipart/form-data",
    },
    data,
  });
}
export function exportProduct(data) {
  return downloadJsonData(`${baseUrl}/export`, data);
  //   return request({
  //     url: `${baseUrl}/export`,
  //     method: "post",
  //     data,
  //   });
}
export function assignProduct(data) {
  return request({
    url: `${baseUrl}/assign`,
    method: "post",
    data,
  });
}
export function shareProduct(data) {
  return request({
    url: `${baseUrl}/share`,
    method: "post",
    data,
  });
}

// 根据U9编码查询产品信息
export function getProductDetailByU9(data) {
  return request({
    url: `${baseUrl}/searchByUnine`,
    method: "post",
    data,
  });
}
