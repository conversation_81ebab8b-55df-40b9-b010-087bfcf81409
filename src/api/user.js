import request from "@/utils/request";
import config from "./configs";

// 获取用户详细信息
export function getInfo() {
  return request({
    url: `${config.PROJAPI}/sec/user/getUserByToken`,
    method: "get",
  });
}

// 获取用户详情
export function getUserDetail(id) {
  return request({
    url: "/sec/user/findById",
    method: "get",
    params: {
      id,
    },
  });
}
// 新增用户
export function saveUser(data) {
  return request({
    url: "/sec/user/add",
    method: "post",
    data,
  });
}
// 编辑用户
export function editUser(data) {
  return request({
    url: "/sec/user/edit",
    method: "post",
    data,
  });
}

// 删除用户
export function deleteUser(ids) {
  return request({
    url: "/sec/user/delete",
    method: "get",
    params: {
      ids,
    },
  });
}
// 修改密码
export function changePassword(params) {
  return request({
    url: "/sec/user/changePwd",
    method: "post",
    data: params,
  });
}

/****************新项目中******************************* */
let baseUrl = `${config.PROJAPI}/user/management`;

// 获取用户列表
export function getUserList(data) {
  return request({
    url: `${baseUrl}/querytopage`,
    method: "post",
    data,
  });
}
