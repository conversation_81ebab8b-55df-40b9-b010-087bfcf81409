import request from "@/utils/request";
import config from "../configs";
import { downloadJsonData } from "@/utils/request";

// 附件管理
let baseUrl = `${config.PROJAPI}/attachment`;

// 通过id删除附件
export function deleteFileById(data) {
  return request({
    url: `${baseUrl}/deleteById`,
    method: "post",
    data,
  });
}

// 上传文件返回id
export function getFileId(data) {
  return request({
    url: `${baseUrl}/uploadFilesTOAttachId`,
    method: "post",
    headers: {
      "Content-Type": "multipart/form-data",
    },
    data,
  });
}

// 上传文件返回路径
export function getFilePath(data) {
  return request({
    url: `${baseUrl}/uploadFilesTOPath`,
    method: "post",
    headers: {
      "Content-Type": "multipart/form-data",
    },
    data,
  });
}

// 上传文件返回数据内容
export function getFileData(data) {
  return request({
    url: `${baseUrl}/uploadFilesTOAttachData`,
    method: "post",
    headers: {
      "Content-Type": "multipart/form-data",
    },
    data,
  });
}
