import request from "@/utils/request";
import config from "../configs";
let baseUrl = `${config.PROJAPI}/dict`;
// 单个字典
export function getSysDict(data) {
  return request({
    url: `${baseUrl}/queryList`,
    method: "post",
    headers: {
      repeatSubmit: false,
    },
    data,
  });
}
// 多个字典
export function getSysDicts(data) {
  return request({
    url: `${baseUrl}/queryBatch`,
    method: "post",
    headers: {
      repeatSubmit: false,
    },
    data,
  });
}
// 添加字典下拉
export function addSysDict(data) {
  return request({
    url: `${baseUrl}/insert`,
    method: "post",
    headers: {
      repeatSubmit: false,
    },
    data,
  });
}
