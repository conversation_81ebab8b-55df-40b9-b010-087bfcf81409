import request from "@/utils/request";
import { downLoadFile } from "@/utils/request";
import config from "./configs";
import { getToken } from "@/utils/auth";
// 登录方法用户名/密码
export function loginUser(data) {
  return request({
    //   url: `${config.PROJAPI}/login/loginByUp`,
    url: "/login/loginByUp",
    headers: {
      isToken: false,
    },
    method: "post",
    data,
  });
}

// 登录 uk
export function loginUk(data) {
  return request({
    url: "/login/loginByUk",
    headers: {
      isToken: false,
    },
    method: "post",
    data,
  });
}

// 退出方法
export function logout() {
  return request({
    url: "/login/logOut",
    method: "post",
    headers: {
      repeatSubmit: false,
    },
  });
}

// 生成随机数
export function getSecretKey() {
  return request({
    url: "/login/getSecretKey",
    method: "get",
  });
}
// 校验uk
export function checkUk(data) {
  return request({
    url: "/login/ukCheck",
    method: "post",
    data,
  });
}
