import request from "@/utils/request";
import config from "../configs";
import { downloadJsonData } from "@/utils/request";

let baseUrl = `${config.PROJAPI}/customer`;

export function getCustomerList(data) {
  return request({
    url: `${baseUrl}/page`,
    method: "post",
    data,
  });
}

export function getCustomerDetail(data) {
  return request({
    url: `${baseUrl}/info`,
    method: "post",
    data,
  });
}
export function addCustomer(data) {
  return request({
    url: `${baseUrl}/add`,
    method: "post",
    data,
  });
}
export function editCustomer(data) {
  return request({
    url: `${baseUrl}/update`,
    method: "post",
    data,
  });
}

export function deleteCustomer(data) {
  return request({
    url: `${baseUrl}/delete`,
    method: "post",
    data,
  });
}

export function importCustomer(data) {
  return request({
    url: `${baseUrl}/import`,
    method: "post",
    headers: {
      "Content-Type": "multipart/form-data",
    },
    data,
  });
}
export function exportCustomer(data) {
  return downloadJsonData(`${baseUrl}/export`, data);
  //   return request({
  //     url: `${baseUrl}/export`,
  //     method: "post",
  //     data,
  //   });
}
export function assignCustomer(data) {
  return request({
    url: `${baseUrl}/assign`,
    method: "post",
    data,
  });
}
export function shareCustomer(data) {
  return request({
    url: `${baseUrl}/share`,
    method: "post",
    data,
  });
}
