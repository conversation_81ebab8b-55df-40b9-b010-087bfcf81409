import request from "@/utils/request";
import config from "../configs";
import { downloadJsonData } from "@/utils/request";

let baseUrl = `${config.PROJAPI}/contact`;

export function getContactList(data) {
  return request({
    url: `${baseUrl}/page`,
    method: "post",
    data,
  });
}

export function getContactDetail(data) {
  return request({
    url: `${baseUrl}/info`,
    method: "post",
    data,
  });
}
export function addContact(data) {
  return request({
    url: `${baseUrl}/add`,
    method: "post",
    data,
  });
}
export function editContact(data) {
  return request({
    url: `${baseUrl}/update`,
    method: "post",
    data,
  });
}

export function deleteContact(data) {
  return request({
    url: `${baseUrl}/delete`,
    method: "post",
    data,
  });
}

export function importContact(data) {
  return request({
    url: `${baseUrl}/import`,
    method: "post",
    headers: {
      "Content-Type": "multipart/form-data",
    },
    data,
  });
}
export function exportContact(data) {
  return downloadJsonData(`${baseUrl}/export`, data);
  //   return request({
  //     url: `${baseUrl}/export`,
  //     method: "post",
  //     data,
  //   });
}
export function assignContact(data) {
  return request({
    url: `${baseUrl}/assign`,
    method: "post",
    data,
  });
}
export function shareContact(data) {
  return request({
    url: `${baseUrl}/share`,
    method: "post",
    data,
  });
}
