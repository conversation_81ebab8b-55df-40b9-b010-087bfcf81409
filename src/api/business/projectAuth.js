import request from "@/utils/request";
import config from "../configs";
import { downloadJsonData } from "@/utils/request";
import { transFormData } from "@/utils/util";

const baseUrl = `${config.PROJAPI}/project/auth`;

// 获取项目授权分页
export function getProjectAuthPage(data) {
  return request({
    url: `${baseUrl}/find`,
    method: "post",
    data,
  });
}

// 新增项目授权
export function addProjectAuth(data) {
  return request({
    url: `${baseUrl}/add`,
    method: "post",
    data: transFormData(data),
  });
}

// 编辑项目授权
export function editProjectAuth(data) {
  return request({
    url: `${baseUrl}/update`,
    method: "post",
    data: transFormData(data),
  });
}

// 删除项目授权
export function deleteProjectAuth(data) {
  return request({
    url: `${baseUrl}/delete`,
    method: "post",
    data,
  });
}

// 下载
export function downloadProjectAuth(data) {
  return downloadJsonData(`${baseUrl}/download`, data);
}

// 详情
export function getProjectAuthDetail(data) {
  return request({
    url: `${baseUrl}/findById`,
    method: "post",
    data,
  });
}
