import request from "@/utils/request";
import config from "../configs";
import { downloadJsonData } from "@/utils/request";

let baseUrl = `${config.PROJAPI}/follow/projectopportunity`;

export function getOpportunityList(data) {
  return request({
    url: `${baseUrl}/queryToPage`,
    method: "post",
    data,
  });
}

export function getOpportunityDetail(data) {
  return request({
    url: `${baseUrl}/queryById`,
    method: "post",
    data,
  });
}
export function addOpportunity(data) {
  return request({
    url: `${baseUrl}/add`,
    method: "post",
    data,
  });
}
export function editOpportunity(data) {
  return request({
    url: `${baseUrl}/update`,
    method: "post",
    data,
  });
}

export function deleteOpportunity(data) {
  return request({
    url: `${baseUrl}/deleteById`,
    method: "post",
    data,
  });
}
