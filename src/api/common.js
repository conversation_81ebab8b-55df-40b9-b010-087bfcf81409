import request from "@/utils/request";
import config from "./configs";
let baseurl = `${config.PROJAPI}`;

/**
 * 获取认证模式
 */
export function getAuthModeData() {
  return request({
    url: "/auth/mode/list",
    method: "get",
  });
}
/**
 * 获取系统信息
 */
export function getSysConfig() {
  return request({
    url: "/login/sysConfig",
    method: "get",
  });
}

// 获取系统字典
// export function getSysDict(typeCode) {
//   return request({
//     url: "/secDict/query",
//     method: "get",
//     params: {
//       typeCode,
//     },
//   });
// }
// 获取多个系统字典
// export function getSysDicts(data) {
//   return request({
//     url: `${config.PLATFORM}/secDict/queryByTypeCode`,
//     method: "post",
//     headers: {
//       repeatSubmit: false,
//     },
//     data,
//   });
// }

//CRM平台 字典
// export function getSysDict(data) {
//   return request({
//     url: `${config.PROJAPI}/dict/queryList`,
//     method: "post",
//     headers: {
//       repeatSubmit: false,
//     },
//     data,
//   });
// }
// export function getSysDicts(data) {
//   return request({
//     url: `${config.PROJAPI}/dict/queryBatch`,
//     method: "post",
//     headers: {
//       repeatSubmit: false,
//     },
//     data,
//   });
// }
// export function addSysDict(data) {
//   return request({
//     url: `${config.PROJAPI}/dict//insert`,
//     method: "post",
//     headers: {
//       repeatSubmit: false,
//     },
//     data,
//   });
// }

export function getShowColumns(data) {
  return request({
    url: `${config.PROJAPI}/columns/show/list`,
    method: "post",
    headers: {
      repeatSubmit: false,
    },
    data,
  });
}
