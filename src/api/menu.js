import request from "@/utils/request";

// 获取路由
export const getRouters = () => {
  return request({
    url: "/menu/queryCatalogue",
    method: "get",
  });
};
// 获取所有菜单
export function getAllMenuList(roleId) {
  return request({
    url: "/menu/queryMenus",
    method: "get",
    params: {
      roleId,
    },
  });
}

// 获取该角色选中的菜单
export function getMenuFormRule(id) {
  return request({
    url: "/menu/queryLastMenuNode",
    method: "get",
    params: {
      roleId: id,
    },
  });
}
// 获取首页菜单url
export function getIndexMenu() {
  return request({
    url: "/menu/queryIndexPage",
    method: "get",
  });
}

// 获取用户拥有的按钮权限
export function getOperAuth() {
  return request({
    url: "/menu/queryCurrentUserMenus",
    params: {
      menuType: 1,
    },
  });
}
