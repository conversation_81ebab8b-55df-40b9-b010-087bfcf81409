import request from "@/utils/request";
import config from "../configs";
import { downloadJsonData } from "@/utils/request";

let baseUrl = `${config.PROJAPI}/special/supply`;

export function getSupplyApplyList(data) {
  return request({
    url: `${baseUrl}/queryToPage`,
    method: "post",
    data,
  });
}

export function getSupplyApplyDetail(data) {
  return request({
    url: `${baseUrl}/queryById`,
    method: "post",
    data,
  });
}
export function addSupplyApply(data) {
  return request({
    url: `${baseUrl}/add`,
    method: "post",
    data,
  });
}
export function editSupplyApply(data) {
  return request({
    url: `${baseUrl}/updateById`,
    method: "post",
    data,
  });
}

export function deleteSupplyApply(data) {
  return request({
    url: `${baseUrl}/deleteById`,
    method: "post",
    data,
  });
}
// 根据供货类型和产品计算额度
export function getSupplyAmountInfo(data) {
  return request({
    url: `${baseUrl}/calculate`,
    method: "post",
    headers: {
      repeatSubmit: false,
    },
    data,
  });
}
