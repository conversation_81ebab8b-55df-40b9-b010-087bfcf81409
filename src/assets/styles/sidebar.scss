#app {
  .main-container {
    min-height: 100%;
    transition: margin-left 0.28s;
    margin-left: $base-sidebar-width;
    position: relative;
  }

  .sidebarHide {
    margin-left: 0 !important;
  }

  .sidebar-container {
    -webkit-transition: width 0.28s;
    transition: width 0.28s;
    width: $base-sidebar-width !important;
    background-color: $base-menu-background;
    height: 100%;
    position: fixed;
    font-size: 0px;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;
    -webkit-box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
    box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out,
        0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 86px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      width: 16px;
      height: 16px;
    }
    span {
      margin-left: 10px;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
    }

    .el-menu-item,
    .el-submenu__title {
      height: 40px !important;
      line-height: 40px !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;

      &:hover {
        background-color: var(--color-primary29) !important;
        color: var(--color-primary) !important;
      }
    }
    .el-menu-item {
      padding: 0px 15px 0px 45px;
    }

    // 一级菜单高亮色
    .el-menu .is-active.submenu-title-noDropdown {
      background-color: var(--color-primary) !important;
      color: #fff !important;
    }

    // 二级菜单高亮色
    .el-menu .is-opened .nest-menu .el-menu-item.is-active {
      background-color: var(--color-primary) !important;
      color: #fff !important;
    }

    & .nest-menu .el-submenu > .el-submenu__title,
    & .el-submenu .el-menu-item {
      min-width: $base-sidebar-width !important;
      //   padding-left: 62px !important;
      background-color: var(--color-primary19) !important;

      &:hover {
        background-color: var(--color-primary29) !important;
      }
    }

    // 自定义主题
    .theme-custom .el-submenu__title i {
      color: var(--custom-menu-color) !important;
    }
    & .theme-custom .el-menu-item,
    & .theme-custom .el-submenu__title {
      &:hover {
        background-color: var(--custom-sub-menu-hover) !important;
        color: var(--custom-menu-color) !important;
      }
    }

    & .theme-custom .el-menu .is-active.submenu-title-noDropdown {
      background-color: var(--custom-sub-menu-background-active) !important;
      color: var(--custom-menu-color-active) !important;
    }

    & .theme-custom .is-active > .el-submenu__title {
      color: var(--custom-menu-color-active) !important;
    }

    & .theme-custom .nest-menu .el-submenu > .el-submenu__title,
    & .theme-custom .el-submenu .el-menu-item {
      background-color: var(--custom-sub-menu-background) !important;

      &:hover {
        background-color: var(--custom-sub-menu-hover) !important;
        color: var(--custom-menu-color) !important;
      }
    }

    & .theme-custom .el-menu .is-opened .nest-menu .is-active {
      background-color: var(--custom-sub-menu-background-active) !important;
      color: var(--custom-menu-color-active) !important;
    }

    // 亮色主题
    & .theme-light .el-submenu__title i {
      color: $base-menu-light-color;
    }
    & .theme-light .el-menu-item,
    & .theme-light .el-submenu__title {
      &:hover {
        background-color: var(--color-primary29) !important;
        color: #000 !important;
      }
    }

    & .theme-light .el-menu .is-active.submenu-title-noDropdown {
      background-color: var(--color-primary) !important;
      color: #fff !important;
    }

    & .theme-light .is-active > .el-submenu__title {
      color: #000 !important;
    }

    & .theme-light .nest-menu .el-submenu > .el-submenu__title,
    & .theme-light .el-submenu .el-menu-item {
      background-color: var(--color-primary19) !important;

      &:hover {
        background-color: var(--color-primary29) !important;
        color: #000 !important;
      }
    }

    & .theme-light .el-menu .is-opened .nest-menu .is-active {
      background-color: var(--color-primary) !important;
      color: #fff !important;
    }

    // 暗色主题
    & .theme-dark .el-submenu__title i {
      color: $base-menu-color;
    }
    & .theme-dark .el-menu-item,
    & .theme-dark .el-submenu__title {
      &:hover {
        background-color: rgba(0, 0, 0, 0.3) !important;
        color: #fff !important;
      }
    }

    & .theme-dark .el-menu .is-active.submenu-title-noDropdown {
      background-color: var(--color-primary) !important;
      color: #fff !important;
    }

    & .theme-dark .is-active > .el-submenu__title {
      color: #fff !important;
    }

    & .theme-dark .nest-menu .el-submenu > .el-submenu__title,
    & .theme-dark .el-submenu .el-menu-item {
      background-color: #000 !important;

      &:hover {
        background-color: rgba(0, 0, 0, 0.3) !important;
        color: #fff !important;
      }
    }

    & .theme-dark .el-menu .is-opened .nest-menu .is-active {
      background-color: var(--color-primary) !important;
      color: #fff !important;
    }

    // theme主题
    & .theme-theme .el-submenu__title i {
      color: $base-menu-color;
    }
    & .theme-theme .el-menu-item,
    & .theme-theme .el-submenu__title {
      &:hover {
        background-color: var(--color-primary-deep80) !important;
        color: #fff !important;
      }
    }

    & .theme-theme .el-menu .is-active.submenu-title-noDropdown {
      background-color: var(--color-primary-deep80) !important;
      color: #fff !important;
    }

    & .theme-theme .is-active > .el-submenu__title {
      color: #fff !important;
    }

    & .theme-theme .nest-menu .el-submenu > .el-submenu__title,
    & .theme-theme .el-submenu .el-menu-item {
      background-color: var(--color-primary-deep50) !important;

      &:hover {
        background-color: var(--color-primary-deep80) !important;
        color: #fff !important;
      }
    }

    & .theme-theme .el-menu .is-opened .nest-menu .is-active {
      background-color: var(--color-primary-deep80) !important;
      color: #fff !important;
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: 54px !important;
    }

    .main-container {
      margin-left: 54px;
    }

    .submenu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;

        .svg-icon {
          margin-left: 10px;
        }
      }
    }

    .el-submenu {
      overflow: hidden;

      & > .el-submenu__title {
        padding: 0 !important;

        .svg-icon {
          margin-left: 10px;
        }
      }
    }

    .el-menu--collapse {
      .el-submenu {
        & > .el-submenu__title {
          & > span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-submenu {
    min-width: $base-sidebar-width !important;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      transition: transform 0.28s;
      width: $base-sidebar-width !important;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$base-sidebar-width, 0, 0);
      }
    }
  }

  .withoutAnimation {
    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  & > .el-menu {
    .svg-icon {
      margin-right: 16px;
    }
  }

  .nest-menu .el-submenu > .el-submenu__title,
  .el-menu-item {
    &:hover {
      // you can use $subMenuHover
      background-color: rgba(0, 0, 0, 0.06) !important;
    }
  }

  // the scroll bar appears when the subMenu is too long
  > .el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}

// sidebarCustom组件样式
#app .right-container .el-menu-demo .el-submenu > .el-submenu__title,
#app .hideSidebar .right-container .submenu-title-noDropdown {
  padding: 0 20px !important;
}
// 自定义主题-左侧菜单demo
.right-container .el-menu-demo {
  width: 240px;
  box-shadow: 1px 2px 2px 3px #e6e6e6;
  .el-menu-item,
  .el-submenu__title {
    height: 40px !important;
    line-height: 40px !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
  }
  .el-menu-item [class^="el-icon-"],
  .el-submenu [class^="el-icon-"] {
    margin-right: 10px;
  }
  .el-submenu .el-menu-item {
    padding: 0 55px !important;
  }
}

.sidebar-custom-container {
  // theme-custom
  .theme-custom .el-submenu__title i {
    color: var(--custom-menu-color) !important;
  }
  .theme-custom .el-menu-item,
  .theme-custom .el-submenu__title {
    &:hover {
      background-color: var(--custom-sub-menu-hover) !important;
      color: var(--custom-menu-color) !important;
    }
  }
  .theme-custom .el-menu .submenu-title-noDropdown .icon {
    color: var(--custom-menu-color) !important;
  }
  .theme-custom .el-menu .is-active.submenu-title-noDropdown {
    background-color: var(--custom-sub-menu-background-active) !important;
    color: var(--custom-menu-color-active) !important;
    .icon {
      color: var(--custom-menu-color-active) !important;
    }
  }

  .theme-custom .is-active > .el-submenu__title {
    color: var(--custom-menu-color) !important;
  }

  .theme-custom .nest-menu .el-submenu > .el-submenu__title,
  .theme-custom .el-submenu .el-menu-item {
    background-color: var(--custom-sub-menu-background) !important;

    &:hover {
      background-color: var(--custom-sub-menu-hover) !important;
      color: var(--custom-menu-color) !important;
    }
  }

  .theme-custom .el-menu .is-opened .is-active {
    background-color: var(--custom-sub-menu-background-active) !important;
    color: var(--custom-menu-color-active) !important;
  }
  // theme-theme
  & .theme-theme .el-submenu__title i {
    color: $base-menu-color;
  }
  .theme-theme .el-menu-item,
  .theme-theme .el-submenu__title {
    &:hover {
      background-color: var(--color-primary-deep80) !important;
      color: #fff !important;
    }
  }
  .theme-theme .el-menu .submenu-title-noDropdown .icon {
    color: #fff !important;
  }
  .theme-theme .el-menu .is-active.submenu-title-noDropdown {
    background-color: var(--color-primary-deep80) !important;
    color: #fff !important;
    .icon {
      color: var(--custom-menu-color-active) !important;
    }
  }

  .theme-theme .is-active > .el-submenu__title {
    color: #fff !important;
  }

  .theme-theme .nest-menu .el-submenu > .el-submenu__title,
  .theme-theme .el-submenu .el-menu-item {
    background-color: var(--color-primary-deep50) !important;

    &:hover {
      background-color: var(--color-primary-deep80) !important;
      color: #fff !important;
    }
  }

  .theme-theme .el-menu .is-opened .is-active {
    background-color: var(--color-primary-deep80) !important;
    color: #fff !important;
  }
  // theme-light
  & .theme-light .el-submenu__title i {
    color: $base-menu-light-color;
  }
  .theme-light .el-menu-item,
  .theme-light .el-submenu__title {
    &:hover {
      background-color: var(--color-primary29) !important;
      color: #000 !important;
    }
  }
  .theme-light .el-menu .submenu-title-noDropdown .icon {
    color: #000 !important;
  }
  .theme-light .el-menu .is-active.submenu-title-noDropdown {
    background-color: var(--color-primary) !important;
    color: #fff !important;
    .icon {
      color: var(--custom-menu-color-active) !important;
    }
  }

  .theme-light .is-active > .el-submenu__title {
    color: #000 !important;
  }

  .theme-light .nest-menu .el-submenu > .el-submenu__title,
  .theme-light .el-submenu .el-menu-item {
    background-color: var(--color-primary19) !important;

    &:hover {
      background-color: var(--color-primary29) !important;
      color: #000 !important;
    }
  }
  .theme-light .el-menu .is-opened .is-active {
    background-color: var(--color-primary) !important;
    color: #fff !important;
  }
  // theme-dark
  & .theme-dark .el-submenu__title i {
    color: $base-menu-color;
  }
  .theme-dark .el-menu-item,
  .theme-dark .el-submenu__title {
    &:hover {
      background-color: rgba(0, 0, 0, 0.3) !important;
      color: #fff !important;
    }
  }
  .theme-dark .el-menu .submenu-title-noDropdown .icon {
    color: #fff !important;
  }
  .theme-dark .el-menu .is-active.submenu-title-noDropdown {
    background-color: var(--color-primary) !important;
    color: #fff !important;
    .icon {
      color: var(--custom-menu-color-active) !important;
    }
  }

  .theme-dark .is-active > .el-submenu__title {
    color: #fff !important;
  }

  .theme-dark .nest-menu .el-submenu > .el-submenu__title,
  .theme-dark .el-submenu .el-menu-item {
    background-color: #000 !important;

    &:hover {
      background-color: rgba(0, 0, 0, 0.3) !important;
      color: #fff !important;
    }
  }

  .theme-dark .el-menu .is-opened .is-active {
    background-color: var(--color-primary) !important;
    color: #fff !important;
  }
}
