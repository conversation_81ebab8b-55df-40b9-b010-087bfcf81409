// cover some element-ui styles
.el-tag {
  background: var(--color-primary09) !important;
  border-color: var(--color-primary39) !important;
}
.el-tag.el-tag--danger {
  background-color: #fef0f0 !important;
  border-color: #fde2e2 !important;
}
.el-tag.el-tag--warning {
  background-color: #fdf6ec !important;
  border-color: #faecd8 !important;
}
.el-tag.el-tag--info {
  background-color: #f4f4f5 !important;
  border-color: #e9e9eb !important;
}
.el-tag.el-tag--success {
  background-color: #f0f9eb !important;
  border-color: #e1f3d8 !important;
}

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.el-upload__tip {
  line-height: 12px;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse
  > div
  > .el-submenu
  > .el-submenu__title
  .el-submenu__icon-arrow {
  display: none;
}

.el-select-dropdown {
  max-width: 400px;
}

.el-tooltip__popper {
  max-width: 400px;
}
