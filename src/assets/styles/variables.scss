// base color
$light-blue: #a6c4ff;
$red: #ea4040;
// $light-red: #fed4d4;
$light-red: #f8adad;
$green: #40b450;
$light-green: #d9f0dc;
$cyan: #20b2aa;
$light-cyan: #48d1cc;
$white: #ffffff;
$warning: #ffa514;
$light-warning: #fbf3e5;
$info: #909399;
$success: #67c23a;
$light-success: #f0f9eb;

// 默认菜单主题风格
$base-menu-color: #fff;
$base-menu-color-active: #fff;
$base-menu-background: #1c2439;
$base-logo-title-color: #ffffff;

$base-menu-light-color: rgba(0, 0, 0, 0.7);
$base-menu-light-background: #ffffff;
$base-logo-light-title-color: #001529;

$base-sub-menu-background: #000000;
// $base-sub-menu-background:#1C6CDD;
// $base-sub-menu-hover:#001528;
$base-sub-menu-hover: rgba(28, 108, 221, 0.5);
$base-menu-background-active: #1c6cdd;

// 自定义暗色菜单风格
/**
$base-menu-color:hsla(0,0%,100%,.65);
$base-menu-color-active:#fff;
$base-menu-background:#001529;
$base-logo-title-color: #ffffff;

$base-menu-light-color:rgba(0,0,0,.70);
$base-menu-light-background:#ffffff;
$base-logo-light-title-color: #001529;

$base-sub-menu-background:#000c17;
$base-sub-menu-hover:#001528;
*/

// 自定义风格.theme-custom
:root {
  // light
  --custom-menu-color: #000;
  --custom-menu-background: #fff;
  --custom-menu-color-active: #fff;
  --custom-sub-menu-background-active: #1c6cdd;
  --custom-sub-menu-background: #fff;
  --custom-sub-menu-hover: #1c6cdd19;
  --custom-logo-title-color: #ffffff;
  --custom-top-svg: #231815;
  --custom-top-bg: #fff;
  // dark
  // --custom-menu-color: #fff;
  // --custom-menu-background: #1C2439;
  // --custom-menu-color-active: #fff;
  // --custom-sub-menu-background-active: #1C6CDD;
  // --custom-sub-menu-background: #000000;
  // --custom-sub-menu-hover: rgba(28, 108, 221, 0.5);
  // --custom-logo-title-color: #ffffff;
  // --custom-top-svg: #231815;
  // --custom-top-bg: #fff;

  --color-primary: #1c6cdd;
  --color-primary09: #1c6cdd09;
  --color-primary19: #1c6cdd19;
  --color-primary29: #1c6cdd29;
  --color-primary39: #1c6cdd39;
  --color-primary49: #1c6cdd49;
  --color-primary59: #1c6cdd59;
  --color-primary69: #1c6cdd69;
  --color-primary79: #1c6cdd79;
  --color-primary89: #1c6cdd89;
  --color-primary99: #1c6cdd99;
  --color-primary-deep80: "#000";
  --color-primary-deep50: "#000";
  --color-success: #40b450;
  --color-warning: #ffba00;
  --color-danger: #ea4040;
  --color-info: rgba(0, 0, 0, 0.45);
  --color-info25: rgba(0, 0, 0, 0.25);
  --bg-color: rgba(0, 0, 0, 0.06);
  --moon-text-color: #c9d1d9;
}

$base-sidebar-width: 224px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuColor: $base-menu-color;
  menuLightColor: $base-menu-light-color;
  menuColorActive: $base-menu-color-active;
  menuBackground: $base-menu-background;
  menuLightBackground: $base-menu-light-background;
  subMenuBackground: $base-sub-menu-background;
  subMenuHover: $base-sub-menu-hover;
  sideBarWidth: $base-sidebar-width;
  logoTitleColor: $base-logo-title-color;
  logoLightTitleColor: $base-logo-light-title-color;

  // 自定义主题样式
  menuColorCustom: var(--custom-menu-color);
  menuBackgroundCustom: var(--custom-menu-background);
}
