//密码工具箱表单
.encry-tool-container {
  display: flex;
  justify-content: center;
  padding-top: 20px;
  .encry-form {
    width: 800px;
    .encry-result-area {
      margin-top: 40px;
      .encry-result-area-title {
        height: 18px;
        line-height: 18px;
        font-size: 16px;
        margin-bottom: 20px;
        border-left: 5px solid var(--color-primary);
        padding-left: 8px;
      }
      .el-form-item {
        margin-bottom: 5px;
        .el-form-item__label {
          color: #999;
        }
      }
    }
  }
}
.input-number-left {
  .el-input__inner {
    text-align: left;
  }
}
//prism 代码高亮
.code-toolbar {
  font-size: 13px;
}
.demo-container {
  background: #fff;
  padding: 15px;
  overflow: auto;
}

.demo-item-title {
  padding: 5px 10px 5px 10px;
  border-left: 4px solid #1c6cdd;
  margin-bottom: 10px;
  background: #fff;
}
.demo-item-body {
  padding: 20px;
  border: 1px solid #eee;
  border-radius: 5px;
  background: #ccc;
}

.s-detail .el-card .el-card__header .actions-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .title {
    font-size: 16px;
    font-weight: 700;
  }
  .el-button {
    padding: 0px;
  }
}

.form-flex {
  display: flex;
  .el-input {
    flex: 1;
  }
  .el-button.el-button--text {
    padding: 7px 0px;
  }
}

.form-dialog {
  .el-dialog__body {
    max-height: 700px;
    overflow-y: auto;
  }
}
.el-form.el-form--label-top {
  .el-form-item {
    margin-bottom: 20px;
    .el-form-item__label {
      line-height: 16px;
      padding-bottom: 8px;
    }
  }
}

// 仪表盘分页样式
.panel-page-container {
  position: relative;
  .page-num-box {
    position: absolute;
    top: -32px;
    right: 16px;
    display: flex;
    align-items: center;
    .el-button {
      padding: 0px;
      width: 14px;
      height: 14px;
      color: #595959;
    }
    .el-button.is-disabled {
      color: #bfbfbf;
    }
    .num {
      padding: 0px 8px;
      font-size: 14px;
      line-height: 14px;
      color: #595959;
      span {
        color: #bfbfbf;
      }
    }
  }
}

// form top
.form-top-content {
  .el-form--label-top .el-form-item__label {
    padding: 0px 0px 8px 0px;
    line-height: 14px;
  }
  .el-form-item {
    margin-bottom: 20px;
  }
}
.form-top-content + .form-footer-container {
  height: 50px;
  margin-top: 40px;
  display: flex;
  justify-content: center;
}

.capability-item {
  display: inline-block;
  border: 1px solid #1864ff;
  padding: 0px 8px;
  border-radius: 50px;
  color: #1864ff;
  font-size: 12px;
  margin-right: 8px;
}

// 基本信息操作
.base-info ~ .el-descriptions {
  color: #eee;
  .el-descriptions-item__label:not(.is-bordered-label) {
    flex-shrink: 0;
  }
  .el-descriptions-item__container
    .el-descriptions-item__content:not(.not-point) {
    display: inline-block;
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

// 下拉框样式
.search-item-popper-class {
  .el-select-dropdown__item {
    div {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}

.options-class {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

// 表单中带有问号提示
.form-item-tips {
  position: relative;
  .el-tooltip {
    position: absolute;
    right: -20px;
    top: 10px;
  }
}

// 列表\树结构
.list-tree-left-box {
  border-right: 1px solid #e8e8e8;
  .header-box {
    padding: 10px 16px;
    // border-bottom: 1px solid #ebeef5;
    // background: #f5f7fa;
    display: flex;
    align-items: center;
    justify-content: space-between;
    h4 {
      color: #222222;
      margin: 0px;
      font-size: 14px;
    }
  }
  .search-input {
    padding: 10px 16px;
    .el-input__inner {
      height: 32px;
      line-height: 32px;
      // border-radius: 20px;
    }
    .el-input__icon {
      line-height: 32px;
    }
  }
  ul {
    height: calc(100% - 89px);
    overflow: auto;
    margin: 0px;
    margin-left: 16px;
    padding-left: 0px;
    li {
      cursor: pointer;
      padding: 0px 16px;
      list-style: none;
      line-height: 32px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      &:hover {
        background: rgba(0, 0, 0, 0.04);
      }
    }
    .is-active {
      background: var(--color-primary19);
      color: var(--color-primary);
      &:hover {
        background: var(--color-primary19);
      }
    }
  }
}

// 表格下使用pageHeader
.page-header-column {
  .iconfont {
    font-size: 12px;
    cursor: pointer;
  }
}

.flex-form-box {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  .el-form-item {
    width: calc(50% - 16px);
  }
}
