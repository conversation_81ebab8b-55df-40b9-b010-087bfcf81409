import SearchTableContent from "./searchTableContent/index";
import SearchListContent from "./searchListContent/index";
import SearchItem from "./searchItem/index";
import commonHeader from "./commonHeader/index";
import contentDetail from "./contentDetail/index";
import Operatebtns from "./operatebtns/index";
import actionBtns from "./actionBtns/index";
import SwFormItem from "./SwFormItem/index";
import DictTag from "./DictTag/index";
import basicTable from "./basicTable/index";
import dialogForm from "./dialogForm/index";
import IpInput from "./IpInput/index";
import PaginationSelect from "./PaginationSelect/index";
import virtualList from "vue-virtual-scroll-list";
import pageHeader from "./pageHeader/index";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import vueEcharts from "./vueEcharts/ECharts.vue";
import noData from "./vueEcharts/noData.vue";
import SwNodata from "./SwNodata/index";
import SwNodataIcon from "./SwNodata/index1";
import baseTitle from "./baseTitle/index";
import alertTips from "./baseTips/alert.vue";
import textTips from "./baseTips/text.vue";
import smartSearchTableContent from "./smartSearchTableContent/index";
import batchActionBtns from "./batchActionBtns/index";
import dictSelect from "./dictSelect/index";
import menuFormContent from "./menuFormContent/index";
import singleFileUpload from "./singleFileUpload/index";
import multipleFileUpload from "./multipleFileUpload/index";
import addBtnInput from "./addBtnInput/index";
const install = (Vue) => {
  Vue.component("search-table-content", SearchTableContent);
  Vue.component("search-list-content", SearchListContent);
  Vue.component("search-item", SearchItem);
  Vue.component("common-header", commonHeader);
  Vue.component("content-detail", contentDetail);
  Vue.component("Operatebtns", Operatebtns);
  Vue.component("action-btns", actionBtns);
  Vue.component("sw-form-item", SwFormItem);
  Vue.component("sw-nodata", SwNodata);
  Vue.component("sw-nodata-icon", SwNodataIcon);
  Vue.component("dict-tag", DictTag);
  Vue.component("basic-table", basicTable);
  Vue.component("dialog-form", dialogForm);
  Vue.component("treeselect", Treeselect);
  Vue.component("ip-input", IpInput);
  Vue.component("pagination-select", PaginationSelect);
  Vue.component("virtual-list", virtualList);
  Vue.component("page-header", pageHeader);
  Vue.component("vue-echarts", vueEcharts);
  Vue.component("no-data", noData);
  Vue.component("base-title", baseTitle);
  Vue.component("alert-tips", alertTips);
  Vue.component("text-tips", textTips);
  Vue.component("smart-search-table-content", smartSearchTableContent);
  Vue.component("batch-action-btns", batchActionBtns);
  Vue.component("dict-select", dictSelect);
  Vue.component("menu-form-content", menuFormContent);
  Vue.component("single-file-upload", singleFileUpload);
  Vue.component("multiple-file-upload", multipleFileUpload);
  Vue.component("add-btn-input", addBtnInput);
};
export default install;
