<template>
  <el-dialog
    title="高级筛选"
    :visible="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    :append-to-body="true"
    @open="onOpenDig"
    :before-close="onCloseClick"
    class="advance-search-dialog"
  >
    <div v-loading="loading">
      <base-title title="设置筛选"></base-title>
      <div class="advance-search-content">
        <el-form
          :model="form"
          :rules="rules"
          size="small"
          ref="formRef"
          label-position="right"
          label-width="auto"
        >
          <div
            class="group-item"
            v-for="(group, groupIndex) in form.searchGroups"
            :key="group.groupId"
          >
            <div v-if="group.columns.length > 0" class="group-item-content">
              <div
                class="delete-column-btn text-danger"
                @click="onDeleteGroupClick(groupIndex)"
              >
                <i class="iconfont icon-delete-tag-left"></i>
              </div>
              <div class="content-box">
                <el-row
                  v-for="(item, index) in group.columns"
                  :key="item.colId"
                  :gutter="10"
                  class="column-box"
                >
                  <el-col :span="5">
                    <sw-form-item
                      :prop="`searchGroups.${groupIndex}.columns.${index}.colName`"
                      :key="`searchGroups.${group.groupId}.columns.${item.colId}.colName`"
                      :rules="rules.colName"
                    >
                      <el-select
                        v-model="item.colName"
                        :placeholder="$t('placeholder.select')"
                        clearable
                      >
                        <el-option
                          v-for="dict in colNameList"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        ></el-option>
                      </el-select>
                    </sw-form-item>
                  </el-col>
                  <el-col :span="4">
                    <sw-form-item
                      :prop="`searchGroups.${groupIndex}.columns.${index}.comparator`"
                      :key="`searchGroups.${group.groupId}.columns.${item.colId}.comparator`"
                      :rules="rules.comparator"
                    >
                      <el-select
                        v-model="item.comparator"
                        :placeholder="$t('placeholder.select')"
                        clearable
                      >
                        <el-option
                          v-for="dict in comparatorList"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        ></el-option>
                      </el-select>
                    </sw-form-item>
                  </el-col>
                  <el-col :span="11">
                    <sw-form-item
                      :prop="`searchGroups.${groupIndex}.columns.${index}.value`"
                      :key="`searchGroups.${group.groupId}.columns.${item.colId}.value`"
                      :rules="rules.value"
                    >
                      <el-input
                        v-model="item.value"
                        placeholder="请输入"
                        clearable
                      >
                      </el-input>
                    </sw-form-item>
                  </el-col>
                  <el-col :span="3">
                    <sw-form-item
                      :prop="`searchGroups.${groupIndex}.columns.${index}.colCondition`"
                      :key="`searchGroups.${group.groupId}.columns.${item.colId}.colCondition`"
                      :rules="rules.colCondition"
                      :style="{
                        visibility:
                          index < group.columns.length - 1
                            ? 'visible'
                            : 'hidden',
                      }"
                    >
                      <el-select
                        v-model="item.colCondition"
                        :placeholder="$t('placeholder.select')"
                      >
                        <el-option
                          v-for="dict in colConditionList"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        ></el-option>
                      </el-select>
                    </sw-form-item>
                  </el-col>
                  <el-col :span="1">
                    <span class="oper-icon-btn-box">
                      <i
                        class="iconfont icon-delete-tag fail-color"
                        @click="onDeleteClick(group, index)"
                        v-if="group.columns.length > 1"
                        :title="$t('common.delete')"
                      ></i>
                    </span>
                  </el-col>
                </el-row>
                <el-row class="column-box add-column-btn" :gutter="10">
                  <el-col>
                    <el-button
                      type="text"
                      style="height: 32px; padding: 0; line-height: 32px"
                      @click="onAddClick(group)"
                    >
                      <i class="iconfont icon-add-tag"></i>
                    </el-button>
                  </el-col>
                </el-row>
              </div>
            </div>

            <div
              class="group-item-condition"
              v-if="groupIndex < form.searchGroups.length - 1"
            >
              <span class="gap-line"></span>
              <sw-form-item
                :prop="`searchGroups.${groupIndex}.rowCondition`"
                :key="`searchGroups.${group.groupId}.rowCondition`"
                :rules="rules.rowCondition"
                style="margin-bottom: 0; width: 100px"
              >
                <el-select
                  v-model="group.rowCondition"
                  :placeholder="$t('placeholder.select')"
                >
                  <el-option
                    v-for="dict in colConditionList"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </sw-form-item>
              <span class="gap-line"></span>
            </div>
          </div>
          <div>
            <el-button type="text" @click="OnAddGroupClick">
              <i class="iconfont icon-add-tag"></i>
              添加组合条件</el-button
            >
          </div>
        </el-form>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="onCloseClick" class="oper-btn cancel-btn">{{
        $t("common.cancel")
      }}</el-button>
      <el-button
        type="primary"
        class="oper-btn"
        @click="onSubmitClick"
        :disabled="loading"
        >{{ $t("common.submit") }}</el-button
      >
    </div>
  </el-dialog>
</template>
<script>
import { getUuid } from "@/utils/util";
let defaultOption = {
  searchGroups: [
    {
      groupId: "1",
      rowCondition: "and",
      columns: [
        {
          colId: "1",
          colName: "字段",
          comparator: "包含",
          value: "已批准",
          colCondition: "and",
        },
        {
          colId: "2",
          colName: "字段",
          comparator: "区间",
          value: "2024-06-01 16:41:02,2025-06-27 16:41:14",
          colCondition: "",
        },
      ],
    },
    {
      groupId: "2",
      columns: [
        {
          colId: "3",
          colName: "字段",
          comparator: "包含",
          value: "已批准",
          colCondition: "and",
        },
        {
          colId: "4",
          colName: "字段",
          comparator: "区间",
          value: "2024-06-01 16:41:02,2025-06-27 16:41:14",
          colCondition: "",
        },
      ],
    },
  ],
};
let colOption = {
  colName: "",
  comparator: "",
  value: "",
  colCondition: "",
};
let groupOption = {
  rowCondition: "and",
  columns: [
    {
      colName: "",
      comparator: "",
      value: "",
      colCondition: "",
    },
  ],
};
import {} from "@/utils/validate";
import { cloneDeep } from "lodash";
export default {
  name: "advanceSearchDialog",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    rowObj: {
      type: Object,
      default: () => {
        return {};
      },
    },
    // 是否高级筛选 若为false，需清空高级筛选参数
    isAdvance: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      form: {},
      rules: {
        colName: [{ required: true, message: "请选择", trigger: "change" }],
        comparator: [{ required: true, message: "请选择", trigger: "change" }],
        value: [{ required: true, message: "请输入", trigger: "blur" }],
        colCondition: [
          //   { required: true, message: "请选择", trigger: "change" },
        ],
        rowCondition: [
          { required: true, message: "请选择", trigger: "change" },
        ],
      },
      colNameList: [],
      comparatorList: [
        { value: "包含", label: "包含" },
        { value: "区间", label: "区间" },
        { value: "不包含", label: "不包含" },
        { value: "等于", label: "等于" },
        { value: "无", label: "无" },
      ],
      colConditionList: [
        { value: "and", label: "且" },
        { value: "or", label: "或" },
      ],
    };
  },
  computed: {},
  watch: {
    isAdvance: {
      immediate: true,
      handler(val) {
        if (!val) {
          this.clearData();
        }
      },
    },
  },
  methods: {
    onOpenDig() {
      this.form = cloneDeep(defaultOption);
      this.getColNameList();
      this.setData();
    },
    setData() {
      this.form = {
        ...cloneDeep(defaultOption),
        ...cloneDeep(this.rowObj),
      };
      if (this.form.searchGroups.length === 0) {
        this.OnAddGroupClick();
      }
    },
    clearData() {
      this.$refs.formRef?.resetFields();
      this.form = cloneDeep(defaultOption);
    },
    getColNameList() {
      this.colNameList = [
        { value: "name", label: "客户名称", type: "text" },
        { value: "code", label: "客户编号", type: "text" },
        { value: "industry", label: "所属行业", type: "select" },
        { value: "registerDate", label: "注册日期", type: "date" },
        { value: "revenue", label: "年营业额", type: "number" },
        { value: "status", label: "客户状态", type: "select" },
      ];
    },
    onDeleteClick(group, index) {
      group.columns.splice(index, 1);
      if (group.columns.length > 0) {
        group.columns[group.columns.length - 1].colCondition = "";
      }
    },
    onAddClick(group) {
      if (group.columns.length > 0) {
        group.columns[group.columns.length - 1].colCondition = "and";
      }
      group.columns.push({
        colId: getUuid(),
        ...cloneDeep(colOption),
      });
    },
    onDeleteGroupClick(index) {
      this.form.searchGroups.splice(index, 1);
      if (this.form.searchGroups.length > 0) {
        this.form.searchGroups[this.form.searchGroups.length - 1].rowCondition =
          "";
      }
    },
    OnAddGroupClick() {
      if (this.form.searchGroups.length > 0) {
        this.form.searchGroups[this.form.searchGroups.length - 1].rowCondition =
          "and";
      }
      this.form.searchGroups.push({
        groupId: getUuid(),
        ...cloneDeep(groupOption),
      });
    },

    onCloseClick() {
      //  this.clearData();
      this.$emit("eventClose");
    },
    onSubmitClick() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.$emit("eventSucc", this.form);
          this.$emit("eventClose");
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.advance-search-dialog {
  ::v-deep .el-dialog__body {
    max-height: 600px;
    overflow-y: auto;
  }
}
.advance-search-content {
  padding: 16px;
  border: 1px dashed rgba(0, 0, 0, 0.15);
  .iconfont {
    font-size: 14px;
  }
  .gap-line {
    flex: 1;
    height: 0px;
    border-bottom: 1px dashed rgba(0, 0, 0, 0.15);
  }
  .group-item {
    .group-item-content {
      position: relative;
      margin: 20px 0 20px 48px;
      &::before {
        position: absolute;
        left: -10px;
        top: 15px;
        content: "";
        display: inline-block;
        width: 0px;
        height: calc(100% - 32px);
        border-left: 2px solid #a5c1f7;
      }
      .delete-column-btn {
        position: absolute;
        left: -34px;
        top: 50%;
        transform: translateY(-50%);
        z-index: 99;
        cursor: pointer;
        &::before {
          position: absolute;
          right: -10px;
          top: 50%;
          transform: translateY(-50%);
          content: "";
          display: inline-block;
          width: 8px;
          height: 0px;
          border-bottom: 2px solid #a5c1f7;
        }
      }
      .content-box {
        .column-box {
          position: relative;
          &::before {
            position: absolute;
            left: -5px;
            top: 15px;
            content: "";
            display: inline-block;
            width: 10px;
            height: 0px;
            border-bottom: 2px solid #a5c1f7;
          }
        }
        .oper-icon-btn-box {
          display: inline-block;
          height: 32px;
          line-height: 32px;
          i {
            cursor: pointer;
          }
        }
      }
    }
    .group-item-condition {
      display: flex;
      align-items: center;
    }
  }
}
</style>
