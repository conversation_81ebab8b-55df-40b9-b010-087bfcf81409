<template>
  <el-form
    ref="searchFormRef"
    :model="dataForm"
    @submit.native.prevent
    @keydown.native="onKeydown"
    inline
    size="small"
    class="search-form"
    :rules="rules"
  >
    <el-container>
      <el-main ref="target">
        <slot></slot>
      </el-main>
      <el-aside :width="asideWidth">
        <div class="el-form-item el-form-item--small">
          <div style="display: flex">
            <el-button
              type="primary"
              class="search-btn"
              size="small"
              @click="$emit('query')"
              id="searchBtnId"
              >{{ $t("common.search") }}</el-button
            >
            <el-button
              size="small"
              class="search-btn cancel-btn mgl16"
              @click="$emit('reset')"
              v-if="showResetBtn && $slots.default"
              id="resetBtnId"
              >{{ $t("common.reset") }}</el-button
            >
            <el-button
              class="toggle-btn"
              v-show="showExpand"
              type="text"
              @click="$emit('expandChange', !expand)"
              id="expandBtnId"
            >
              <i class="el-icon-d-arrow-right" :class="{ expand: expand }"> </i>
            </el-button>
          </div>
        </div>
      </el-aside>
    </el-container>
  </el-form>
</template>

<script>
export default {
  name: "SearchForm",
  props: {
    labelWidth: {
      type: String,
      default: "70px",
    },
    expand: {
      type: Boolean,
      default: false,
    },
    showExpand: Boolean,
    showResetBtn: {
      type: Boolean,
      default: true,
    },
    rules: {
      type: Object,
      default: () => {},
    },
    model: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      dataForm: {},
    };
  },
  watch: {
    model: {
      handler(val) {
        this.dataForm = val;
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    asideWidth() {
      if (this.showResetBtn) {
        return "190px";
      }
      return "100px";
    },
  },
  methods: {
    getRowHeight() {
      return this.$refs.target.$el.clientHeight;
    },
    onKeydown(e) {
      // 禁用Tab键
      if (e.keyCode === 9) {
        e.preventDefault();
        return false;
      }
    },
    resetField() {
      this.$refs.searchFormRef.resetFields();
    },
  },
};
</script>
<style lang="scss" scoped>
.search-form .el-main {
  padding: 0;
  // overflow: hidden;
}
.search-form .el-main .form__row {
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.search-form .el-main .el-form-item--mini.el-form-item,
.search-form .el-main .el-form-item--small.el-form-item {
  margin-bottom: 16px;
}
.search-form .el-aside .el-form-item--mini.el-form-item,
.search-form .el-aside .el-form-item--small.el-form-item {
  padding: 0px;
  margin-bottom: 16px;
}
.search-form .el-aside {
  background-color: #fff;
  padding: 0px;
  margin-bottom: 0px;
  text-align: right;
  overflow: hidden;
}
.search-form .search-btn {
  width: 72px;
  height: 32px;
  padding: 0;
  line-height: 32px;
  font-size: 14px;
}
.search-form .toggle-btn {
  padding: 8px 0;
}
.search-form .toggle-btn i {
  font-size: 14px;
  -webkit-transition: -webkit-transform 0.3s ease;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
}
.search-form .toggle-btn i.expand {
  -webkit-transform: rotate(270deg);
  transform: rotate(270deg);
}
.search-form .search-form__content .el-form-item {
  margin-bottom: 16px;
}
.search-form .search-form__btn {
  text-align: center;
}
.search-form .search-form__btn .el-form-item__content {
  margin-left: 0 !important;
}
</style>
