<template>
  <el-dialog
    title="选择成员"
    :visible="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    :append-to-body="true"
    @open="onOpenDig"
    :before-close="onCloseClick"
    class="user-search-dialog"
  >
    <div class="content-box">
      <div class="left-content">
        <ul>
          <li
            v-for="item in typeList"
            :key="item.value"
            :class="{ active: activeName == item.value }"
            @click.stop="onTypeChange(item)"
          >
            {{ item.label }}
          </li>
        </ul>
      </div>
      <div class="center-content">
        <div class="center-search-box">
          <el-form
            ref="formRef"
            :model="form"
            @submit.native.prevent
            class="center-search-form"
          >
            <sw-form-item label="">
              <el-input
                v-model="form.label"
                prop="label"
                @keyup.enter.native="onSearchClick"
                placeholder="请输入"
              >
                <i slot="prefix" class="el-input__icon el-icon-search"></i>
              </el-input>
            </sw-form-item>
          </el-form>
        </div>
        <div class="center-search-tree" v-loading="treeLoading">
          <el-tree
            :data="treeData"
            show-checkbox
            default-expand-all
            node-key="id"
            ref="tree"
            highlight-current
            :props="defaultProps"
            @check="onTreeCheck"
          >
            <span
              class="center-search-tree-node"
              slot-scope="{ node, data }"
              :title="data.label"
              @click="onTreeNodeClick(data, node)"
            >
              <i
                class="node-icon iconfont"
                :class="
                  data.type == 'department'
                    ? 'icon-a-wenjianjia-dakai1'
                    : 'icon-a-solid-user'
                "
              ></i>
              <span>{{ data.label }}</span>
            </span>
          </el-tree>
        </div>
      </div>
      <div class="right-content">
        <div class="right-header">
          <span>已选（{{ selectedList.length }}）</span>
          <el-button type="text" @click="onClearClick"> 清空</el-button>
        </div>
        <div class="right-list">
          <ul>
            <li
              v-for="(item, index) in selectedList"
              :key="item.id"
              :title="item.label"
            >
              <i class="iconfont icon-a-solid-user"></i>
              <span class="right-user option-class">{{ item.label }}</span>
              <span class="close-btn" @click="onDeleteClick(item)">
                <i class="el-icon-error"></i>
              </span>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="onCloseClick" class="oper-btn cancel-btn">{{
        $t("common.cancel")
      }}</el-button>
      <el-button
        type="primary"
        class="oper-btn"
        @click="onSubmitClick"
        :disabled="loading"
        >{{ $t("common.submit") }}</el-button
      >
    </div>
  </el-dialog>
</template>
<script>
import { getUuid } from "@/utils/util";
// import { getTreeData } from "@/api/user";

let defaultOption = {};

import {} from "@/utils/validate";
import { cloneDeep } from "lodash";
export default {
  name: "userSearchDialog",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    rowObj: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      loading: false,
      treeLoading: false,
      activeName: "department",
      typeList: [
        // { label: "用户", value: "user" },
        { label: "部门", value: "department" },
      ],
      form: { label: "" },
      rules: {},
      colNameList: [],
      selectedList: [{ id: 21, label: "白连涛" }],
      treeData: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
    };
  },
  computed: {},
  mounted() {
    this.onOpenDig();
  },
  methods: {
    onOpenDig() {
      this.form = cloneDeep(defaultOption);
      this.$nextTick(() => {
        this.fetchTreeData();
      });
    },

    async fetchTreeData(params = {}) {
      this.treeLoading = true;
      try {
        await new Promise((resolve) => setTimeout(resolve, 500));
        const mockData = [
          {
            id: 1,
            label: "三未信安科技股份有限公司",
            children: [
              {
                id: 3,
                label: "张岳公",
                type: "user",
              },
              {
                id: 4,
                label: "高志权",
                type: "user",
              },
              {
                id: 5,
                label: "范希骏",
                type: "user",
              },
              {
                id: 2,
                label: "三未营销中心",
                type: "department",
                children: [
                  {
                    id: 21,
                    label: "白连涛",
                    type: "user",
                  },
                  {
                    id: 22,
                    label: "上海分公司",
                    type: "department",
                    children: [
                      {
                        id: 221,
                        label:
                          "曹顺利字段超长超长超长字段超长超长超长字段超长超长超长字段超长超长超长字段超长超长超长字段超长超长超长字段超长超长超长",
                        type: "user",
                      },
                      {
                        id: 222,
                        label: "曹顺利",
                        type: "user",
                      },
                      {
                        id: 223,
                        label: "曹顺利",
                        type: "user",
                      },
                      {
                        id: 224,
                        label: "曹顺利",
                        type: "user",
                      },
                      {
                        id: 225,
                        label: "曹顺利",
                        type: "user",
                      },
                      {
                        id: 226,
                        label: "曹顺利",
                        type: "user",
                      },
                      {
                        id: 227,
                        label: "曹顺利",
                        type: "user",
                      },
                      {
                        id: 228,
                        label: "曹顺利",
                        type: "user",
                      },
                    ],
                  },
                ],
              },
              {
                id: 6,
                label: "三未营销中心",
                type: "department",
                children: [
                  {
                    id: 61,
                    label: "白连涛",
                    type: "user",
                  },
                  {
                    id: 62,
                    label: "上海分公司",
                    type: "department",
                    children: [
                      {
                        id: 621,
                        label: "曹顺利",
                        type: "user",
                      },
                      {
                        id: 622,
                        label: "曹顺利",
                        type: "user",
                      },
                      {
                        id: 623,
                        label: "曹顺利",
                        type: "user",
                      },
                      {
                        id: 624,
                        label: "曹顺利",
                        type: "user",
                      },
                      {
                        id: 625,
                        label: "曹顺利",
                        type: "user",
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ];
        const mockData2 = [
          {
            id: 221,
            label:
              "曹顺利字段超长超长超长字段超长超长超长字段超长超长超长字段超长超长超长字段超长超长超长字段超长超长超长字段超长超长超长",
            type: "user",
          },
          {
            id: 222,
            label: "曹顺利",
            type: "user",
          },
        ];
        this.treeData = params.keyword ? mockData2 : mockData;
        this.$nextTick(() => {
          if (this.$refs.tree) {
            this.syncTreeChecked();
          }
        });
      } catch (error) {
      } finally {
        this.treeLoading = false;
      }
    },
    async onSearchClick() {
      const params = {
        keyword: this.form.label,
        type: this.activeName,
      };
      await this.fetchTreeData(params);
    },

    onTypeChange(val) {
      this.activeName = val.value;
      this.onSearchClick();
    },

    onCloseClick() {
      this.$emit("eventClose");
    },

    onSubmitClick() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.$emit("eventSucc", this.form);
          this.$emit("eventClose");
        } else {
          return false;
        }
      });
    },

    // 树节点选中状态改变时触发
    onTreeCheck(data, checkedInfo) {
      // 更新selectedList为当前选中的所有用户节点
      this.selectedList = checkedInfo.checkedNodes
        .filter((node) => node.type === "user")
        .map((node) => ({
          id: node.id,
          label: node.label,
        }));
    },

    // 同步selectedList到树的选中状态
    syncTreeChecked() {
      this.$refs.tree.setCheckedKeys([]);
      const selectedKeys = this.selectedList.map((item) => item.id);
      selectedKeys.forEach((key) => {
        this.$refs.tree.setChecked(key, true);
      });
    },
    // 根据ID查找节点
    getNodeById(nodes, id) {
      for (let node of nodes) {
        if (node.id === id) {
          return node;
        }
        if (node.children && node.children.length) {
          const found = this.getNodeById(node.children, id);
          if (found) return found;
        }
      }
      return null;
    },
    onClearClick() {
      this.selectedList = [];
      // 确保树组件存在
      if (this.$refs.tree) {
        this.$refs.tree.setCheckedKeys([]);
      }
    },

    onDeleteClick(item) {
      const index = this.selectedList.findIndex((i) => i.id === item.id);
      if (index > -1) {
        this.selectedList.splice(index, 1);
      }
      this.$refs.tree.setChecked(item.id, false);
      //   this.updateParentNodes();
    },

    // 更新父节点的半选状态
    updateParentNodes() {
      if (!this.$refs.tree) {
        return;
      }
      const checkedNodes = this.$refs.tree.getCheckedNodes(false);
      const halfCheckedNodes = this.$refs.tree.getHalfCheckedNodes();
      // 获取当前所有选中的用户
      const currentSelectedUsers = [
        ...checkedNodes.filter((node) => node.type === "user"),
        ...halfCheckedNodes.filter((node) => node.type === "user"),
      ];
      this.selectedList = currentSelectedUsers.map((node) => ({
        id: node.id,
        label: node.label,
      }));
    },

    // 点击树节点行时触发
    onTreeNodeClick(data, node) {
      const checkedKeys = this.$refs.tree.getCheckedKeys();
      const isChecked = checkedKeys.includes(data.id);
      this.$refs.tree.setChecked(data, !isChecked);
      // 更新selectedList
      const checkedNodes = this.$refs.tree.getCheckedNodes(false);
      const halfCheckedNodes = this.$refs.tree.getHalfCheckedNodes();
      this.selectedList = [...checkedNodes, ...halfCheckedNodes]
        .filter((node) => node.type === "user")
        .map((node) => ({
          id: node.id,
          label: node.label,
        }));
    },
  },
};
</script>
<style lang="scss" scoped>
.user-search-dialog {
  ::v-deep .el-dialog__body {
    padding: 0;
    // max-height: 600px;
    // overflow-y: auto;
  }

  .content-box {
    border-radius: 4px;
    position: relative;
    display: flex;
    height: 600px;
    ul,
    li {
      margin: 0;
      padding: 0;
      list-style: none;
    }

    .left-content {
      width: 120px;
      height: 100%;
      color: rgba(0, 0, 0, 0.65);
      font-size: 14px;
      padding-top: 15px;
      border-right: 1px solid rgba(0, 0, 0, 0.06);

      ul {
        position: fixed;
        //   top: 165px;
        overflow-y: auto;
        overflow-x: hidden;
        width: 120px;
      }

      li {
        padding: 0 16px;
        cursor: pointer;
        line-height: 40px;
        text-align: right;
      }

      li.active {
        color: var(--color-primary);
        font-weight: 500;
        background: var(--color-primary19);
        border-right: 2px solid var(--color-primary);
      }
    }
    .center-content {
      width: calc(100% - 360px);
      overflow-x: hidden;
      overflow-y: auto;
      .center-search-box {
        padding: 16px;
        .center-search-form {
          ::v-deep .el-form-item {
            margin-bottom: 0;
          }
        }
      }
      .center-search-tree {
        padding: 0 16px 20px;
        position: relative;
        .center-search-tree-node {
          display: flex;
          font-size: 14px;
          line-height: 18px;
          cursor: pointer;
          .node-icon {
            color: var(--color-primary);
            margin-right: 10px;
            font-size: 14px;
          }
        }

        .tree-loading {
          text-align: center;
          padding: 20px;
          color: #909399;
        }
      }
    }
    .right-content {
      width: 240px;
      overflow: hidden;
      border-left: 1px solid rgba(0, 0, 0, 0.06);
      .right-header {
        display: flex;
        justify-content: space-between;
        color: rgba(0, 0, 0, 0.45);
        font-size: 14px;
        height: 48px;
        line-height: 48px;
        padding: 0 16px;
      }
      .right-list {
        font-size: 14px;
        overflow-y: auto;
        color: rgba(0, 0, 0, 0.65);
        ul {
          padding: 0 10px;
          li {
            position: relative;
            display: flex;
            height: 30px;
            line-height: 30px;
            padding: 0 10px;
            &:hover {
              background: var(--color-primary19);
              .close-btn {
                display: block;
              }
            }
          }
          .icon-a-solid-user {
            font-size: 14px;
            color: var(--color-primary);
            margin-right: 10px;
          }
          .right-user {
            width: calc(100% - 40px);
          }
          .close-btn {
            cursor: pointer;
            position: absolute;
            top: 0px;
            right: 10px;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
            display: none;
          }
        }
      }
    }
  }
}
</style>
