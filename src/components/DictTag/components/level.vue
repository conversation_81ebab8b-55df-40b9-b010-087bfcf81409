<template>
  <div class="level-tag" :style="getStyle">
    <i :class="getIconClass" class="tag-icon"></i>
    <slot name="title"></slot>
    <slot name="content"></slot>
  </div>
</template>

<script>
export default {
  name: "DictTagCom",
  props: {
    options: {
      type: Array,
      default: null,
    },
    labelName: {
      type: String,
      default: "label",
    },
    valueName: {
      type: String,
      default: "value",
    },
    value: [Number, String, Array],
    styles: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  computed: {
    getIconClass() {
      let item = this.options.find((key) => key[this.valueName] == this.value);
      return item?.icon || "iconfont icon-level";
    },
    getStyle() {
      let item = this.options.find((key) => key[this.valueName] == this.value);
      let color = item?.color || "#909399";
      return {
        borderColor: color,
        backgroundColor: color + "10",
        color,
      };
    },
  },
};
</script>
<style scoped lang="scss">
.level-tag {
  display: inline-block;
  padding: 0 8px;
  // height: 20px;
  line-height: 20px;
  border-width: 1px;
  border-style: solid;
  border-radius: 4px;
  font-size: 12px;
  box-sizing: border-box;
  white-space: nowrap;
  font-weight: 400;
  .tag-icon {
    font-size: 12px;
  }
}
.level-tag + .level-tag {
  margin-left: 10px;
}
</style>
