<template>
  <span class="text-spot text-spot-class">
    <i class="spot-box" :class="spotClass"></i>
    <slot name="title"></slot>
    <slot name="content"></slot>
  </span>
</template>
<script>
export default {
  name: "textSpotCom",
  props: {
    value: [String, Number, Array],
    options: {
      type: Array,
      default: () => [],
    },
    valueName: {
      type: String,
    },
    styles: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    spotClass() {
      let item = this.options.find((key) => key[this.valueName] == this.value);
      if (item.listClass) {
        return item.listClass;
      }
      return this.styles[this.value].listClass;
    },
  },
};
</script>
<style lang="scss" scoped>
.text-spot {
  .spot-box {
    margin-bottom: 2px;
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 4px;
  }
}
</style>
