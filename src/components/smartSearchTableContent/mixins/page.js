import Operatebtns from "@/components/operatebtns/index.vue";
import { trimObjectValue } from "@/utils/util";
import { checkPermi } from "@/utils/permission";
import { getShowColumns } from "@/api/common";
import { getSysDicts } from "@/api/dict/index.js";

export default {
  components: {
    Operatebtns,
  },
  data() {
    return {
      tablePage: {
        total: 0,
        pageSize: 20,
        pageNum: 1,
      },
      loading: false,
      tableData: [],
      searchTableRef: "searchTableRef",
      selectList: [],
      initQuery: true,
      operateBtns: [],
      operbtnsLimit: 2,
      showEditDialog: false,
      isSelectTotal: false, // 全选 total
      batchLimit: 5,
      // 高级筛选参数
      advanceSearchForm: {
        searchGroups: [],
      },
      isAdvance: false,
      // 列筛选
      isFilterColumn: true,
      showColumnList: [],
    };
  },
  async beforeMount() {
    if (this.isFilterColumn) {
      await this.getShowColumns();
    }
    this.setColumnsMinWidth();
    this.filterHasRoleOperBtns(this.operbtnsLimit);
  },
  computed: {
    // 页面标识 用于筛选列名
    pageCode() {
      return this.$route.name;
    },
  },
  mounted() {
    if (this.initQuery) {
      this.query();
    }
  },
  methods: {
    async getShowColumns() {
      let res = await getShowColumns({ code: this.pageCode });
      this.showColumnList = res.data || [];
      if (this.showColumnList.length) {
        this.columns = this.columns.filter(
          (item) =>
            this.showColumnList.includes(item.prop) ||
            item.render == "columnOperate" ||
            item.render == "operation",
        );
      }
    },
    setColumnsMinWidth() {
      this.columns.forEach((item, idx) => {
        if (!(item.render == "columnOperate" || item.render == "operation")) {
          let width = Number(
            (item.width || item.minWidth || "").replace("px", ""),
          );
          let textWidth =
            this.getStringWidth(
              item.label,
              "bold 14px 'Montserrat', 'Helvetica Neue'",
            ) + 30;
          if (!width || width < textWidth) {
            delete item.width;
            item.minWidth = textWidth + "px";
          }
        }
      });
    },
    filterHasRoleOperBtns(limit) {
      //无操作列 无操作按钮
      if (!this.operateBtns || !this.columns) {
        return;
      }
      //过滤仅有权限按钮
      this.operateBtns =
        this.operateBtns?.filter((i) => {
          //有权限标识，则判断权限
          if (i.permi) {
            return checkPermi(i.permi);
          } else {
            return true;
          }
        }) || [];
      let operlen = this.operateBtns.length;
      if (operlen == 0) {
        // 无权限隐藏操作列
        this.columns.forEach((item, idx) => {
          if (item.render == "columnOperate" || item.render == "operation") {
            this.columns.splice(idx, 1);
          }
        });
        return;
      }
      let text = "";
      limit = limit || 999;
      this.operateBtns.forEach((item, idx) => {
        if (idx < limit) {
          text += item.name;
        }
        if (operlen == limit + 1 && idx == limit) {
          text += item.name;
        }
      });
      let width = this.getStringWidth(
        text,
        "14px 'Montserrat', 'Helvetica Neue', 'Microsoft YaHei'",
      );
      width += ((operlen > limit ? limit : operlen) - 1) * 10; //按钮间距
      width += 40; //td边距
      if (this.operateBtns.length > limit + 1) {
        width += 60;
      }
      if (width < 120) {
        width = 120;
      }
      // 更新操作列宽度
      this.columns.forEach((item, idx) => {
        if (item.render == "columnOperate" || item.render == "operation") {
          item.width = width + "px";
        }
      });
    },

    getStringWidth(str, font) {
      const div = document.createElement("div");
      div.innerText = str;
      div.style.font = font;
      div.style.display = "inline-block";
      div.style.visibility = "hidden";
      document.body.appendChild(div);
      const width = div.offsetWidth;
      document.body.removeChild(div);
      return width;
    },
    queryParams() {
      let params = {
        ...this.tablePage,
      };
      if (this.searchForm) {
        params = Object.assign(params, this.searchForm);
      }
      if (this.getExtraParams) {
        params = Object.assign(params, this.getExtraParams());
      }
      if (this.advanceSearchForm) {
        params = Object.assign(params, this.cloneDeep(this.advanceSearchForm));
      }
      return trimObjectValue(params);
    },
    async query() {
      try {
        this.loading = true;
        const res = await this.doQuery();
        this.tableData = res.data.list ? res.data.list : res.data;
        this.tablePage.total = res.data.total;

        if (this.tableLoadData) {
          this.tableLoadData();
        }
        if (this.tablePage.total !== 0 && this.tableData.length === 0) {
          if (this.tablePage.pageNum > 1) {
            this.tablePage.pageNum--;
            this.query();
          }
          return;
        }
      } catch {
      } finally {
        this.loading = false;
        // 表格回到顶部
        if (this.$refs[this.searchTableRef]) {
          this.$refs[
            this.searchTableRef
          ].getTableRef().bodyWrapper.scrollTop = 0;
          this.$refs[this.searchTableRef].getTableRef().doLayout();
        }
      }
    },
    doQuery() {
      return this.request(this.queryParams());
    },
    resetParamsQuery(flag = false) {
      if (flag) {
        this.tablePage.pageNum = 1;
      }
      this.query();
    },
    // 重置(只能重置普通筛选)
    reset() {
      this.$nextTick(() => {
        this.$refs[this.searchTableRef].resetField();
        this.resetParamsQuery(true);
        this.resetTableHeight();
      });
    },
    // 分页
    pagination(params) {
      Object.keys(params).forEach((item) => {
        this.tablePage[item] = params[item];
      });
      this.query();
    },
    // 按钮提示性操作
    confirmInfoOperFun(msg, request, params, succMsg) {
      this.confirmMessage(msg).then(() => {
        this.loading = true;
        request(params)
          .then((res) => {
            this.successMsg(succMsg);
            this.query();
          })
          .finally(() => {
            this.loading = false;
          });
      });
    },
    selectionChange(selection) {
      this.selectList = selection;
    },
    selectable(row) {
      return true;
    },
    closeEditDialog() {
      this.showEditDialog = false;
      this.$nextTick(() => {
        this.resetEditInfo();
      });
    },
    resetTableHeight() {
      this.$nextTick(() => {
        this.$refs[this.searchTableRef].resetHeight();
      });
    },
    // 选择所有total
    onSelectTotalClick() {
      this.isSelectTotal = !this.isSelectTotal;
      this.$nextTick(() => {
        const tableRef = this.$refs?.[this.searchTableRef]?.getTableRef();
        if (this.isSelectTotal) {
          this.tableData.forEach((row) => {
            tableRef.toggleRowSelection(row, true);
          });
          this.selectList = this.tableData;
          // TODO selectList只能保存当前页内容，选择所有数据如何传值？
          //
        } else {
          this.clearSelectList();
        }
      });
    },
    clearSelectList() {
      this.$nextTick(() => {
        const tableRef = this.$refs?.[this.searchTableRef]?.getTableRef();
        tableRef.clearSelection();
        this.selectList = [];
      });
    },
    // 清空高级筛选参数
    clearAdvanceSearch() {
      this.advanceSearchForm.searchGroups = [];
    },
    // 执行普通筛选
    queryBase() {
      this.isAdvance = false;
      this.advanceSearchForm.searchGroups = [];
      this.query();
    },
    // 执行高级筛选
    queryAdvance(data) {
      this.isAdvance = true;
      this.advanceSearchForm.searchGroups = data.searchGroups;
      this.reset();
    },
  },
};
