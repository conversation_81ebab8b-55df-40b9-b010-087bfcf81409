<template>
  <div class="content-box">
    <div class="left-content">
      <ul>
        <li
          v-for="item in filterTypeList"
          :key="item.value"
          :class="{ active: activeName == item.value }"
          @click.stop="onTypeChange(item)"
        >
          {{ item.label }}
        </li>
      </ul>
    </div>
    <div class="right-content">
      <slot name="rightContent">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="auto"
          size="small"
          label-position="right"
        >
          <div
            class="right-content-item"
            :id="item.value"
            v-for="item in filterTypeList"
            :key="item.value"
          >
            <base-title :title="item.label"></base-title>
            <div style="height: 300px">{{ item.label }}表单内容</div>
          </div>
        </el-form>
      </slot>
    </div>
  </div>
</template>
<script>
export default {
  name: "menuFormContent",
  props: {
    typeList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      isClickType: false,
      activeName: "",
      scrollDom: null,
      form: {},
      rules: {},
    };
  },
  computed: {
    filterTypeList() {
      return this.typeList.filter((item) => {
        if (item.hidden) {
          return !item.hidden();
        }
        return true;
      });
    },
  },
  created() {
    if (this.filterTypeList.length > 0) {
      this.activeName = this.filterTypeList[0].value;
    }
  },
  mounted() {
    this.scrollDom = document.querySelector("#scrollAppMain");
    this.scrollDom.addEventListener("scroll", this.handleScroll);
  },
  beforeDestroy() {
    this.scrollDom.removeEventListener("scroll", this.handleScroll);
  },
  methods: {
    onTypeChange(item) {
      this.isClickType = true;
      this.activeName = item.value;
      //   let dom = this.$refs[`${item.value}`];
      let dom = document.querySelector(`#${item.value}`);
      if (dom) {
        dom.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    },
    handleScroll() {
      if (this.isClickType) {
        clearTimeout(this.scrollTimer);
        this.scrollTimer = setTimeout(() => {
          this.isClickType = false;
        }, 100);
        return;
      }
      const scrollTop = this.scrollDom.scrollTop;
      let currentActive = "";

      for (let i = this.filterTypeList.length - 1; i >= 0; i--) {
        // const section = this.$refs[this.filterTypeList[i].value];
        const section = document.querySelector(
          `#${this.filterTypeList[i].value}`,
        );
        if (section && section.offsetTop <= scrollTop + 50) {
          // 50px的偏移量用于提前触发
          currentActive = this.filterTypeList[i].value;
          break;
        }
      }

      if (currentActive && currentActive !== this.activeName) {
        this.activeName = currentActive;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.content-box {
  background: #fff;
  border-radius: 4px;
  position: relative;
  display: flex;
  min-height: 200px;
  ul,
  li {
    margin: 0;
    padding: 0;
    list-style: none;
  }

  .left-content {
    width: 200px;
    height: 100%;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    padding-top: 15px;

    ul {
      position: fixed;
      //   top: 165px;
      overflow-y: auto;
      overflow-x: hidden;
      max-height: calc(100% - 215px); // 滚动 165+50 footer
      width: 201px;
    }

    li {
      padding: 0 16px;
      cursor: pointer;
      line-height: 40px;
      text-align: right;
    }

    li.active {
      color: var(--color-primary);
      font-weight: 500;
      background: var(--color-primary19);
      border-right: 2px solid var(--color-primary);
    }
  }

  ::v-deep .right-content {
    flex: 1;
    border-left: 1px solid #ddd;
    padding: 16px 120px 50px 20px;
    width: calc(100% - 200px);
    .right-content-item {
      padding: 10px 0;
    }
  }
}
</style>
