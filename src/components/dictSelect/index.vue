<template>
  <div class="dict-select-container">
    <el-select
      v-bind="$attrs"
      :value="currentValue"
      v-on="inputListeners"
      @change="handleChange"
      ref="dictSelectRef"
    >
      <slot>
        <el-option
          v-for="(item, index) in dictOptions"
          :key="item.value + index"
          :label="item.label"
          :value="item.value"
          :disabled="item.disabled"
        >
          <span :style="{ color: item.color }">{{ item.label }}</span>
        </el-option>
      </slot>
    </el-select>
    <div
      class="config-btn"
      @click="onDictListClick"
      :class="{ disabled: !dictCode || (hasParent && !parentData.dictCode) }"
    >
      <i class="el-icon-setting"></i>
    </div>
    <dictSelectDialog
      :dialogVisible="dictDialog.dialogVisible"
      :dictCode="dictCode"
      :dictName="dictName"
      :hasParent="hasParent"
      :parentData="parentData"
      :rowObj="dictDialog.rowObj"
      @eventSucc="$emit('queryDictList')"
      @eventClose="eventClose(dictDialog)"
      @onAddClick="onAddClick"
    ></dictSelectDialog>
    <addDictDialog
      :dialogVisible="addDialog.dialogVisible"
      :dictCode="dictCode"
      :dictName="dictName"
      :hasParent="hasParent"
      :parentData="parentData"
      :rowObj="addDialog.rowObj"
      @eventSucc="onDictListClick"
      @eventClose="eventClose(addDialog)"
    ></addDictDialog>
  </div>
</template>

<script>
import dictSelectDialog from "../dictSelectDialog/index.vue";
import addDictDialog from "../dictSelectDialog/addDict.vue";
export default {
  name: "DictSelect",
  components: { dictSelectDialog, addDictDialog },
  props: {
    value: {
      type: [String, Number, Boolean, Array],
      default: "",
    },
    options: {
      type: Array,
      default: () => [],
    },
    // 字典标识
    dictCode: {
      type: String,
      default: "",
    },
    dictName: {
      type: String,
      default: "",
    },
    // 是否有父字典
    hasParent: {
      type: Boolean,
      default: false,
    },
    parentData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      dictOptions: [],
      currentValue: this.value,
      dictDialog: {
        dialogVisible: false,
        rowObj: {},
      },
      addDialog: {
        dialogVisible: false,
        rowObj: {},
      },
    };
  },
  computed: {
    inputListeners() {
      const listeners = { ...this.$listeners };
      delete listeners.change;
      return listeners;
    },
    selectedOption() {
      return this.dictOptions.find((item) => item.value === this.currentValue);
    },
  },
  watch: {
    value(newVal) {
      this.currentValue = newVal;
      this.updateSelectColor();
    },
    options: {
      handler(newVal) {
        this.dictOptions = newVal;
        this.$nextTick(() => {
          this.updateSelectColor();
        });
      },
      immediate: true,
    },
    dictOptions() {
      this.$nextTick(() => {
        this.updateSelectColor();
      });
    },
  },
  created() {
    if (this.options && this.options.length > 0) {
      this.dictOptions = this.options;
    } else if (this.dictCode) {
      this.getDictData();
    }
  },
  mounted() {
    this.updateSelectColor();
  },
  methods: {
    async getDictData() {},
    onAddClick() {
      this.dictDialog.dialogVisible = false;
      this.addDialog.dialogVisible = true;
    },
    eventClose(dialog) {
      dialog.dialogVisible = false;
    },
    onDictListClick() {
      if (
        this.dictCode &&
        (!this.hasParent || (this.hasParent && this.parentData.dictCode))
      ) {
        this.dictDialog.dialogVisible = true;
      }
    },
    handleChange(val) {
      this.currentValue = val;
      this.$emit("input", val);
      this.$emit("change", val);
      this.updateSelectColor();
    },
    updateSelectColor() {
      this.$nextTick(() => {
        if (this.$refs.dictSelectRef) {
          const selectInput =
            this.$refs.dictSelectRef.$el.querySelector(".el-input__inner");
          if (selectInput) {
            const selected = this.selectedOption;
            if (selected && selected.color) {
              selectInput.style.color = selected.color;
            } else {
              selectInput.style.color = "";
            }
          }
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
.dict-select-container {
  position: relative;
  ::v-deep .el-input--suffix .el-input__inner {
    padding-right: 50px;
  }
  .config-btn {
    position: absolute;
    // display: none;
    top: 0px;
    right: 32px;
    font-size: 14px;
    line-height: 32px;
    height: 32px;
    color: rgba(0, 0, 0, 0.65);
    cursor: pointer;
    &.disabled {
      cursor: not-allowed;
      color: rgba(0, 0, 0, 0.45);
    }
  }
  &:hover {
    .config-btn {
      display: block;
    }
  }
}
</style>
