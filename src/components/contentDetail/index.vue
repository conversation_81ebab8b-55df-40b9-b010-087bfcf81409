<template>
  <div class="s-detail">
    <el-page-header
      class="detail-header"
      @back="goBack"
      :content="content"
      title=""
      v-if="content"
    ></el-page-header>
    <el-page-header
      class="detail-header"
      @back="goBack"
      title=""
      v-if="$slots.headContent"
    >
      <div slot="content">
        <slot name="headContent"></slot>
      </div>
    </el-page-header>
    <div class="detail-container" :class="isList ? '' : 'detail-style'">
      <slot></slot>
    </div>
    <div v-if="$slots.footer" class="detail-footer">
      <slot name="footer"></slot>
    </div>
  </div>
</template>
<script>
import linkPage from "./mixins/linkPage.js";

export default {
  name: "s-detail",
  mixins: [linkPage],
  props: {
    back: {
      type: Function,
      default: null,
    },
    content: {
      type: String,
      default: "",
    },
    jumpPath: {
      type: String,
      default: "",
    },
    isList: {
      type: <PERSON>olean,
      default: false,
    },
  },
  methods: {
    goBack() {
      return this.back
        ? this.back()
        : this.onBackPage(this.jumpPath, this.$route.query);
    },
  },
};
</script>
<style lang="scss">
.s-detail {
  .el-page-header {
    padding: 16px;
    margin: -16px -16px 0 -16px;
    background: #fff;
    box-shadow: 0 1px 4px rgb(0 21 41 / 8%);
    .el-page-header__left {
      margin-right: 30px;
      .el-icon-back {
        margin-right: 0px;
      }
      &::after {
        right: -13px;
      }
    }
    .el-page-header__content {
      color: #444444;
      font-size: 16px;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .detail-header {
    margin-bottom: 16px;
    // & + .detail-container {
    //   height: calc(100% - 56px);
    // }
  }

  .detail-container {
    // position: absolute;
    width: 100%;
    height: calc(100% - 24px);
    overflow: auto;
  }
  .detail-style {
    // background: #fff;
    // padding: 16px;
  }
  .detail-footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    right: 0;
    background: #fff;
    z-index: 999;
    border-top: 1px solid #ddd;
    padding: 8px 16px;
  }
}
</style>
