<template>
  <div class="no-data full-height full-width">
    <div class="detail-body">
      <slot>
        <span class="no-data-default">{{ text }}</span>
      </slot>
    </div>
  </div>
</template>

<script>
import i18n from "@/i18n";
export default {
  name: "noData",
  props: {
    text: {
      type: String,
      default: i18n.t("common.noData"),
    },
  },
  data() {
    return {};
  },
};
</script>
<style lang="scss" scoped>
.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  .no-data-default {
    font-size: 28px;
    color: #dbdbdb;
    text-align: center;
  }
}
</style>
