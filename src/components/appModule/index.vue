<template>
  <span class="pull-left">
    <span class="back-home-btn" @click="openFirstModule">
      <i class="iconfont icon-square-9"></i>
    </span>
  </span>
</template>

<script>
import { constantRoutes } from "@/router";
import { getFirstPage } from "@/utils/util";
const hideList = ["/index"];

export default {
  data() {
    return {
      activeMenu: "",
    };
  },
  computed: {
    theme() {
      return this.$store.state.settings.theme;
    },
    // 顶部显示菜单
    topMenus() {
      let topMenus = [];
      this.routers.map((menu) => {
        if (menu.hidden !== true) {
          // 兼容顶部栏一级菜单内部跳转
          if (menu.path === "/") {
            topMenus.push(menu.children[0]);
          } else {
            topMenus.push(menu);
          }
        }
      });
      return topMenus;
    },
    // 所有的路由信息
    routers() {
      return this.$store.state.permission.topbarRouters;
    },
    // 设置子路由
    childrenMenus() {
      var childrenMenus = [];
      this.routers.map((router) => {
        for (var item in router.children) {
          if (router.children[item].parentPath === undefined) {
            if (router.path === "/") {
              router.children[item].path = "/" + router.children[item].path;
            } else {
              if (!this.ishttp(router.children[item].path)) {
                router.children[item].path =
                  router.path + "/" + router.children[item].path;
              }
            }
            router.children[item].parentPath = router.path;
          }
          childrenMenus.push(router.children[item]);
        }
      });
      return constantRoutes.concat(childrenMenus);
    },
  },
  beforeMount() {
    this.getActiveMenu();
  },
  methods: {
    getActiveMenu() {
      const path = this.$route.path;
      let activePath = path;
      if (
        path !== undefined &&
        path.lastIndexOf("/") > 0 &&
        hideList.indexOf(path) === -1
      ) {
        const tmpPath = path.substring(1, path.length);
        activePath = "/" + tmpPath.substring(0, tmpPath.indexOf("/"));
        this.$store.dispatch("app/toggleSideBarHide", false);
      } else if (!this.$route.children) {
        activePath = path;
        this.$store.dispatch("app/toggleSideBarHide", true);
      }
      this.activeRoutes(activePath);
      return activePath;
    },
    // 当前激活的路由
    activeRoutes(key) {
      var routes = [];
      if (this.childrenMenus && this.childrenMenus.length > 0) {
        this.childrenMenus.map((item) => {
          if (key == item.parentPath || (key == "index" && "" == item.path)) {
            routes.push(item);
          }
        });
      }
      if (routes.length > 0) {
        this.$store.commit("SET_SIDEBAR_ROUTERS", routes);
      }
      return routes;
    },
    ishttp(url) {
      return url.indexOf("http://") !== -1 || url.indexOf("https://") !== -1;
    },
    openFirstModule() {
      let path = this.topMenus[0]?.path || "";
      if (path != this.$route.matched[0].path) {
        this.$store.dispatch("tagsView/delAllAffixViews");
      }
      let routes = this.activeRoutes(path);
      let first = getFirstPage(routes, this.$router);
      this.$router.push(first);
    },
  },
};
</script>

<style lang="scss">
.back-home-btn {
  color: #fff;
  cursor: pointer;
  background: #00000033;
  display: inline-block;
  padding: 10px;
  height: auto;
  border-radius: 5px;
  line-height: 1em;
}
</style>
