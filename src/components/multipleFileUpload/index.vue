<template>
  <div>
    <!-- 自动上传 -->
    <template v-if="autoUpload">
      {{ acceptType }}
      <el-upload
        action=""
        multiple
        :auto-upload="true"
        :accept="acceptType"
        :before-upload="handleChange"
        :file-list="fileList"
        :http-request="uploadFile"
        :show-file-list="showFileList"
        :on-exceed="handleExceed"
        ref="uploadRef"
      >
        <!-- 上传按钮 -->
        <el-button size="mini" :type="primary ? 'primary' : ''">{{
          $t("common.clickUpload")
        }}</el-button>
        <!-- 上传提示 -->
        <div class="el-upload__tip" slot="tip" v-if="isShowTip">
          <template v-if="type.length > 0 && size > 0">
            {{
              $t("tips.uploadInfo", {
                type: type.join("、"),
                size,
                unit,
              })
            }}
          </template>
          <template v-if="type.length == 0 && size > 0">
            {{
              $t("tips.uploadInfo1", {
                type: type.join("、"),
                size,
                unit,
              })
            }}
          </template>
          <template v-if="type.length > 0 && size == 0">
            {{
              $t("tips.uploadInfo2", {
                type: type.join("、"),
              })
            }}
          </template>
        </div>
      </el-upload>
    </template>
    <template v-else> </template>
  </div>
</template>
<script>
import { checkFileSize, checkFileSuff } from "@/utils/validate";
export default {
  name: "multipleFileUpload",
  props: {
    primary: {
      type: Boolean,
      default: false,
    },
    value: {
      type: Array,
      default: () => [],
    },
    // 默认不自动上传
    autoUpload: {
      type: Boolean,
      default: false,
    },
    // 自动上传参数
    autoParams: {
      type: Object,
      default: () => {},
    },
    // 默认为单个上传
    limit: {
      type: Number,
      default: 1,
    },
    type: {
      type: Array,
      default: () => {
        return [];
      },
    },
    size: {
      type: Number,
      default: 5,
    },
    unit: {
      type: String,
      default: "KB",
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true,
    },
    showFileList: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      fileList: [],
    };
  },
  computed: {
    acceptType() {
      return this.type.map((item) => `.${item}`).join(",");
    },
  },
  watch: {
    value: {
      handler(val) {
        this.fileList = val;
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    handleChange(file) {
      let type = true;
      // 类型、大小
      if (this.type.length > 0 && this.size > 0) {
        type =
          !checkFileSuff(file.raw || file, this, this.type.join("、")) ||
          !checkFileSize(file.size, this, this.unit, this.size);
      }
      // 只有大小
      if (this.type.length == 0 && this.size > 0) {
        type = !checkFileSize(file.size, this, this.unit, this.size);
      }
      // 只有类型
      if (this.type.length > 0 && this.size == 0) {
        type = !checkFileSuff(file.raw || file, this, this.type.join("、"));
      }

      // 判断限制数量
      if (this.fileList.length > this.limit - 1) {
        this.errorMsg(this.$t("common.maxUploadNum", { num: this.limit }));
        return false;
      }

      if (type) {
        this.fileList.pop();
        this.$emit("input", this.fileList);
        this.$emit("change");
        return false;
      }
      let fileResult = file.raw || file;
      this.fileList.push({ fileResult });
      this.$emit("input", this.fileList);
      this.$emit("change");
    },

    uploadFile(file) {
      this.$emit("upload", file);
    },

    handleExceed() {
      this.errorMsg(this.$t("common.maxUploadNum", { num: this.limit }));
    },
  },
};
</script>
