<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    :width="width"
    :before-close="onCloseClick"
    :close-on-click-modal="false"
    @open="() => $emit('onOpenDig')"
  >
    <el-form
      ref="formRef"
      class="mgr40"
      :model="form"
      :rules="rules"
      @submit.native.prevent
      label-position="right"
      label-width="auto"
    >
      <slot />
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="onCloseClick" :loading="loading">{{
        $t("common.cancel")
      }}</el-button>
      <el-button type="primary" @click="onSubmitClick" :loading="loading">{{
        $t("common.submit")
      }}</el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  name: "dialogForm",
  props: {
    title: {
      type: String,
      default: "",
    },
    width: {
      type: String,
      default: "600px",
    },
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    form: {
      type: Object,
      default: () => {
        return {};
      },
    },
    rules: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      loading: false,
    };
  },
  methods: {
    onCloseClick() {
      this.$refs.formRef?.resetFields();
      this.$emit("closeDialog");
    },
    onSubmitClick() {
      this.$refs.formRef.validate((valid) => {
        if (!valid) {
          return false;
        }
        this.loading = true;
        this.$emit("submitClick", (isSuccess) => {
          this.loading = false;
          if (isSuccess) {
            this.onCloseClick();
          }
        });
      });
    },
  },
};
</script>
