<template>
  <el-dialog
    :title="`字典项列表${dictName ? ' - ' + dictName : ''}`"
    :visible="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    :append-to-body="true"
    @open="onOpenDig"
    :before-close="onCloseClick"
    class="advance-search-dialog"
  >
    <div v-loading="loading">
      <el-form
        :model="form"
        :rules="rules"
        size="small"
        ref="formRef"
        label-position="right"
        label-width="auto"
      >
        <search-table-content
          class="full-height full-width"
          ref="searchTableRef"
          :searchForm="searchForm"
          :columns="columns"
          :tableData="form.tableData"
          :table-loading="loading"
          :tablePage="tablePage"
          @reset="reset"
          @query="query"
          @pagination="pagination"
          :heightAuto="false"
          :isDig="true"
          :inner="true"
          :isFooter="false"
        >
          <template v-slot:actions>
            <action-btns :btns="actionBtns" />
          </template>
          <template #dictValue="{ data, index }">
            <sw-form-item
              v-if="data.isNew"
              label=""
              :prop="`tableData.${index}.dictValue`"
              :rules="rules.dictValue"
            >
              <el-input
                v-model="data.dictValue"
                placeholder="请输入"
                clearable
              ></el-input>
            </sw-form-item>
            <span v-else>{{ data.dictValue }}</span>
          </template>
          <template #color="{ data }">
            <el-color-picker v-model="data.color"></el-color-picker>
          </template>
          <template #extendData="{ data }">
            <div v-if="data.extendData">
              <div v-for="(item, index) in data.extendData" :key="index">
                <el-tag size="mini">{{ item.name }} ：{{ item.val }}</el-tag>
              </div>
            </div>
            <div v-else>-</div>
          </template>
          <template #isDefault="{ data }">
            <el-switch
              v-model="data.isDefault"
              active-value="true"
              inactive-value="false"
              @change="(val) => changeDefault(val, data)"
            >
            </el-switch>
          </template>
          <template #operation="{ data }">
            <operatebtns
              :row="data"
              :btns="operateBtns"
              :limit="operbtnsLimit"
            ></operatebtns>
          </template>
        </search-table-content>
      </el-form>
    </div>
    <!-- <div slot="footer" class="dialog-footer">
      <el-button @click="onCloseClick" class="oper-btn cancel-btn">{{
        $t("common.cancel")
      }}</el-button>
      <el-button
        type="primary"
        class="oper-btn"
        @click="onSubmitClick"
        :disabled="loading"
        >{{ $t("common.submit") }}</el-button
      >
    </div> -->
  </el-dialog>
</template>
<script>
let defaultOption = {
  tableData: [],
};
let itemOption = {
  dictValue: "",
  color: "",
  isDefault: "false",
};
import Page from "@/components/searchTableContent/mixins/page";
import { getUuid } from "@/utils/util";

import {} from "@/utils/validate";
import { cloneDeep } from "lodash";
import { getSysDicts, addSysDict, getSysDict } from "@/api/dict/index.js";

export default {
  name: "dictSelectDialog",
  mixins: [Page],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    rowObj: {
      type: Object,
      default: () => {
        return {};
      },
    },
    dictName: {
      type: String,
      default: "",
    },
    dictCode: {
      type: String,
      default: "",
    },
    // 是否有父字典
    hasParent: {
      type: Boolean,
      default: false,
    },
    parentData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      loading: false,
      form: {},
      rules: {
        dictValue: [{ required: true, message: "请输入", trigger: "blur" }],
      },
      searchForm: {},
      dictData: {},
      actionBtns: [
        {
          name: "新增字典项",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        // {
        //   name: "恢复默认颜色",
        //   class: "action-btn blue-color-btn",
        //   icon: "iconfont icon-tianjia-line",
        //   func: this.onDefaultColorClick,
        //   permi: ["none:none:none"],
        // },
      ],
      columns: [
        {
          label: "名称",
          prop: "dictValue",
          //   render: "dictValue",
          minWidth: "80px",
        },
        {
          label: "扩展数据",
          prop: "extendData",
          render: "extendData",
          minWidth: "200px",
        },
        // {
        //   label: "备注",
        //   prop: "remark",
        //   minWidth: "100px",
        // },
        // {
        //   label: "颜色",
        //   prop: "color",
        //   render: "color",
        // },
        // {
        //   label: "设为默认",
        //   prop: "isDefault",
        //   render: "isDefault",
        // },
        // {
        //   label: "操作",
        //   prop: "operation",
        //   render: "operation",
        //   width: "120px",
        //   align: "center",
        //   fixed: "right",
        // },
      ],
      operateBtns: [
        // {
        //   name: this.$t("common.delete"),
        //   class: "delete-text-btn",
        //   permi: ["none:none:none"],
        //   func: this.onDeleteClick,
        // },
      ],
      initQuery: false,
    };
  },
  computed: {},
  beforeMount() {
    this.form = cloneDeep(defaultOption);
  },

  methods: {
    request: getSysDicts,
    onOpenDig() {
      console.log(this.parentData);
      if (this.dictCode) {
        if (this.hasParent) {
          getSysDict({
            dictCode: this.dictCode,
            parentCode: this.parentData.dictCode,
            parentValue: this.parentData.dictValue,
          }).then((res) => {
            this.form.tableData = (res.data || []).map((i) => {
              return {
                ...i,
                dictValue: i.dictNameCn,
                typeCode: i.typeCode,
                extendData: i.extendData,
                id: i.id ? i.id : getUuid(),
              };
            });
          });
        } else {
          getSysDicts({
            dictCodes: [this.dictCode],
          }).then((res) => {
            this.form.tableData =
              res.data[this.dictCode]?.map((i) => {
                return {
                  // value: i.code,
                  // label: i.name,
                  dictValue: i.dictNameCn,
                  typeCode: i.typeCode,
                  extendData: i.extendData,
                  //   color: i.color,
                  //   isDefault: str(i.isDefault || false),
                  //   ...(i.remark ? JSON.parse(i.remark) : {}),
                  id: i.id ? i.id : getUuid(),
                };
              }) || [];
          });
        }
      } else {
        this.form.tableData = [];
      }
    },
    onAddClick() {
      //   this.form.tableData.push({
      //     ...cloneDeep(itemOption),
      //     id: getUuid(),
      //   });
      this.$emit("onAddClick");
    },
    changeDefault(val, row) {
      row.isDefault = val == "true" ? "false" : "true";
      if (val == "true") {
        this.form.tableData.forEach((i) => {
          i.isDefault = "false";
        });
      }
      row.isDefault = val;
    },
    onDeleteClick(row) {
      this.form.tableData = this.form.tableData.filter(
        (item) => item.id !== row.id,
      );
    },
    onDefaultColorClick() {
      this.form.tableData.forEach((item) => {
        item.color = "";
      });
    },
    onCloseClick() {
      this.form.tableData = [];
      this.$refs.formRef?.resetFields();
      this.$emit("eventSucc", this.form.tableData);
      this.$emit("eventClose");
    },

    onSubmitClick() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          console.log(this.form.tableData);
          this.$emit("eventSucc", this.form.tableData);
          this.$emit("eventClose");
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
