<template>
  <el-dialog
    :title="`新增字典项${dictName ? ' - ' + dictName : ''}`"
    :visible="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    :append-to-body="true"
    @open="onOpenDig"
    :before-close="onCloseClick"
    class="advance-search-dialog"
  >
    <div v-loading="loading">
      <el-form
        :model="form"
        :rules="rules"
        size="small"
        ref="formRef"
        label-position="right"
        label-width="auto"
        style="margin-right: 50px"
      >
        <sw-form-item label="字典项名称" prop="dictNameCn">
          <el-input
            v-model="form.dictNameCn"
            placeholder="请输入"
            clearable
          ></el-input>
        </sw-form-item>
        <!-- <sw-form-item label="备注" prop="remark">
          <el-input
            type="textarea"
            maxlength="200"
            show-word-limit
            rows="4"
            v-model="form.remark"
            placeholder="请输入"
          />
        </sw-form-item> -->
        <base-title title="扩展数据" />
        <sw-form-item label="">
          <div v-if="form.extendData.length > 0">
            <el-row class="top-title-box">
              <el-col :span="11" class="is-required-col">字段名称</el-col>
              <el-col :span="11" class="is-required-col">字段值</el-col>
            </el-row>
            <div class="content-box">
              <el-row
                v-for="(item, index) in form.extendData"
                :key="index"
                :gutter="10"
              >
                <el-col :span="11">
                  <sw-form-item
                    :prop="`extendData.${index}.name`"
                    :key="`extendData.${item.id}.name`"
                    :rules="rules.name"
                  >
                    <el-input
                      v-model="item.name"
                      placeholder="请输入"
                      clearable
                    >
                    </el-input>
                  </sw-form-item>
                </el-col>
                <el-col :span="11">
                  <sw-form-item
                    :prop="`extendData.${index}.val`"
                    :key="`extendData.${item.id}.val`"
                    :rules="rules.val"
                  >
                    <el-input v-model="item.val" placeholder="请输入" clearable>
                    </el-input>
                  </sw-form-item>
                </el-col>
                <el-col :span="2">
                  <span class="oper-icon-btn-box">
                    <i
                      class="iconfont icon-tianjia-line pending-color"
                      v-if="
                        index == form.extendData.length - 1 &&
                        form.extendData.length < 10
                      "
                      @click="onAddClick"
                      :title="$t('common.add')"
                    ></i>
                    <i
                      class="iconfont icon-shanchu fail-color"
                      @click="deleteClick(index)"
                      v-if="form.extendData.length > 0"
                      :title="$t('common.delete')"
                    ></i>
                  </span>
                </el-col>
              </el-row>
            </div>
          </div>
          <div class="mgb16" v-else>
            <el-button type="text" @click="onAddClick">
              <i class="iconfont icon-add-tag" style="font-size: 12px"></i>
              新增扩展数据</el-button
            >
          </div>
        </sw-form-item>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="onCloseClick" class="oper-btn cancel-btn">{{
        $t("common.cancel")
      }}</el-button>
      <el-button
        type="primary"
        class="oper-btn"
        @click="onSubmitClick"
        :disabled="loading"
        >{{ $t("common.submit") }}</el-button
      >
    </div>
  </el-dialog>
</template>
<script>
let defaultOption = {
  dictNameCn: "",
  remark: "",
  extendData: [
    // {
    //   name: "",
    //   val: "",
    // },
  ],
};
let itemOption = {
  name: "",
  val: "",
};
import { getUuid } from "@/utils/util";

import {} from "@/utils/validate";
import { cloneDeep } from "lodash";
import { addSysDict } from "@/api/dict/index.js";
export default {
  name: "addDict",
  mixins: [],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    rowObj: {
      type: Object,
      default: () => {
        return {};
      },
    },
    dictName: {
      type: String,
      default: "",
    },
    dictCode: {
      type: String,
      default: "",
    },
    // 是否有父字典
    hasParent: {
      type: Boolean,
      default: false,
    },
    parentData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      loading: false,
      form: cloneDeep(defaultOption),
      rules: {
        dictNameCn: [{ required: true, message: "请输入", trigger: "blur" }],
        name: [{ required: true, message: "请输入", trigger: "blur" }],
        val: [{ required: true, message: "请输入", trigger: "blur" }],
      },
    };
  },
  computed: {},
  beforeMount() {},

  methods: {
    onOpenDig() {
      this.form = cloneDeep(defaultOption);
    },
    deleteClick(index) {
      this.form.extendData.splice(index, 1);
    },
    onAddClick() {
      this.form.extendData.push({
        ...this.cloneDeep(itemOption),
        id: getUuid(),
      });
    },
    onCloseClick() {
      this.$refs.formRef?.resetFields();
      this.$emit("eventClose");
      this.$emit("eventSucc");
    },

    onSubmitClick() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          let params = {
            dictNameCn: this.form.dictNameCn,
            dictCode: this.dictCode,
            remark: this.form.remark,
            extendData: this.form.extendData.map((item) => {
              return {
                name: item.name,
                val: item.val,
              };
            }),
          };
          if (this.hasParent) {
            params.parentCode = this.parentData.dictCode;
            params.parentValue = this.parentData.dictValue;
          }
          addSysDict(params).then((res) => {
            this.successMsg("新增成功");
            this.$emit("eventClose");
            this.$emit("eventSucc");
          });
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.top-title-box {
  //   margin-bottom: 16px;
  font-size: 14px;
  font-weight: 500;
}
.oper-icon-btn-box {
  line-height: 32px;
  display: inline-block;
  i {
    cursor: pointer;
  }
  i + i {
    margin-left: 8px;
  }
}
</style>
