<template>
  <div class="common-no-data">
    <img :src="require('./no-data.svg')" alt="" />
    <div v-if="!title">{{ $t("common.noData") }}</div>
    <div v-else>{{ title }}</div>
  </div>
</template>
<script>
export default {
  name: "SwNoData",
  props: {
    title: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      showFlow: false,
    };
  },
};
</script>
<style lang="scss" scoped>
.common-no-data {
  background: #ffffff;
  padding: 50px;
  text-align: center;
  color: #909399;
}
</style>
