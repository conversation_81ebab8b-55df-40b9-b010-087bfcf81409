<template>
  <div>
    <!-- 自动上传 -->
    <template v-if="autoUpload">
      <el-upload
        action=""
        drag
        :accept="accept"
        :auto-upload="autoUpload"
        :http-request="uploadFile"
        :before-upload="handleChange"
        :file-list="fileList"
        ref="uploadRef"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          {{ $t("tips.dragFile") }}<em> {{ $t("common.clickUpload") }}</em>
        </div>
        <div class="el-upload__tip" slot="tip" v-if="isShowTip">
          <template v-if="type.length > 0 && size > 0">
            {{
              $t("tips.uploadInfo", {
                type: type.join("、"),
                size,
                unit,
              })
            }}
          </template>
          <template v-if="type.length == 0 && size > 0">
            {{
              $t("tips.uploadInfo1", {
                size,
                unit,
              })
            }}
          </template>
          <template v-if="type.length > 0 && size == 0">
            {{
              $t("tips.uploadInfo2", {
                type: type.join("、"),
              })
            }}
          </template>
        </div>
      </el-upload>
    </template>
    <!-- 手动上传 -->
    <template v-else>
      <el-upload
        action=""
        :accept="accept"
        :auto-upload="autoUpload"
        :file-list="fileList"
        :on-change="handleChange"
        ref="uploadRef"
      >
        <!-- 上传按钮 -->
        <el-button size="mini" :type="primary ? 'primary' : ''">{{
          $t("common.clickUpload")
        }}</el-button>
        <!-- 上传提示 -->
        <div class="el-upload__tip" slot="tip" v-if="isShowTip">
          <template v-if="type.length > 0 && size > 0">
            {{
              $t("tips.uploadInfo", {
                type: type.join("、"),
                size,
                unit,
              })
            }}
          </template>
          <template v-if="type.length == 0 && size > 0">
            {{
              $t("tips.uploadInfo1", {
                size,
                unit,
              })
            }}
          </template>
          <template v-if="type.length > 0 && size == 0">
            {{
              $t("tips.uploadInfo2", {
                type: type.join("、"),
              })
            }}
          </template>
        </div>
      </el-upload>
    </template>
  </div>
</template>

<script>
import { checkFileSuff, checkFileSize } from "@/utils/validate";
export default {
  name: "singleFileUpload",
  props: {
    primary: {
      type: Boolean,
      default: false,
    },
    value: {
      type: Array,
      default: () => [],
    },
    // 默认不自动上传
    autoUpload: {
      type: Boolean,
      default: false,
    },
    // 自动上传参数
    autoParams: {
      type: Object,
      default: () => {},
    },
    // 默认为单个上传
    limit: {
      type: Number,
      default: 1,
    },
    type: {
      type: Array,
      default: () => [],
    },
    size: {
      type: Number,
      default: 0,
    },
    unit: {
      type: String,
      default: "KB",
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      fileList: [],
    };
  },
  computed: {
    accept() {
      return this.type.map((item) => `.${item}`).join(",");
    },
  },
  watch: {
    value: {
      handler(val) {
        this.fileList = val;
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    handleChange(file) {
      let type = true;
      // 类型、大小
      if (this.type.length > 0 && this.size > 0) {
        type =
          !checkFileSuff(file.raw || file, this, this.type.join("、")) ||
          !checkFileSize(file.size, this, this.unit, this.size);
      }
      // 只有大小
      if (this.type.length == 0 && this.size > 0) {
        type = !checkFileSize(file.size, this, this.unit, this.size);
      }
      // 只有类型
      if (this.type.length > 0 && this.size == 0) {
        type = !checkFileSuff(file.raw || file, this, this.type.join("、"));
      }
      if (type) {
        this.fileList = [];
        this.$emit("input", this.fileList);
        this.$emit("change");
        return false;
      }
      this.fileList = [file.raw || file];
      this.$emit("input", this.fileList);
      this.$emit("change");
    },

    uploadFile(file) {
      this.$emit("upload", file);
    },
  },
};
</script>

<style scoped lang="scss"></style>
