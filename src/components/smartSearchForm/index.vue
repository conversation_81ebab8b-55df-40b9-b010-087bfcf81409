<template>
  <el-form
    ref="searchFormRef"
    :model="dataForm"
    @submit.native.prevent
    @keydown.native="onKeydown"
    inline
    size="small"
    class="search-form smart-search-form"
    :rules="rules"
  >
    <el-container>
      <el-main ref="target">
        <slot></slot>
      </el-main>
      <el-aside :width="asideWidth">
        <div class="el-form-item el-form-item--small">
          <div style="display: flex">
            <el-button
              type="primary"
              class="search-btn"
              size="small"
              @click="$emit('query')"
              id="searchBtnId"
              >{{ $t("common.search") }}</el-button
            >
            <el-button
              size="small"
              class="search-btn cancel-btn mgl16"
              @click="$emit('reset')"
              v-if="showResetBtn && $slots.default"
              id="resetBtnId"
              >{{ $t("common.reset") }}</el-button
            >
            <el-button
              class="toggle-btn"
              v-show="showExpand"
              type="text"
              @click="$emit('expandChange', !expand)"
              id="expandBtnId"
            >
              <i class="el-icon-d-arrow-right" :class="{ expand: expand }"> </i>
            </el-button>
            <el-popover
              placement="bottom-start"
              title="高级筛选"
              width="200"
              trigger="hover"
              v-show="isShowAdvance"
            >
              <div class="advance-content">内容待定</div>
              <span
                id="advanceBtnId"
                class="advance-search-btn mgl16"
                :class="{ active: isAdvance }"
                @click="$emit('openAdvanceDialog')"
                slot="reference"
              >
                <i class="iconfont icon-advance-search"> </i>
                高级筛选
              </span>
            </el-popover>
          </div>
        </div>
      </el-aside>
    </el-container>
    <userSearchDialog
      :dialogVisible="userDialog.dialogVisible"
    ></userSearchDialog>
  </el-form>
</template>

<script>
import userSearchDialog from "../userSearchDialog/index.vue";
export default {
  name: "smartSearchForm",
  components: {
    userSearchDialog,
  },
  props: {
    labelWidth: {
      type: String,
      default: "70px",
    },
    expand: {
      type: Boolean,
      default: false,
    },
    showExpand: Boolean,
    showResetBtn: {
      type: Boolean,
      default: true,
    },
    rules: {
      type: Object,
      default: () => {},
    },
    model: {
      type: Object,
      default: () => {
        return {};
      },
    },
    // 是否开启高级筛选
    isShowAdvance: {
      type: Boolean,
      default: true,
    },
    // 高级筛选高亮
    isAdvance: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dataForm: {},
      userDialog: {
        dialogVisible: false,
      },
    };
  },
  watch: {
    model: {
      handler(val) {
        this.dataForm = val;
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    asideWidth() {
      if (this.showResetBtn) {
        return "290px";
      }
      return "100px";
    },
  },
  methods: {
    getRowHeight() {
      return this.$refs.target.$el.clientHeight;
    },
    onKeydown(e) {
      // 禁用Tab键
      if (e.keyCode === 9) {
        e.preventDefault();
        return false;
      }
    },
    resetField() {
      this.$refs.searchFormRef.resetFields();
    },
  },
};
</script>
<style lang="scss" scoped>
.advance-search-btn {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  cursor: pointer;
  .iconfont {
    font-size: 12px;
  }
  &.active {
    color: var(--color-primary);
  }
}
.search-form.smart-search-form {
  ::v-deep .el-container {
    display: block;
    .el-main {
      display: inline-block;
      max-width: calc(100% - 290px);
    }
    .el-aside {
      display: inline-block;
      vertical-align: top;
    }
  }
}
.search-form .el-main {
  padding: 0;
  // overflow: hidden;
}
.search-form .el-main .form__row {
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.search-form .el-main .el-form-item--mini.el-form-item,
.search-form .el-main .el-form-item--small.el-form-item {
  margin-bottom: 16px;
}
.search-form .el-aside .el-form-item--mini.el-form-item,
.search-form .el-aside .el-form-item--small.el-form-item {
  padding: 0px;
  margin-bottom: 16px;
}
.search-form .el-aside {
  background-color: #fff;
  padding: 0px;
  margin-bottom: 0px;
  text-align: right;
  overflow: hidden;
}
.search-form .search-btn {
  width: 72px;
  height: 32px;
  padding: 0;
  line-height: 32px;
  font-size: 14px;
}
.search-form .toggle-btn {
  padding: 8px 0;
}
.search-form .toggle-btn i {
  font-size: 14px;
  -webkit-transition: -webkit-transform 0.3s ease;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
}
.search-form .toggle-btn i.expand {
  -webkit-transform: rotate(270deg);
  transform: rotate(270deg);
}
.search-form .search-form__content .el-form-item {
  margin-bottom: 16px;
}
.search-form .search-form__btn {
  text-align: center;
}
.search-form .search-form__btn .el-form-item__content {
  margin-left: 0 !important;
}
</style>
