<template>
  <div>
    <el-select
      v-model="content"
      :placeholder="$t('placeholder.select')"
      v-bind="$attrs"
      filterable
      clearable
      @change="onUserChange"
    >
      <el-option
        v-for="item in userList"
        :key="item.id"
        :label="item.nickname"
        :value="item.id.toString()"
      ></el-option>
    </el-select>
  </div>
</template>
<script>
import { getUserList } from "@/api/user.js";
export default {
  name: "userSelect",
  props: {
    value: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      content: this.value,
      userList: [],
    };
  },
  created() {
    this.getUserList();
  },
  methods: {
    getUserList() {
      this.userList = [];
      getUserList({ pageNum: 1, pageSize: 9999 }).then((res) => {
        this.userList = res.data.list;
        // 需要获取当前用户进行默认赋值
      });
    },

    onUserChange() {
      this.$emit("input", this.content);
    },
  },
};
</script>
