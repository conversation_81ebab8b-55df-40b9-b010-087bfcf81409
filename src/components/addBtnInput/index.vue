<template>
  <div class="add-btn-input-container">
    <template v-if="isCustom">
      <!-- 自定义内容 -->
      <div
        class="custom-input-wrapper el-input"
        :class="[
          $attrs.size ? 'el-input--' + $attrs.size : '',
          {
            // 'is-disabled': $attrs.disabled,
            'is-focus': isFocused,
            // 'el-input--prefix': $slots.prefix || $scopedSlots.prefix,
            // 'el-input--suffix': $slots.suffix || $scopedSlots.suffix || value,
          },
        ]"
      >
        <slot name="customContent" :value="value"> 这里是插槽内容 </slot>
      </div>

      <i
        v-show="value"
        class="el-input__icon el-icon-circle-close"
        @click.stop="handleClear"
      ></i>
      <div class="config-btn" @click="onOpenClick">
        <i class="el-icon-plus"></i>
      </div>
    </template>

    <template v-else>
      <!-- 原有输入框实现 -->
      <el-input
        ref="inputRef"
        v-bind="$attrs"
        v-on="$listeners"
        :value="value"
        @input="handleInput"
        @change="handleChange"
        @focus="handleFocus"
        @blur="handleBlur"
        @clear="handleClear"
        @mouseenter="handleMouseEnter"
        @mouseleave="handleMouseLeave"
        readonly
      >
        <i
          v-show="value"
          slot="suffix"
          class="el-input__icon el-icon-circle-close"
          @click.stop="handleClear"
        ></i>
        <!-- 插槽透传 -->
        <template v-for="(_, slotName) in $slots" #[slotName]>
          <slot :name="slotName"></slot>
        </template>

        <!-- 作用域插槽透传 -->
        <template v-for="(_, slotName) in $scopedSlots" #[slotName]="slotData">
          <slot :name="slotName" v-bind="slotData"></slot>
        </template>
      </el-input>
      <div class="config-btn" @click="onOpenClick">
        <i class="el-icon-plus"></i>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  name: "EnhancedInput",
  inheritAttrs: false,
  props: {
    value: {
      type: [String, Number],
      default: "",
    },
    trim: {
      type: Boolean,
      default: false,
    },
    maxLength: {
      type: Number,
      default: undefined,
    },
    // 是否自定义展示内容
    isCustom: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      isFocused: false,
      isHovered: false,
    };
  },

  methods: {
    getInputRef() {
      return this.$refs.inputRef;
    },

    handleInput(value) {
      if (this.trim && typeof value === "string") {
        value = value.trim();
      }

      if (
        this.maxLength &&
        typeof value === "string" &&
        value.length > this.maxLength
      ) {
        value = value.substring(0, this.maxLength);
      }

      this.$emit("input", value);
    },

    handleChange(value) {
      this.$emit("change", value);
    },

    handleFocus(event) {
      this.isFocused = true;
      this.$emit("focus", event);
    },

    handleBlur(event) {
      this.isFocused = false;
      this.$emit("blur", event);
    },

    handleClear() {
      this.$emit("clear");
    },

    handleMouseEnter(event) {
      this.isHovered = true;
      this.$emit("mouseenter", event);
    },

    handleMouseLeave(event) {
      this.isHovered = false;
      this.$emit("mouseleave", event);
    },

    focus() {
      if (this.isCustom) {
        this.$el.querySelector(".custom-input-wrapper").focus();
      } else {
        this.$refs.inputRef.focus();
      }
    },

    blur() {
      if (this.isCustom) {
        this.$el.querySelector(".custom-input-wrapper").blur();
      } else {
        this.$refs.inputRef.blur();
      }
    },

    select() {
      if (!this.isCustom) {
        this.$refs.inputRef.select();
      }
    },

    handleCustomClick() {
      if (!this.$attrs.disabled) {
        this.isFocused = true;
        this.$emit("focus");
        this.onOpenClick();
      }
    },

    onOpenClick() {
      this.$emit("open");
    },
  },
};
</script>

<style scoped lang="scss">
.add-btn-input-container {
  position: relative;

  ::v-deep .el-input--suffix .el-input__inner {
    padding-right: 50px;
  }

  ::v-deep .el-input__icon.el-icon-circle-close {
    cursor: pointer;
    color: #c0c4cc;
  }

  ::v-deep .el-input__icon.el-icon-circle-close:hover {
    color: #909399;
  }

  .config-btn {
    position: absolute;
    top: 0px;
    right: 32px;
    font-size: 14px;
    line-height: 32px;
    height: 32px;
    color: rgba(0, 0, 0, 0.65);
    cursor: pointer;

    &.disabled {
      cursor: not-allowed;
      color: rgba(0, 0, 0, 0.45);
    }
  }

  &:hover {
    .config-btn {
      display: block;
    }
  }

  // 自定义输入框样式
  .custom-input-wrapper {
    position: relative;
    display: inline-block;
    width: 100%;
    height: 32px;
    line-height: 32px;
    font-size: 14px;
    box-sizing: border-box;
    cursor: pointer;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #fff;
    outline: none;
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

    &.is-focus {
      border-color: #409eff;
      outline: 0;
    }

    &.is-disabled {
      background-color: #f5f7fa;
      border-color: #e4e7ed;
      color: #c0c4cc;
      cursor: not-allowed;
    }

    &:hover {
      border-color: #c0c4cc;
    }

    .custom-input-inner {
      padding: 0 15px;
      height: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      .placeholder {
        color: #c0c4cc;
      }
    }

    .el-input__icon {
      height: 100%;
      line-height: 32px;
      color: #c0c4cc;
    }

    .custom-arrow {
      cursor: pointer;
    }

    .el-input__prefix,
    .el-input__suffix {
      position: absolute;
      top: 0;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .el-input__prefix {
      left: 5px;
    }

    .el-input__suffix {
      right: 5px;
    }
  }

  // 不同尺寸的支持
  .el-input--medium .custom-input-wrapper {
    height: 36px;
    line-height: 36px;

    .el-input__icon {
      line-height: 36px;
    }
  }

  .el-input--small .custom-input-wrapper {
    height: 32px;
    line-height: 32px;

    .el-input__icon {
      line-height: 32px;
    }
  }

  .el-input--mini .custom-input-wrapper {
    height: 28px;
    line-height: 28px;

    .el-input__icon {
      line-height: 28px;
    }
  }
}
</style>
