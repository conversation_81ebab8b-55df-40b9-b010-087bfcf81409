<template>
  <div class="btns-container">
    <div class="actions-container" v-show="!selectList.length">
      <el-button
        v-for="(item, index) in btns.filter(
          (item) => !(item.hidden && item.hidden()),
        )"
        :key="item.id || item.name"
        :icon="item.icon"
        type="primary"
        class="action-btn"
        :class="item.class"
        @click="item.func"
        :id="item.id"
        :disabled="item.rule ? item.rule() : false"
        v-hasPermi="item.permi"
        :loading="item.loading ? item.loading() : false"
        >{{ item.name }}</el-button
      >
    </div>
    <div class="batch-actions-container" v-show="selectList.length">
      <span class="select-total">
        已选
        <span class="text-primary">{{ selectList.length }}</span> 条数据
      </span>
      <span
        class="select-all-btn text-primary"
        @click="$emit('onSelectTotalClick')"
        >{{ !isSelectTotal ? `全选所有 ${total} 条数据` : "取消全选" }}</span
      >
      <div class="batch-btns-box">
        <span
          v-for="(item, index) in hasRoleBtns.filter(
            (item) => !(item.hidden && item.hidden()),
          )"
          :key="item.name"
          type="primary"
          class="batch-action-btn"
          @click="getClickFun(item)"
          :class="getClass(item)"
        >
          <i
            :class="
              (item.loading ? item.loading() : false)
                ? 'el-icon-loading'
                : item.icon
            "
            class="batch-action-btn-icon"
          ></i>
          {{ item.name }}</span
        >
        <el-dropdown
          size="small"
          v-if="hasRoleMoreBtns.length"
          class="more-dropdown"
        >
          <span class="batch-action-btn more-btn">
            <i class="el-icon-more-outline"></i>
            更多</span
          >
          <el-dropdown-menu class="operate-dropdown-more-btns" slot="dropdown">
            <el-dropdown-item
              v-for="(item, index) in hasRoleMoreBtns"
              :key="item.id || item.name"
              :disabled="item.rule ? item.rule() : false"
            >
              <span
                :key="item.name"
                type="primary"
                class="batch-action-btn"
                @click="getClickFun(item)"
                :class="getClass(item)"
              >
                {{ item.name }}</span
              >
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <span class="close-btn" @click="onCloseBatchClick">
        <i class="el-icon-error"></i>
      </span>
    </div>
  </div>
</template>
<script>
export default {
  name: "batchActionBtns",
  props: {
    btns: {
      type: Array,
      default: () => [],
    },
    batchBtns: {
      type: Array,
      default: () => [],
    },
    getSelectList: {
      type: Function,
    },
    total: {
      type: [String, Number],
      default: 0,
    },
    isSelectTotal: {
      type: Boolean,
      default: false,
    },
    limit: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      hasRoleBtns: [],
      hasRoleMoreBtns: [],
    };
  },
  computed: {
    selectList() {
      return this.getSelectList ? this.getSelectList() : [];
    },
  },
  beforeMount() {
    this.checkRuleBtn();
  },
  methods: {
    checkRuleBtn() {
      let hasRoleBtns = this.batchBtns.filter((item) =>
        this.checkPermi(item.permi),
      );
      if (this.limit > 0) {
        if (hasRoleBtns.length > this.limit + 1) {
          this.hasRoleBtns = hasRoleBtns.slice(0, this.limit);
          this.hasRoleMoreBtns = hasRoleBtns.slice(this.limit);
        } else {
          this.hasRoleBtns = hasRoleBtns;
        }
      } else {
        this.hasRoleBtns = hasRoleBtns;
      }
    },
    getClass(item) {
      return {
        [item.class]: true,
        disabled: item.rule ? item.rule() : false,
        loading: item.loading ? item.loading() : false,
      };
    },
    getClickFun(item) {
      let classData = this.getClass(item);
      if (classData.disabled || classData.loading) {
        return;
      } else {
        return item.func();
      }
    },
    // 清除多选
    onCloseBatchClick() {
      this.$emit("clearSelectList");
    },
  },
};
</script>
<style lang="scss" scoped>
.btns-container {
  min-height: 32px;
  .actions-container {
    display: flex;
  }
  .batch-actions-container {
    display: flex;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    line-height: 32px;
    .select-all-btn {
      cursor: pointer;
      display: inline-block;
      margin: 0 24px;
      &:hover {
        text-decoration: underline;
      }
    }
    .close-btn {
      cursor: pointer;
      color: rgba(0, 0, 0, 0.25);
      &:hover {
        color: rgba(0, 0, 0, 0.45);
      }
    }
    .batch-btns-box {
      display: flex;
      align-items: center;
    }
  }
}
.batch-action-btn {
  cursor: pointer;
  padding: 0 16px;
  height: 18px;
  line-height: 18px;
  font-weight: 500;
  & + .batch-action-btn {
    border-left: 1px solid #dcdfe6;
  }
  &.more-btn {
    border-left: 1px solid #dcdfe6;
  }
  &.oper-text-btn {
    &:hover {
      color: var(--color-primary);
    }
  }
  &.danger-text-btn {
    color: var(--color-danger);
    &:hover {
      color: var(--color-danger);
    }
  }
  &.disabled {
    color: #dcdfe6;
    cursor: not-allowed;
    &:hover {
      color: #dcdfe6;
    }
  }
  .batch-action-btn-icon {
    font-size: 14px;
  }
}
</style>
