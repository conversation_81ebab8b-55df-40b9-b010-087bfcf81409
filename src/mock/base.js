import Mock from "mockjs2";
import { checkedMenu, dict } from "./data";
import { menu } from "@Project/config/menu";
import { btnPermission } from "@Project/config/btnPermission";
const baseURL = process.env.VUE_APP_BASE_API;
import { setToken, getToken } from "@/utils/auth";
import { get, stubTrue, template } from "lodash";
if (swGlobal.isCCSP == "true" && swGlobal.isInIframe == "true") {
  //   setToken("xxx");
}
// 用户是否已经注册
Mock.mock(baseURL + "/init/flow/finish", "get", {
  requestId: null,
  code: "0",
  status: "0",
  message: "操作成功！",
  timestamp: null,
  costMillis: 0,
  result: {
    finish: "Y",
  },
  success: true,
});
// 获取系统信息
Mock.mock(baseURL + "/login/sysConfig", "get", {
  requestId: null,
  code: "0",
  status: "0",
  message: "操作成功！",
  timestamp: null,
  costMillis: 0,
  result: {
    themeId: "001",
    language: "zh_CN",
    systemVersion: "v1.2.0",
    UKeyInsertStatus: "0",
  },
  success: true,
});
// 获取默认主题
Mock.mock(baseURL + "/theme/default", "get", {
  requestId: null,
  code: "0",
  status: "0",
  message: "操作成功！",
  timestamp: null,
  costMillis: 0,
  result: {
    themeId: "001",
    // webTitle: "666",
    // themeNameZh: "青花",
    // themeNameEn: "qing",
    // tagsView: true,
    // sidebarLogo: true,
    // dynamicTitle: false,
    // sideTheme: "theme-theme",
    // theme: "#1c6cdd",
    // menuColorCustom: "#000",
    // menuBackgroundCustom: "#fff",
    // menuColorActiveCustom: "#fff",
    // subMenuBackgroundCustom: "#fff",
    // subMenuBackgroundActiveCustom: "#1C6CDD",
    // subMenuHoverCustom: "#1C6CDD19",
    // topBackgroundCustom: "#fff",
    // topSvgCustom: "#231815",
  },
  success: true,
});
// 获取logo
Mock.mock(new RegExp(baseURL + "/sys/manage/image/download"), "get", {
  requestId: null,
  code: "0",
  status: "0",
  message: "操作成功！",
  timestamp: null,
  costMillis: 0,
  result: null,
  success: true,
});
// 获取登录模式
Mock.mock(baseURL + "/auth/mode/list", "get", {
  requestId: null,
  code: "0",
  status: "0",
  message: "操作成功！",
  timestamp: null,
  costMillis: 0,
  result: {
    authModeId: "1",
    authModeList: [
      //   {
      //     id: "2",
      //     authModeCnName: "USBKEY",
      //     authModeEnName: "USBKEY",
      //     initStatus: 1,
      //     routePath: "loginKey",
      //   },
      {
        id: "1",
        authModeCnName: "用户名/口令",
        authModeEnName: "PASSWORD",
        initStatus: 1,
        routePath: "loginUser",
      },
    ],
  },
  success: true,
});
// 登录成功
Mock.mock(new RegExp(baseURL + "/login/loginByUp"), "post", {
  requestId: null,
  code: "0",
  status: "0",
  message: "操作成功！",
  timestamp: null,
  costMillis: 0,
  result: {
    token:
      "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0ZW5hbnRDb2RlIjoiU1lTVEVNTUFOQUdFIiwidXNlcklkIjoiMTY5NjQwMDU4ODE2MTAxNTgwOCIsInV1aWQiOiJlMzljMmE3Yzc2YjA0MTQzOTczZTFiODVmMGVjMGMxMSJ9.Xg6Gx1wz-MUBlkYo8WEB5WxoEAdBdGNjY-wIrYZ2lRA",
  },
  success: true,
});
// 退出登录
Mock.mock(new RegExp(baseURL + "/login/logOut"), "post", {
  requestId: null,
  code: "0",
  status: "0",
  message: "操作成功！",
  timestamp: null,
  costMillis: 0,
  result: null,
  success: true,
});
// 首页url地址
Mock.mock(new RegExp(baseURL + "/menu/queryIndexPage"), "get", {
  requestId: null,
  code: "0",
  status: "0",
  message: "操作成功！",
  timestamp: null,
  costMillis: 0,
  result: "/monitor/viewPage",
  success: true,
});
// 用户信息
Mock.mock(/sec\/user\/getUserByToken/, "get", {
  requestId: null,
  code: "0",
  status: "0",
  message: "操作成功！",
  timestamp: null,
  costMillis: 0,
  result: {
    id: "1696400587095662592",
    userName: "管理员",
    userDn: "",
    userUkNo: "",
    userCert: "",
    userTel: "",
    userEmail: "",
    userAddr: "",
    userType: 1,
    userIdentity: "Y",
    roleNames: "super",
    roleIds: "97308309283869776282624",
    loginDate: "2023-09-07 16:08:27",
    verification: false,
  },
  success: true,
});
// 按钮权限
Mock.mock(new RegExp(baseURL + "/menu/queryCurrentUserMenus"), "get", {
  requestId: null,
  code: "0",
  status: "0",
  message: "操作成功！",
  timestamp: null,
  costMillis: 0,
  result: btnPermission,
  success: true,
});
// 当前用户菜单
Mock.mock(new RegExp(baseURL + "/menu/queryCatalogue"), "get", {
  requestId: null,
  code: "0",
  status: "0",
  message: "操作成功！",
  timestamp: null,
  costMillis: 0,
  result: menu,
  success: true,
});

// 用户列表
Mock.mock(new RegExp(baseURL + "/sec/user/page"), "post", {
  requestId: null,
  code: "0",
  status: "0",
  message: "操作成功！",
  timestamp: null,
  costMillis: 0,
  result: {
    pageNum: 1,
    pageSize: 20,
    total: 3,
    list: [
      {
        id: "1696400588161015808",
        userName: "oper",
        userDn: "",
        userUkNo: "",
        userCert: null,
        userTel: "",
        userEmail: "",
        userAddr: null,
        userType: 1,
        userIdentity: "N",
        roleNames: "oper",
        roleIds: "103768005144488839331840",
        loginDate: "2023-09-07 15:01:45",
        verification: true,
      },
      {
        id: "1696400587737391104",
        userName: "audit",
        userDn: "",
        userUkNo: "",
        userCert: null,
        userTel: "",
        userEmail: "",
        userAddr: null,
        userType: 1,
        userIdentity: "N",
        roleNames: "audit",
        roleIds: "97309911112380508677632",
        loginDate: null,
        verification: true,
      },
      {
        id: "1696400587095662592",
        userName: "super",
        userDn: "",
        userUkNo: "",
        userCert: null,
        userTel: "",
        userEmail: "",
        userAddr: null,
        userType: 1,
        userIdentity: "N",
        roleNames: "super",
        roleIds: "97308309283869776282624",
        loginDate: "2023-09-07 16:08:27",
        verification: true,
      },
    ],
  },
  success: true,
});

// 角色列表
Mock.mock(new RegExp(baseURL + "/sec/role/page"), "post", {
  requestId: null,
  code: "0",
  status: "0",
  message: "操作成功！",
  timestamp: null,
  costMillis: 0,
  result: {
    pageNum: 1,
    pageSize: 20,
    total: 7,
    list: [
      {
        id: "1699707498653736960",
        roleCode: "test",
        flagInitialRole: 0,
        enableDelUser: 1,
        rowSign:
          "f7914254d4523df76215d129415ee1a0bef8fc3088b322a5bb335e03895b75f6",
        createBy: "",
        createDate: "2023-09-07 16:53:41",
        updateBy: "",
        updateDate: "2023-09-07 16:53:56",
        flagDel: 0,
        remark: "",
        roleType: 1,
        roleName: "test",
      },
      {
        id: "97308309283869776282624",
        roleCode: "super",
        flagInitialRole: 1,
        enableDelUser: 0,
        rowSign:
          "c61799c5f75a3ec663671e35d0db8853a4208e5fcc41c7d673fc83540b97ee04",
        createBy: "",
        createDate: "2023-07-27 15:57:04",
        updateBy: "",
        updateDate: "2023-07-27 15:57:04",
        flagDel: 0,
        remark: "",
        roleType: 1,
        roleName: "super",
      },
      {
        id: "97309911112380508677632",
        roleCode: "audit",
        flagInitialRole: 1,
        enableDelUser: 1,
        rowSign:
          "38dce6f21599a6a07397e531bff985c873c498dbe1129c812a74790413a38c20",
        createBy: "",
        createDate: "2023-07-27 15:57:04",
        updateBy: "",
        updateDate: "2023-07-27 15:57:04",
        flagDel: 0,
        remark: "",
        roleType: 1,
        roleName: "audit",
      },
      {
        id: "103768005144488839331840",
        roleCode: "oper",
        flagInitialRole: 1,
        enableDelUser: 1,
        rowSign:
          "c4ad82c715f7656b3a2a26da50db7db2f03cafa0a550054294dbd3e9185c1718",
        createBy: "",
        createDate: "2023-07-27 15:57:04",
        updateBy: "",
        updateDate: "2023-07-27 15:57:04",
        flagDel: 0,
        remark: "",
        roleType: 1,
        roleName: "oper",
      },
    ],
  },
  success: true,
});
// 获取某个角色的菜单check树
Mock.mock(new RegExp(baseURL + "/menu/queryMenus"), "get", {
  requestId: null,
  code: "0",
  status: "0",
  message: "操作成功！",
  timestamp: null,
  costMillis: 0,
  result: checkedMenu,
  success: true,
});

Mock.Random.extend({
  phone: function () {
    var phonePrefixs = ["132", "135", "189"]; // 自己写前缀哈
    return this.pick(phonePrefixs) + "****" + Mock.mock(/\d{4}/); //Number()
  },
});

let obj = {
  id: "@uuid",
  uuid: "@uuid",
  name: "mock_@cname",
  companyName: "@province()@cword(3)公司",
  tenantName: "@province()@cword(3)公司",
  "serverArea|1": ["上海1区", "上海2区", "上海3区", "上海4区"],
  userName: "@cname",
  port: "8080",
  "eipType|1": ["静态GBP", "全动态GBP"],
  eipName: ["eip-", "@word"],
  serverName: "server-" + "@word(5,10)",
  fileSize: ["@integer(1, 10)", "M"],
  fileName: "file-logs-" + "@word" + ".log",
  pkgFileName: "pkg-" + "@word(10)" + ".zip",
  pkgName: "server-" + "@word(10)",
  "serverType|1": ["CHSM", "VSM"],
  "bindStatus|1": ["未绑定", "已绑定"],
  "serviceType|1": ["公共服务", "租户独有服务"],
  "vendorIdName|1": ["三未信安"],
  "serverimage|1": ["sensec 镜像 001", "sensec 镜像 002", "sensec 镜像 003"],
  "serverguige|1": ["4核8G", "4核16G", "2核4G"],
  phone: "@phone",
  ip: "@ip",
  daikuanName: ["bandwidth-", "@word"],
  "applyStatus|1": [0, 1, 2],
  "status|1": [0, 1, 2, 3, 4],
  "algoName|1": ["SM1", "SM2", "SM4"],
  "lengthName|1": ["128", "256"],
  keygenName: "@cname",
  email: "@email",
  year: Mock.Random.integer(1, 10),
  groupMainKey: "" + "@integer(1,3)",
  "Num|1": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10],

  //应用
  appCode: "@cword(3)APP",
  appName: "mock_@cname()管理系统",
  icon: "https://www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png",
  appshort: "@cword(3)",
  proName: "@province()@cword(3)公司",
  deviceGroups: "@cword(3)设备组",
  "businessTypes|1": [
    "协签、KMIP",
    "KMIP、密码服务",
    "密码服务、电子签章",
    "电子签章、VPN服务",
    "密码服务、VPN服务",
  ],
  "busiTypeName|1": ["协签", "KMIP", "密码服务", "电子签章", "VPN服务"],

  //服务类型
  "serviceCode|1": ["协签", "KMIP", "密码服务", "电子签章", "VPN服务"],
  "serviceCreateType|1": ["自动创建", "手动创建"],
  "serviceUseType|1": ["独享", "共享"],
  mgtPort: "@integer(8000,9000)",
  mgtGatewayPort: "@integer(8000,9000)",
  busiPort: "@integer(8000,9000)",
  busiGatewayPort: "@integer(8000,9000)",

  uris: "/@word()/@word()",
  timeout: "@integer(100,3000)",
  //用户组
  usergroupId: "@uuid()",
  usergroupName: "@cname()团队组",
  usergroupCode: "@name()_group",
  "roleList|1": [
    "操作员",
    "",
    "操作员,管理员",
    "管理员,超级管理员",
    "管理员",
    "审计员",
    "测试员",
  ],

  realName: "@cname()",
  userCode: "@name()@integer(10,100)",
  "departName|1": ["测试部", "开发部", "研发部", "市场部"],

  "authModeName|+1": ["口令认证", "USBKEY", "短信验证", "LDAP", "OAuth2.0"],
  "authModeCode|+1": ["pin", "usbkey", "phone", "ldap", "oauth2"],
  "authModeType|+1": ["本地", "第三方"],
  "flagDefault|+1": ["是", "否"],
  "flagEnable|1": ["已配置", "未配置"],
  "flagSupFirst|1": ["支持", "不支持"],
  "flagSupSecond|1": ["支持", "不支持"],
  "showSort|1": "@integer(1,10)",
  "firstAuth|1": ["口令", "USBKEY", "短信验证", "LDAP", "OAuth2.0"],
  "SecondAuth|1": ["口令", "USBKEY", "短信验证", "LDAP", "OAuth2.0"],
  JsonStr: "{}",

  //公共
  remark: "@csentence(0,100)",
  createBy: "@cname",
  createTime: "@datetime('2025-04-dd:HH:mm:ss')",
  activateTime: "@datetime('2025-04-dd:HH:mm:ss')",
  archiveTime: "@datetime('2025-04-dd:HH:mm:ss')",
  revOKeTime: "@datetime('2025-04-dd:HH:mm:ss')",
  auditTime: "@datetime('2025-04-dd:HH:mm:ss')",
  applyTime: "@datetime('2025-04-dd:HH:mm:ss')",
  updateBy: "@cname",
  updateTime: "@datetime('2025-04-dd:HH:mm:ss')",
  startTime: "@datetime('2025-04-dd:HH:mm:ss')",
  endTime: "@datetime('2025-04-dd:HH:mm:ss')",
};

let getMockList = (options) => {
  return Mock.mock({
    code: "0",
    result: {
      "list|20": [obj],
      total: 200,
    },
  });
};
let getMockList2 = (options) => {
  return Mock.mock({
    code: "0",
    "result|30": [obj],
  });
};
let info = {
  result: obj,
  total: 200,
  records: 200,
};

//租户列表
// Mock.mock(/tenant\/find/, "post", getMockList);
Mock.mock(/demo\/test\/list2/, "post", getMockList2);

Mock.mock(/demo\/test\/list/, "post", getMockList);

let yeziObj = {
  id: "@uuid()",
  name: "@name()",
};
let treeObj = {
  id: "@uuid()",
  name: ["1级", "@name()"],
  "children|3": [
    {
      id: "@uuid()",
      name: ["2级", "@name()"],
      "children|2": [
        {
          id: "@uuid()",
          name: ["3级", "@name()"],
          "children|1": [yeziObj],
        },
      ],
    },
  ],
};
Mock.mock(/mock\/treelist/, "post", {
  code: "0",
  "result|4": [treeObj],
  total: 200,
  records: 200,
});

Mock.mock(/demo\/test\/detail/, "get", info);
Mock.mock(/demo\/test\/link/, "get", getMockList);
Mock.mock(/demo\/test\/upload/, "post", () => {
  return {
    code: "0",
    result: "",
    message: "上传成功",
  };
});

Mock.mock(/mock\/add\/form/, "post", () => {
  return {
    code: "0",
    result: "",
    message: "新增成功",
  };
});
Mock.mock(/mock\/chart/, "post", {
  code: "0",
  result: {
    "dataList|10": [
      {
        name: "指标@integer(100,200)",
        value: "@integer(100,1000)",
        "unit|1": ["%", "个", "次", "台", "W"],
        "type|1": ["pki", "svs", "kms", "sms", "secdb", "tsa", "secstorage"],
        "data|2": [
          {
            name: "指标@integer(200,300)",
            value: "@integer(100,1000)",
            "unit|1": ["%", "个", "次", "台", "W"],
          },
        ],
        alarmTypeName: "指标@integer(300,400)",
        content: "@csentence",
        lastTime: "@datetime('2025-04-dd:HH:mm:ss')",
        "level|1": [1, 2, 3],
        deviceName: "指标@integer(400,500)",
        ip: "@ip",
        "status|1": [1, 2, 0],
        cpu: "@integer(0,100)",
        memory: "@integer(0,100)",
        disk: "@integer(0,100)",
      },
    ],
    total: "@integer(0,1000)",
    rate: "@integer(0,100)",
    unit: "%",
    "pieChartList|4": [
      {
        name: "指标@integer(500,600)",
        value: "@integer(0,100)",
        rate: "@integer(0,100)",
      },
    ],
    "lineChartList|5": [
      {
        name: "指标@integer(600,700)",
        "text|1": ["近一小时", "近一周", "近一月", "近三月", "近一年"],
        "unit|1": ["%", "个", "次", "台", "W"],
        "xlist|20": ["@date"],
        "ylist|20": ["@integer(0,100)"],
        y: "@integer(0,100)",
      },
    ],
    "barChartList|3": [
      {
        name: "指标@integer(700,800)",
        "xlist|5": ["@cword(2,3)"],
        "ylist|5": ["@integer(0,100)"],
      },
    ],
  },
  total: 200,
});

Mock.mock(/mock\/search/, "post", ({ body }) => {
  let pageSize = 20;
  let value = JSON.parse(body).value || "";
  let pageNum = JSON.parse(body).pageNum || 1;
  let arr = [];
  for (let i = 0; i < pageSize; i++) {
    arr.push({
      label: `第${pageNum}页${value}_${i + 1}`,
      value: `第${pageNum}页${value}_${i + 1}`,
    });
  }
  return {
    code: "0",
    result: {
      total: 121,
      list: arr,
    },
    message: "新增成功",
  };
});
// 获取多个字典
if (true && process.env.NODE_ENV === "development") {
  Mock.mock(new RegExp(baseURL + "/secDict/queryByTypeCode"), "post", {
    requestId: null,
    code: "0",
    status: "0",
    message: "操作成功！",
    timestamp: null,
    costMillis: 0,
    result: dict,
    success: true,
  });
}
