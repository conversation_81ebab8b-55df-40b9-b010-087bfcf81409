import Mock from "mockjs2";
if (false && process.env.NODE_ENV === "development") {
  // 产品管理
  Mock.mock(/product\/manage\/queryToPage/, "post", {
    requestId: null,
    code: "0",
    status: "0",
    message: "操作成功！",
    timestamp: null,
    costMillis: 0,
    result: {
      pageNum: 1,
      pageSize: 4,
      total: 4,
      "list|20": [
        {
          id: "主键_@uuid()",
          productType: "产品类型_@uuid()",
          unineCode: "U9编码_@uuid()",
          productName: "产品名称_@uuid()",
          productModel: "产品型号_@uuid()",
          gmBatchNumber: "国密批号_@uuid()",
          configType: "配置类型,推广策略_@uuid()",
          marketStrategy: "市场策略_@uuid()",
          supplyQuotationDesc: "供货及报价说明_@uuid()",
          productCatalogPrice: "产品目录价_@uuid()",
          internalSettlePrice: "内部结算价_@uuid()",
          majorCategory: "大类_@uuid()",
          mediumCategory: "中类_@uuid()",
          minorCategory: "小类_@uuid()",
          brand: "品牌，按照公司选择（三未/天安/科友/世纪先承）_@uuid()",
          disable: "是否禁用_@uuid()",
          disableDate: "禁用日期_@uuid()",
          rdDepartment: "研发部门_@uuid()",
          productManager: "产品经理_@uuid()",
          supplierName: "供应商名称_@uuid()",
          borrowedAmount: "借用占用额_@uuid()",
          salesTaxRate: "销售税率_@uuid()",
          configMethod: "配置方式_@uuid()",
          chassis: "机箱_@uuid()",
          powerSupply: "电源_@uuid()",
          motherboard: "主板_@uuid()",
          cpu: "cpu_@uuid()",
          memory: "内存_@uuid()",
          storageDeviceElectDisk: "存储设备+电子盘_@uuid()",
          networkCard: "网卡_@uuid()",
          secretCard: "密码卡_@uuid()",
          remarks: "备注_@uuid()",
          gmCertNumber: "国密证书编号_@uuid()",
          securityLevel: "安全等级_@uuid()",
          trustInnovation: "是否信创_@uuid()",
          gmCertExpireDate: "国密证书到期日期_@uuid()",
          softwareRegNumber: "软著登记号_@uuid()",
          cardProgram: "卡内程序_@uuid()",
          fpgaVersion: "fpga版本_@uuid()",
          accessMode: "访问模式_@uuid()",
          operateSystem: "操作系统_@uuid()",
          correspondSoftwareVersion: "对应软件版本_@uuid()",
          ukcosVersion: "UK COS版本_@uuid()",
          accessControlReference: "访问控制参考_@uuid()",
          productionDepartment: "生产部门_@uuid()",
          productCode: "产品编码_@uuid()",
          materialCode: "物料编码（4位）_@uuid()",
          productionCode: "生产编码_@uuid()",
          others: "其他_@uuid()",
          safetyStock: "安全库存_@uuid()",
          createPerson: "创建人_@uuid()",
          createTime: "创建时间_@uuid()",
          outsource: "是否外采_@uuid()",
          optional: "是否选配_@uuid()",
          pushPdm: "是否已推送PDM_@uuid()",
          attributeOne: "属性1_@uuid()",
          attributeParamOne: "属性参数1_@uuid()",
          attributeTwo: "属性2_@uuid()",
          attributeParamTwo: "属性参数2_@uuid()",
          indicatorThree: "指标3_@uuid()",
          indicatorParamThree: "指标参数3_@uuid()",
          indicatorFour: "指标4_@uuid()",
          indicatorParamFour: "指标参数4_@uuid()",
          indicatorFive: "指标5_@uuid()",
          indicatorParamFive: "指标参数5_@uuid()",
          indicatorSix: "指标6_@uuid()",
          indicatorParamSix: "指标参数6_@uuid()",
          indicatorSeven: "指标7_@uuid()",
          indicatorParamSeven: "指标参数7_@uuid()",
          indicatorEight: "指标8_@uuid()",
          indicatorParamEight: "指标参数8_@uuid()",
          softwareName: "软著名称_@uuid()",
          hardwarePlatform: "硬件平台_@uuid()",
          updatePerson: "更新人_@uuid()",
          updateTime: "更新时间_@uuid()",
        },
      ],
    },
    success: true,
  });
}
