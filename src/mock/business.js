import Mock from "mockjs2";
Mock.mock(/columns\/show\/list/, "post", ({ body }) => {
  let code = JSON.parse(body).code;
  console.log(code);
  let data = {
    //   businessCustomerManage: [
    //     "customerCode",
    //     "corporation",
    //     "customerGroup",
    //     "customerName",
    //   ],
  };
  return {
    code: "0",
    result: data[code] || [],
  };
});
if (false && process.env.NODE_ENV === "development") {
  // 客户档案
  Mock.mock(/custom\/page/, "post", {
    requestId: null,
    code: "0",
    status: "0",
    message: "操作成功！",
    timestamp: null,
    costMillis: 0,
    result: {
      pageNum: 1,
      pageSize: 4,
      total: 4,
      "list|20": [
        {
          id: "物理主键_@uuid()",
          customerCode: "客户编号_@uuid()",
          corporation: "主体名称_@uuid()",
          customerGroup: "客户集_@uuid()",
          customerName: "客户名称_@uuid()",
          salesDepartment: "销售部门_@uuid()",
          salesman: "销售负责人_@uuid()",
          customerSource: "客户来源_@uuid()",
          strategicCustomerFlag: "是否为战略客户_@uuid()",
          resourceDescription: "资源描述_@uuid()",
          teamMembers: "团队成员 多个使用,隔开_@uuid()",
          businessDescription: "业务说明_@uuid()",
          formerName: "曾用名_@uuid()",
          transactionState: "成交状态_@uuid()",
          customerLevel: "客户级别_@uuid()",
          industry: "合作领域/行业_@uuid()",
          channelType: "渠道类型_@uuid()",
          discountHardware: "@integer(1,100)",
          discountSoftware: "@integer(1,100)",
          channelAgreement: "渠道协议_@uuid()",
          agreementStart: "协议起始日期_@uuid()",
          agreementEnd: "协议截止日期_@uuid()",
          agreementExtendFlag: "是否申请延迟协议周期_@uuid()",
          certificateNumber: "证书编号_@uuid()",
          taskAmount: "@integer(1,100)",
          contractSigningAmount: "@integer(1,100)",
          followUp: "跟进情况_@uuid()",
          registeredDate: "注册时间_@uuid()",
          registeredCapital: "@integer(1,100)",
          registeredIndustry: "注册行业_@uuid()",
          enterpriseNature: "企业性质_@uuid()",
          legalRepresentative: "法定代表人_@uuid()",
          officialWebsite: "官网网址_@uuid()",
          staffSize: "人员规模_@uuid()",
          socialSecurityNumber: "@integer(1,100)",
          businessScope: "经营范围_@uuid()",
          companyEmail: "公司Email_@uuid()",
          companyTel: "公司电话_@uuid()",
          country: "国家_@uuid()",
          province: "身份_@uuid()",
          city: "城市_@uuid()",
          region: "区域_@uuid()",
          address: "详细地址_@uuid()",
          u9Flag: "是否已传U9_@uuid()",
          u9Time: "传U9时间_@uuid()",
          u9Code: "U9单据号_@uuid()",
          u9Error: "U9错误信息_@uuid()",
          fbtFlag: "已传分贝通_@uuid()",
          fbtTime: "传分贝通时间_@uuid()",
          fbtError: "分贝通对接信息_@uuid()",
          fbtCode: "客户三方ID_@uuid()",
          u9Arq: "重传开票信息_@uuid()",
          u9ArqTime: "重传U9时间_@uuid()",
          extend1: "@integer(1,100)",
          extend2: "扩展字段2_@uuid()",
          extend3: "扩展字段3_@uuid()",
          createBy: "创建人_@uuid()",
          createTime: "创建时间_@uuid()",
          updateBy: "更新人_@uuid()",
          updateTime: "更新时间_@uuid()",
          name: "联系人姓名_@uuid()",
          position: "职务_@uuid()",
          mobile: "联系人手机号_@uuid()",
          tel: "联系人电话_@uuid()",
          wechat: "微信_@uuid()",
          department: "联系人部门_@uuid()",
          companyName: "公司名称_@uuid()",
          bankName: "开户行_@uuid()",
          phoneNumber: "电话_@uuid()",
          taxpayerType: "纳税人资质_@uuid()",
          taxpayerId: "纳税人识别号_@uuid()",
          bankAccount: "银行账户_@uuid()",
          invoiceAddress: "开票地址_@uuid()",
        },
      ],
    },
    success: true,
  });
  // 联系人
  Mock.mock(/contact\/page/, "post", {
    requestId: null,
    code: "0",
    status: "0",
    message: "操作成功！",
    timestamp: null,
    costMillis: 0,
    result: {
      pageNum: 1,
      pageSize: 4,
      total: 4,
      "list|20": [
        {
          id: "物理主键_@uuid()",
          contactCode: "联系人编号_@uuid()",
          customerId: "客户信息主键_@uuid()",
          name: "联系人姓名_@uuid()",
          department: "所在部门_@uuid()",
          position: "职务_@uuid()",
          sex: "性别_@uuid()",
          mobile: "手机号_@uuid()",
          tel: "电话_@uuid()",
          wechat: "微信_@uuid()",
          superiorLeader: "上级领导_@uuid()",
          primaryContactFlag: "是否首要联系人_@uuid()",
          confirmationReceiverFlag: "是否函证接收人_@uuid()",
          confirmationType: "接收函证类型_@uuid()",
          remark: "备注_@uuid()",
          extend1: "@integer(1,100)",
          extend2: "扩展字段2_@uuid()",
          extend3: "扩展字段3_@uuid()",
          createBy: "创建人_@uuid()",
          createTime: "创建时间_@uuid()",
          updateBy: "更新人_@uuid()",
          updateTime: "更新时间_@uuid()",
          customerName: "客户名称_@uuid()",
        },
      ],
    },
    success: true,
  });
  // 产品机会推进
  Mock.mock(/projectopportunity\/queryToPage/, "post", {
    requestId: null,
    code: "0",
    status: "0",
    message: "操作成功！",
    timestamp: null,
    costMillis: 0,
    result: {
      pageNum: 1,
      pageSize: 4,
      total: 4,
      "list|20": [
        {
          subjectSelectName: "主体名称_@uuid()",
          projectCity: "项目地市_@uuid()",
          projectProvince: "项目省份_@uuid()",
          projectStage: "项目阶段_@uuid()",
          projectNum: "项目编号_@uuid()",
          projectName: "项目名称_@uuid()",
          customerLevel: "客户级别_@uuid()",
          industryLittleCategory: "所属行业小类_@uuid()",
          industryBigCategory: "所属行业大类_@uuid()",
          salesPersonDept: "销售部门_@uuid()",
          salesPerson: "销售负责人_@uuid()",
          riskType: "风险类型_@uuid()",
          id: "主键id_@uuid()",
          subjectUserName: "项目用户名称（最终用户）_@uuid()",
          requireClientProjectName: "需求/客户立项名称_@uuid()",
          projectFailureReason: "项目失败原因_@uuid()",
          remark: "备注_@uuid()",
          expectedSignValue: "@integer(1,100)",
          expectedSignDate: "预计签约日期_@uuid()",
          signedContractAmount: "@integer(1,100)",
          afterSignedContractAmount: "@integer(1,100)",
          projectPersons: "项目团队成员_@uuid()",
          customerNum: "客户编号_@uuid()",
          confirmTransferU9: "是否已传U9_@uuid()",
          transferU9Date: "传U9时间_@uuid()",
          transferU9Num: "对应U9单据编号_@uuid()",
          transferU9Msg: "对接错误信息_@uuid()",
          confirmTransferFenbeitong: "已传分贝通_@uuid()",
          transferFenbeitongDate: "分贝通同步时间_@uuid()",
          transferFenbeitongThirdId: "项目三方ID_@uuid()",
          transferFenbeitongMsg: "分贝通对接信息_@uuid()",
          updateTime: "@integer(1,100)",
          createTime: "@integer(1,100)",
          createId: "创建者id_@uuid()",
        },
      ],
    },
    success: true,
  });
}
