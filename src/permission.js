import router from "./router";
import store from "./store";
import { Message } from "element-ui";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import { getToken, setToken } from "@/utils/auth";
import { isRelogin } from "@/utils/request";
import { getSession, setSession, removeSession } from "@/utils/session";
import { setLanguage, getLanguage } from "@/utils/language";
import i18n from "./i18n";
import { getSysConfig } from "@/api/common";

import { getFirstPage } from "@/utils/util";
NProgress.configure({ showSpinner: false });

const whiteList = ["/login", "/auth-redirect", "/bind", "/register"];
router.beforeEach((to, from, next) => {
  NProgress.start();
  if (getToken()) {
    // 查询认证模式
    to.meta.title && store.dispatch("settings/setTitle", to.meta.title);
    /* has token*/
    if (to.path === "/login") {
      //   next();
      next({ path: "/" });
      NProgress.done();
    } else {
      next();
      if (store.getters.roles.length === 0) {
        isRelogin.show = true;
        // 判断当前用户是否已拉取完user_info信息
        store
          .dispatch("GetInfo")
          .then(() => {
            isRelogin.show = false;
            // 获取用户按钮权限
            store.dispatch("GenerateRoutes").then((accessRoutes) => {
              // 根据roles权限生成可访问的路由表
              router.addRoutes(accessRoutes); // 动态添加可访问路由表
              if (to.path == "" || to.path == "/") {
                let first = getFirstPage(accessRoutes, router);
                next({ ...first, replace: true }); // hack方法 确保addRoutes已完成
              } else {
                next({ ...to, replace: true }); // hack方法 确保addRoutes已完成
              }
            });
          })
          .catch((err) => {
            if (swGlobal.isInIframe != "true") {
              store.dispatch("LogOut").then(() => {
                // 调用获取系统信息接口
                getSysConfig().then(async (res) => {
                  store.commit("SET_SYS_VERSION", res.data.systemVersion);
                  setSession(
                    "UKeyInsertStatus",
                    res.data.UKeyInsertStatus ? res.data.UKeyInsertStatus : 0,
                  ); // 0: 关闭uk拔掉退出登录校验  1：开启uk拔掉退出登录校验
                  if (!getLanguage()) {
                    let language = "zh";
                    if (res.data.language) {
                      language = res.data.language == "zh_CN" ? "zh" : "en";
                      i18n.locale = language;
                    }
                    setLanguage(language);
                  }
                });
                if (err == "1") {
                  next({ path: "/register" });
                  return false;
                }
                Message.error(err);
                next({ path: "/login" });
              });
            }
          });
      } else {
        // if (to.path == "" || to.path == "/") {
        //   let first = getFirstPage(accessRoutes, router);
        //   next({ ...first, replace: true }); // hack方法 确保addRoutes已完成
        // } else {
        next(); // hack方法 确保addRoutes已完成
        // }
      }
    }
  } else {
    if (swGlobal.isInIframe != "true") {
      next();
      // 没有token
      if (whiteList.indexOf(to.path) !== -1) {
        // 在免登录白名单，直接进入
        next();
      } else {
        next(`/login?redirect=/`); // 否则全部重定向到登录页
        NProgress.done();
      }
    } else {
      console.log(to);
      next();
    }
  }
});

router.afterEach(() => {
  NProgress.done();
});
