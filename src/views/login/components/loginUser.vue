<template>
  <div class="login-user" v-loading="loading">
    <el-form
      @submit.native.prevent
      class="user-form"
      :model="userForm"
      ref="userFormRef"
      :rules="rules"
    >
      <el-form-item prop="username">
        <el-input
          v-model="userForm.username"
          maxlength="64"
          :placeholder="$t('placeholder.username')"
        ></el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="userForm.password"
          type="password"
          :placeholder="$t('placeholder.password')"
          @keyup.enter.native="onLoginUserClick"
          maxlength="64"
        ></el-input>
      </el-form-item>
    </el-form>
    <el-button
      type="primary"
      class="login-btn"
      :loading="btnLoading"
      @click="onLoginUserClick"
      >{{ $t("common.login") }}</el-button
    >
  </div>
</template>
<script>
export default {
  name: "login-user",
  props: {
    redirect: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      userForm: {
        username: "",
        password: "",
      },
      rules: {
        username: [
          {
            required: true,
            message: this.$t("placeholder.username"),
            trigger: "blur",
          },
        ],
        password: [
          {
            required: true,
            message: this.$t("placeholder.password"),
            trigger: "blur",
          },
        ],
      },
      loading: false,
      btnLoading: false,
    };
  },
  methods: {
    onLoginUserClick() {
      this.$refs.userFormRef.validate(async (valid) => {
        if (valid) {
          this.btnLoading = true;
          let params = {
            password: this.userForm.password,
            // password: this.$sm3(this.userForm.password),
            username: this.userForm.username.trim(),
            type: "user",
          };
          this.$store
            .dispatch("Login", params)
            .then(() => {
              this.btnLoading = false;
              this.$router.push({ path: this.redirect || "/" }).catch(() => {});
            })
            .catch(() => {
              this.btnLoading = false;
            });
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss">
.login-user {
  height: 100%;
  .user-form {
    height: calc(100% - 48px);
  }
  .el-form-item .el-form-item__content .el-input__inner {
    height: 48px;
    line-height: 48px;
  }
  .img-box {
    width: 116px;
    height: 48px;
    border: 1px solid #888;
    margin-left: 24px;
    border-radius: 4px;
    cursor: pointer;
  }
  .code-box {
    display: flex;
    align-items: center;
  }
}
</style>
