<template>
  <div class="login-key" v-loading="loading">
    <el-form
      class="key-form"
      :model="keyForm"
      ref="keyFormRef"
      :rules="rules"
      @submit.native.prevent
    >
      <el-form-item prop="pin">
        <el-input
          id="pinId"
          v-model="keyForm.pin"
          type="password"
          :placeholder="$t('placeholder.pin')"
          @keyup.enter.native="onUkeyClick"
          maxlength="256"
        ></el-input>
      </el-form-item>
    </el-form>
    <el-button
      type="primary"
      class="login-btn"
      id="loginId"
      :loading="btnLoading"
      @click="onUkeyClick"
      >{{ $t("common.login") }}</el-button
    >
  </div>
</template>
<script>
import { getDn } from "@/api/register";
import { getSecretKey } from "@/api/login";
export default {
  name: "login-key",
  props: {
    redirect: {
      type: String,
      default: "",
    },
  },
  data() {
    let pinValidate = async (rule, value, callback) => {
      if (!this.isInUk) {
        let version = await this.isInstallUk(false);
        if (!version) {
          this.ukTip();
          return false;
        }
        // 比较uk版本号, 推荐下载
        let arr2 = version.split(".");
        let arr1 = SecUKVersion.split(".");
        let flag = true;
        for (let i = 0; i < arr1.length; i++) {
          if (arr2[i] < arr1[i]) {
            this.ukNewTip();
            flag = false;
            return false;
          }
        }
        if (!flag) {
          return false;
        }
      }
      if (!this.$ukey.isUKeyExist()) {
        return callback(new Error(this.$t("validate.insertUk")));
      }
      if (!value) {
        return callback(new Error(this.$t("placeholder.pin")));
      }

      return callback();
    };
    return {
      keyForm: {
        dn: "",
        sn: "",
        pin: "",
      },
      rules: {
        pin: [{ validator: pinValidate, trigger: "blur" }],
      },
      cert: "",
      btnLoading: false,
      loading: false,
      isInUk: false,
    };
  },
  methods: {
    onKeyDown(event) {
      if (event.keyCode == 13) {
        return false;
      }
    },
    async onUkeyClick() {
      // 需要判断是否安装uk
      if (!this.isInUk) {
        let version = await this.isInstallUk(true);
        if (!version) {
          this.ukTip();
          return false;
        }
        // 比较uk版本号, 推荐下载
        let arr2 = version.split(".");
        let arr1 = SecUKVersion.split(".");
        let flag = true;
        for (let i = 0; i < arr1.length; i++) {
          if (arr2[i] < arr1[i]) {
            this.ukNewTip();
            flag = false;
            return false;
          }
        }
        if (!flag) {
          return false;
        }
      }
      this.isInUk = true;
      this.loading = false;
      this.$refs.keyFormRef.validate((valid) => {
        if (valid) {
          let str = this.$ukey.login(this.keyForm.pin);
          if ("184549392" === str) {
            this.errorMsg(this.$t("validate.checkUKeyType"));
            return false;
          }
          if (0 !== str) {
            this.errorMsg(this.$t("validate.uKeyPasswordError"));
            return false;
          }
          this.btnLoading = true;
          this.getUkInfo();
          getSecretKey()
            .then((res) => {
              let str = res.data + "test";
              let sign = this.$ukey.signData(str);
              let params = {
                userCert: this.cert,
                signature: sign,
                plainText: str,
                type: "uk",
              };
              this.$store
                .dispatch("Login", params)
                .then(() => {
                  this.btnLoading = false;
                  this.$router
                    .push({ path: this.redirect || "/" })
                    .catch(() => {});
                })
                .catch(() => {
                  this.btnLoading = false;
                });
            })
            .catch(() => {
              this.btnLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    async getUkInfo() {
      let devName = this.$ukey.getContainerName();
      this.cert = this.$ukey.getCert(devName);
      let res = await getDn(this.cert);
      if (res.data == undefined || res.data == "") {
        this.$message.error(this.$t("validate.uSBKeyCertEmpty"));
        return false;
      }
      this.keyForm.dn = res.data;
      this.keyForm.sn = this.$ukey.getUKeySerial();
    },
    ukTip() {
      this.confirmMessage(this.$t("initLogin.ukTip"))
        .then(() => {
          window.location.href = `${this.getBasePrefix(
            true,
          )}safe-usbkey_3.1.4.exe`;
        })
        .catch(() => {});
    },
    ukNewTip() {
      this.confirmMessage(this.$t("initLogin.ukTip2"))
        .then(() => {
          window.location.href = `${this.getBasePrefix(
            true,
          )}safe-usbkey_3.1.4.exe`;
        })
        .catch(() => {});
    },
    // 判断是否安装了uk
    isInstallUk(flag) {
      this.loading = flag;
      return new Promise((reslove, reject) => {
        this.$ukey.checkPlugin((version) => {
          this.loading = false;
          reslove(version);
        });
      });
    },
  },
  mounted() {
    this.isInstallUk(true).then((res) => {
      if (!res) {
        this.ukTip();
        return false;
      }
      this.isInUk = true;

      // 比较uk版本号, 推荐下载
      let arr2 = res.split(".");
      let arr1 = SecUKVersion.split(".");
      for (let i = 0; i < arr1.length; i++) {
        if (arr2[i] < arr1[i]) {
          this.ukNewTip();
          this.isInUk = false;
          return false;
        }
      }
    });
  },
};
</script>
<style lang="scss">
.login-key {
  height: 100%;
  .key-form {
    height: calc(100% - 48px);
  }
  .el-form-item .el-form-item__content .el-input__inner {
    height: 48px;
    line-height: 48px;
  }
}
</style>
