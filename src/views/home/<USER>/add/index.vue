<template>
  <content-detail :content="isEdit ? `编辑-${form.tenderProjectName}` : '新增'">
    <menu-form-content :typeList="typeList">
      <el-form
        slot="rightContent"
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="auto"
        size="small"
        label-position="right"
      >
        <div class="right-content-item" ref="basicInfoRef">
          <base-title title="基本信息"></base-title>
          <div class="flex-form-box">
            <sw-form-item label="单据编号" prop="billNo">
              <el-input
                v-model="form.billNo"
                :placeholder="$t('placeholder.place')"
              ></el-input>
            </sw-form-item>
            <sw-form-item label="主体选择" prop="subject">
              <dict-select
                v-model="form.subject"
                filterable
                clearable
                placeholder="请选择"
                dictName="主体选择"
                dictCode="export_file_type"
                :options="dictData.export_file_type"
              ></dict-select>
            </sw-form-item>
            <sw-form-item label="负责人" prop="principalId">
              <user-select
                v-if="!isEdit"
                v-model="form.principalId"
              ></user-select>
              <span v-else>{{ form.principalName }}</span>
            </sw-form-item>
            <sw-form-item label="产品类型" prop="productType">
              <dict-select
                v-model="form.productType"
                filterable
                clearable
                placeholder="请选择"
                dictName="产品类型"
                dictCode="export_file_type"
                :options="dictData.export_file_type"
              ></dict-select>
            </sw-form-item>
            <sw-form-item label="产品描述" prop="productDesc">
              <el-input
                v-model="form.productDesc"
                :placeholder="$t('placeholder.place')"
              ></el-input>
            </sw-form-item>
            <sw-form-item label="产品名称(签约名称)" prop="productName">
              <el-input
                v-model="form.productName"
                :placeholder="$t('placeholder.place')"
              ></el-input>
            </sw-form-item>
            <sw-form-item label="产品型号" prop="productModel">
              <el-input
                v-model="form.productModel"
                :placeholder="$t('placeholder.place')"
              ></el-input>
            </sw-form-item>
            <sw-form-item label="产品目录价" prop="productPrice">
              <el-input
                v-model="form.productPrice"
                :placeholder="$t('placeholder.place')"
              ></el-input>
            </sw-form-item>
            <sw-form-item
              label="报价说明"
              style="width: calc(100% - 16px)"
              prop="quotationDesc"
            >
              <el-input
                type="textarea"
                v-model="form.quotationDesc"
                :placeholder="$t('placeholder.place')"
              ></el-input>
            </sw-form-item>
            <sw-form-item label="产品类" prop="productClass">
              <dict-select
                v-model="form.productClass"
                filterable
                clearable
                placeholder="请选择"
                dictName="产品类"
                dictCode="export_file_type"
                :options="dictData.export_file_type"
              ></dict-select>
            </sw-form-item>
          </div>
        </div>
        <div class="right-content-item" ref="attachmentInfoRef">
          <base-title title="附件信息"></base-title>
          <div class="flex-form-box">
            <sw-form-item label="附件" prop="fileList" ref="fileDomRef">
              <multiple-file-upload
                v-model="form.fileList"
                :size="500"
                unit="MB"
                :limit="100"
                :autoUpload="true"
                :showFileList="false"
                @change="onFileChange"
                @upload="submitFileClick"
              ></multiple-file-upload>
              <div class="file-box" v-if="form.fileInfo?.length > 0">
                <div
                  class="file-item"
                  v-for="item in form.fileInfo"
                  :key="item.id"
                >
                  <span :title="`${item.fileName}(${item.fileSize})`"
                    >{{ item.fileName }}<span>({{ item.fileSize }})</span></span
                  >
                  <operatebtns
                    :row="item"
                    :btns="operateBtns"
                    :limit="operateBtns.length"
                  ></operatebtns>
                </div>
              </div>
            </sw-form-item>
          </div>
        </div>
      </el-form>
    </menu-form-content>
    <div slot="footer" class="page-content-footer">
      <el-button
        @click="onCloseClick"
        :loading="loading"
        class="oper-btn cancel-btn"
        >{{ $t("common.cancel") }}</el-button
      >
      <el-button
        type="primary"
        class="oper-btn"
        @click="onSubmitClick"
        :loading="loading"
        >{{ $t("common.submit") }}</el-button
      >
    </div>
  </content-detail>
</template>
<script>
import { addService, editService } from "@/api/home/<USER>";
import { cloneDeep } from "lodash";
import userSelect from "@/components/userSelect/index";
import { getFileData, deleteFileById } from "@/api/attach/index";
export default {
  name: "hwswServiceAdd",
  components: {
    userSelect,
  },
  data() {
    return {
      isEdit: false,
      loading: false,
      dictData: {
        exclusive_authorization: [],
        export_file_type: [],
      },
      activeName: "basicInfo",
      typeList: [
        { label: "基本信息", value: "basicInfo" },
        { label: "附件信息", value: "attachmentInfo" },
      ],
      form: {
        billNo: "",
        subject: "",
        principalId: "",
        productType: "",
        productDesc: "",
        productName: "",
        productModel: "",
        productPrice: "",
        quotationDesc: "",
        productClass: "",
        fileList: [],
        fileInfo: [],
      },
      rules: {
        billNo: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        subject: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        principalId: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
      },
      operateBtns: [
        {
          name: this.$t("common.delete"),
          class: "delete-text-btn",
          permi: ["none:none:none"],
          func: this.onDeleteFile,
        },
        {
          name: this.$t("common.preview"),
          class: "oper-text-btn",
          permi: ["none:none:none"],
          func: this.onPreviewClick,
        },
      ],
    };
  },
  computed: {
    queryData() {
      return this.$route.query;
    },
  },
  created() {
    this.isEdit = !!this.$route.query.id;
    this.getDictList();
    if (this.isEdit) {
      this.getDetail();
    }
  },

  methods: {
    getDetail() {
      // this.loading = true;
    },

    dialogClick() {
      this.projectDialog = {
        dialogVisible: true,
      };
    },

    onFileChange() {
      this.$refs.fileDomRef.$children[0].clearValidate();
    },
    submitFileClick(fileInfo) {
      this.loading = true;
      getFileData({ file: fileInfo.file })
        .then((res) => {
          this.form.fileInfo.push(res.data);
        })
        .finally(() => {
          this.loading = false;
        });
    },

    onDeleteFile(row) {
      this.confirmMessage(this.$t("tips.deleteTip")).then((res) => {
        this.loading = true;
        deleteFileById({ id: row.id })
          .then((res) => {
            this.form.fileInfo.splice(this.form.fileInfo.indexOf(row), 1);
          })
          .finally(() => {
            this.loading = false;
          });
      });
    },

    eventClose(dialog) {
      dialog.dialogVisible = false;
    },
    onCloseClick() {
      this.$refs.formRef?.resetFields();
      this.$router.go(-1);
    },
    onSubmitClick() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          let request = this.isEdit ? editService : addService;
          this.loading = true;
          let params = cloneDeep({
            ...this.form,
            attachIds: this.form.fileInfo.map((i) => i.id),
          });
          delete request(params)
            .then((res) => {
              this.successMsg(this.isEdit ? "编辑成功" : "新增成功");
              this.onCloseClick();
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
