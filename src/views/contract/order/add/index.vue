<template>
  <content-detail :content="isEdit ? `编辑-${queryData.name}` : '新增'">
    <menu-form-content :typeList="typeList">
      <el-form
        slot="rightContent"
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="auto"
        size="small"
        label-position="right"
      >
        <div class="right-content-item" id="Base">
          <base-title title="基本信息"></base-title>
          <el-row :gutter="20">
            <el-col :span="12">
              <sw-form-item label="主体名称" prop="baseData.subjectSelectName">
                <dict-select
                  v-model="form.baseData.subjectSelectName"
                  :options="dictData.subject_select"
                  placeholder="请选择"
                  dictName="主体名称"
                  dictCode="subject_select"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="项目机会跟进"
                prop="baseData.projectFollowNum"
              >
                <!-- <el-select
                  v-model="form.baseData.projectFollowNum"
                  placeholder="请选择"
                  filterable
                  remote
                  reserve-keyword
                  :remote-method="getProjectFollowList"
                  :loading="loading2"
                  @change="onChangeProject"
                  clearable
                >
                  <el-option
                    v-for="(item, index) in projectList"
                    :key="item.projectNum"
                    :value="item.projectNum"
                    :label="item.projectName"
                  ></el-option>
                </el-select> -->
                <add-btn-input
                  v-model="form.baseData.projectName"
                  clearable
                  @open="openProjectDialog"
                  @clear="clearProject"
                ></add-btn-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="最终用户名称"
                prop="baseData.subjectUserName"
              >
                <el-input
                  v-model="form.baseData.subjectUserName"
                  placeholder="请输入"
                  clearable
                  maxlength="20"
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="所属行业大类"
                prop="baseData.industryBigCategory"
              >
                <dict-select
                  v-model="form.baseData.industryBigCategory"
                  :options="dictData.major_big_categories"
                  placeholder="请选择"
                  dictName="所属行业大类"
                  dictCode="major_big_categories"
                  clearable
                  @queryDictList="queryDictList"
                  @change="onChangeBigCate"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="所属行业小类"
                prop="baseData.industryLittleCategory"
              >
                <dict-select
                  v-model="form.baseData.industryLittleCategory"
                  :options="dictData[smallCateDictCode] || []"
                  placeholder="请选择"
                  dictName="所属行业小类"
                  :dictCode="smallCateDictCode"
                  clearable
                  @queryDictList="querySmallDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="签约客户" prop="baseData.customerName">
                <add-btn-input
                  v-model="form.baseData.customerName"
                  clearable
                  @open="openCustomerDialog"
                  @clear="clearCustomer"
                ></add-btn-input>
                <!-- <el-select
                  v-model="form.baseData.customerId"
                  placeholder="请选择"
                  filterable
                  remote
                  reserve-keyword
                  :remote-method="getCustomerList"
                  :loading="loading1"
                  @change="onChangeCustomer"
                >
                  <el-option
                    v-for="(item, index) in customerList"
                    :key="item.id"
                    :value="item.id"
                    :label="item.customerName"
                  ></el-option>
                </el-select> -->
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="内部合同编号" prop="baseData.contractNum">
                <el-input
                  v-model="form.baseData.contractNum"
                  placeholder="请输入"
                  clearable
                  maxlength="200"
                  :disabled="isEdit"
                ></el-input>
                <!-- TODO 是否可根据主体及日期规则自动生成？ -->
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="合同名称" prop="baseData.contractName">
                <el-input
                  v-model="form.baseData.contractName"
                  placeholder="请输入"
                  clearable
                  maxlength="200"
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="框架合同"
                prop="baseData.connectContractName"
              >
                <add-btn-input
                  v-model="form.baseData.connectContractName"
                  clearable
                  @open="openContractDialog"
                  @clear="clearContract"
                ></add-btn-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="客户合同编号"
                prop="baseData.customerContractNum"
              >
                <el-input
                  v-model="form.baseData.customerContractNum"
                  placeholder="请输入"
                  clearable
                  maxlength="200"
                ></el-input>
              </sw-form-item>
            </el-col>

            <el-col :span="24">
              <sw-form-item
                label="合同表述项目信息"
                prop="baseData.contractDescInfo"
              >
                <el-input
                  type="textarea"
                  maxlength="2000"
                  show-word-limit
                  rows="4"
                  v-model="form.baseData.contractDescInfo"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="合同类型" prop="baseData.contractType">
                <dict-select
                  v-model="form.baseData.contractType"
                  :options="dictData.contract_type"
                  placeholder="请选择"
                  dictName="合同类型"
                  dictCode="contract_type"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="标的类型" prop="baseData.contractTargetType">
                <dict-select
                  v-model="form.baseData.contractTargetType"
                  :options="dictData.target_type"
                  placeholder="请选择"
                  dictName="标的类型"
                  dictCode="target_type"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="是否进入案例库"
                prop="baseData.configContractCase"
              >
                <el-select
                  v-model="form.baseData.configContractCase"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in getEnum(['Boolean'])"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="归档日期" prop="baseData.archiveDate">
                <el-date-picker
                  v-model="form.baseData.archiveDate"
                  type="date"
                  placeholder="请选择日期"
                  value-format="yyyy-MM-dd"
                  format="yyyy-MM-dd"
                  clearable
                >
                </el-date-picker>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="签订日期" prop="baseData.signDate">
                <el-date-picker
                  v-model="form.baseData.signDate"
                  type="date"
                  placeholder="请选择日期"
                  value-format="yyyy-MM-dd"
                  format="yyyy-MM-dd"
                  clearable
                >
                </el-date-picker>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="合同有效截止日期"
                prop="baseData.contractEndDate"
              >
                <el-date-picker
                  v-model="form.baseData.contractEndDate"
                  type="date"
                  placeholder="请选择日期"
                  value-format="yyyy-MM-dd"
                  format="yyyy-MM-dd"
                  clearable
                >
                </el-date-picker>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="订单属性"
                prop="baseData.contractOrderProperty"
              >
                <dict-select
                  v-model="form.baseData.contractOrderProperty"
                  :options="dictData.order_attributes"
                  placeholder="请选择"
                  dictName="订单属性"
                  dictCode="order_attributes"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>

            <el-col :span="24">
              <sw-form-item
                label="签收/验收条件"
                prop="baseData.conditionsAcceptReceipt"
              >
                <el-input
                  type="textarea"
                  maxlength="1000"
                  show-word-limit
                  rows="4"
                  v-model="form.baseData.conditionsAcceptReceipt"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="是否含有外采产品"
                prop="baseData.thirdPartyProd"
              >
                <el-select
                  v-model="form.baseData.thirdPartyProd"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in getEnum(['Boolean'])"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="收入确认凭证"
                prop="baseData.revenueConfirmCert"
              >
                <dict-select
                  v-model="form.baseData.revenueConfirmCert"
                  :options="dictData.confirm_cert"
                  placeholder="请选择"
                  dictName="收入确认凭证"
                  dictCode="confirm_cert"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>

            <!-- <el-col :span="12">
              <sw-form-item
                label="销售人员"
                prop="baseData.salesPerson"
              >
                <el-input
                  v-model="form.baseData.salesPerson"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col> -->
            <el-col :span="12">
              <sw-form-item label="销售人员" prop="baseData.salesPersonId">
                <el-input
                  v-model="form.baseData.salesPersonId"
                  placeholder="请输入"
                  clearable
                ></el-input>
                TODO
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="销售人员部门"
                prop="baseData.salesPersonDept"
              >
                <el-input
                  v-model="form.baseData.salesPersonDept"
                  placeholder="请输入"
                  disabled
                ></el-input>
              </sw-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <sw-form-item
                label="销售人员部门id"
                prop="baseData.salesPersonDeptId"
              >
                <el-input
                  v-model="form.baseData.salesPersonDeptId"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col> -->
            <el-col :span="12">
              <sw-form-item
                label="实际考核部门"
                prop="baseData.salesAssessmentDept"
              >
                <dict-select
                  v-model="form.baseData.salesAssessmentDept"
                  :options="dictData.check_dept"
                  placeholder="请选择"
                  dictName="实际考核部门"
                  dictCode="check_dept"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="24">
              <sw-form-item
                label="项目团队成员"
                prop="baseData.projectPersonDatas"
              >
                <el-select
                  v-model="form.baseData.projectPersonDatas"
                  placeholder="请选择"
                  clearable
                  multiple
                >
                  <el-option
                    v-for="item in 4"
                    :key="item"
                    :value="item"
                    :label="item"
                  ></el-option>
                </el-select>
                TODO
              </sw-form-item>
            </el-col>

            <!-- <el-col :span="24">
              <sw-form-item label="团队成员" prop="baseData.teamPersonNames">
                <el-input
                  type="textarea"
                  maxlength="1000"
                  show-word-limit
                  rows="4"
                  v-model="form.baseData.teamPersonNames"
                  placeholder="请输入"
                  clearable
                ></el-input>
                TODO
              </sw-form-item>
            </el-col> -->
            <el-col :span="12">
              <sw-form-item
                label="源单类型"
                prop="baseData.contractSourceOrderTypey"
              >
                <dict-select
                  v-model="form.baseData.contractSourceOrderTypey"
                  :options="dictData.source_order"
                  placeholder="请选择"
                  dictName="源单类型"
                  dictCode="source_order"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="选择源单编码"
                prop="baseData.contractSourceOrderNum"
              >
                <el-select
                  v-model="form.baseData.contractSourceOrderNum"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in 4"
                    :key="item"
                    :value="item"
                    :label="item"
                  ></el-option>
                </el-select>
                TODO
              </sw-form-item>
            </el-col>
            <el-col :span="24">
              <sw-form-item label="备注" prop="baseData.contractRemark">
                <el-input
                  type="textarea"
                  maxlength="1000"
                  show-word-limit
                  rows="4"
                  v-model="form.baseData.contractRemark"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="Active">
          <base-title title="动态信息"></base-title>
          <el-row :gutter="20">
            <el-col :span="12">
              <sw-form-item
                label="已发货金额"
                prop="baseData.contractDeliveryCount"
              >
                <el-input
                  v-model="form.baseData.contractDeliveryCount"
                  placeholder=""
                  disabled
                ></el-input>
                <!-- 已批准发货单金额—已批准退货单金额 -->
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="未发货金额"
                prop="baseData.contractNoDeliveryCount"
              >
                <el-input
                  v-model="form.baseData.contractNoDeliveryCount"
                  placeholder=""
                  disabled
                ></el-input>
                <!-- 订单合计金额-已批准发货单金额+已批准退货单金额 -->
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="已开票金额"
                prop="baseData.contractInvoiceCount"
              >
                <el-input
                  v-model="form.baseData.contractInvoiceCount"
                  placeholder=""
                  disabled
                ></el-input>
                <!-- 已批准销售发票金额—已批准变更发票金额 -->
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="未开票金额"
                prop="baseData.contractNoInvoiceCount"
              >
                <el-input
                  v-model="form.baseData.contractNoInvoiceCount"
                  placeholder=""
                  disabled
                ></el-input>
                <!-- 订单总金额 -（已批准销售发票金额+已批准变更发票金额） -->
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="已回款金额"
                prop="baseData.contractPaymentCount"
              >
                <el-input
                  v-model="form.baseData.contractPaymentCount"
                  placeholder=""
                  disabled
                ></el-input>
                <!-- 已批准收款单金额 -->
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="应收余额"
                prop="baseData.contractNoPaymentCount"
              >
                <el-input
                  v-model="form.baseData.contractNoPaymentCount"
                  placeholder=""
                  disabled
                ></el-input>
                <!-- 订单合计金额-已批准收款单金额 -->
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="Person">
          <base-title title="人员分成"></base-title>
          <el-row :gutter="20">
            <el-col :span="23" :offset="1">
              <PersonCom
                :data="form.personPercents"
                ref="PersonCom"
              ></PersonCom>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="Service">
          <base-title title="维保服务"></base-title>
          <el-row :gutter="20">
            <el-col :span="23" :offset="1">
              <ServiceCom
                :data="form.supportServices"
                ref="ServiceCom"
              ></ServiceCom>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="Product">
          <base-title title="产品管理"></base-title>
          <el-row :gutter="20">
            <el-col :span="23" :offset="1">
              <ProductCom
                :data="form.productDatas"
                ref="ProductCom"
              ></ProductCom>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="Pay">
          <base-title title="付款条件"></base-title>
          <el-row :gutter="20">
            <el-col :span="23" :offset="1">
              <PayCom :data="form.paymentConditions" ref="PayCom"></PayCom>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="Repay">
          <base-title title="回款计划"></base-title>
          <el-row :gutter="20">
            <el-col :span="23" :offset="1">
              <RePayCom :data="form.paymentPlans" ref="RePayCom"></RePayCom>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="Attach">
          <base-title title="附件"></base-title>
          <el-row :gutter="20">
            <el-col :span="23" :offset="1">
              <AttachCom
                :data="form.attachIds"
                ref="AttachCom"
                :attachs="form.attachs"
              ></AttachCom>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="Other">
          <base-title title="其他"></base-title>
          <el-row :gutter="20">
            <el-col :span="24">
              <sw-form-item label="合同标签" prop="baseData.contractTag">
                <el-input
                  v-model="form.baseData.contractTag"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </menu-form-content>
    <div slot="footer" class="page-content-footer">
      <el-button
        class="oper-btn cancel-btn mgr10"
        @click="onCloseClick"
        :loading="loading"
        >{{ $t("common.cancel") }}</el-button
      >
      <el-button
        type="primary"
        class="oper-btn"
        :loading="loading"
        @click="onSubmitClick"
        >{{ $t("common.submit") }}</el-button
      >
    </div>
    <selectProject
      :dialogVisible="projectDialog.dialogVisible"
      :rowObj="projectDialog.rowObj"
      :selectedList="[{ projectNum: form.baseData.projectFollowNum }]"
      @eventClose="projectDialog.dialogVisible = false"
      @eventSucc="eventSuccAddProject"
    ></selectProject>
    <selectCustomer
      :dialogVisible="customerDialog.dialogVisible"
      :rowObj="customerDialog.rowObj"
      :selectedList="[{ id: form.baseData.customerId }]"
      @eventClose="customerDialog.dialogVisible = false"
      @eventSucc="eventSuccAddCustomer"
    ></selectCustomer>
    <connectContractDialog
      :dialogVisible="connectDialog.dialogVisible"
      :rowObj="connectDialog.rowObj"
      :selectedList="[{ contractNum: form.baseData.connectContractNum }]"
      @eventClose="connectDialog.dialogVisible = false"
      @eventSucc="eventSuccAddContract"
    ></connectContractDialog>
  </content-detail>
</template>
<script>
import {
  getContractDetail,
  addContract,
  editContract,
} from "@/api/contract/order";
import { getCustomerList } from "@/api/business/customer";
import { getOpportunityList } from "@/api/business/projectOpportunity";
import PersonCom from "./components/PersonCom/index.vue";
import ServiceCom from "./components/ServiceCom/index.vue";
import PayCom from "./components/PayCom/index.vue";
import RePayCom from "./components/RePayCom/index.vue";
import ProductCom from "./components/ProductCom/index.vue";
import AttachCom from "./components/AttachCom/index.vue";
import connectContractDialog from "./components/connectContractDialog.vue";
import selectProject from "./components/selectProject.vue";
import selectCustomer from "@/views/components/selectCustomer/index.vue";

let defaultInfo = {
  baseData: {
    subjectSelectName: "",
    projectFollowNum: "",
    projectName: "",
    customerId: "",
    customerName: "",
    contractTag: "",
    contractNum: "",
    contractName: "",
    contractType: "",
    contractTargetType: "",
    connectContractNum: "",
    connectContractName: "",
    customerContractNum: "",
    archiveDate: "",
    signDate: "",
    contractEndDate: "",
    configContractCase: "",
    thirdPartyProd: "",
    revenueConfirmCert: "",
    conditionsAcceptReceipt: "",
    salesPerson: "销售人员TODO",
    salesPersonId: "",
    salesPersonDept: "部门TODO",
    salesPersonDeptId: "部门ID TODO",
    salesAssessmentDept: "",
    contractOrderProperty: "",
    // teamPersonNames: "",
    projectPersonDatas: [],
    contractRemark: "",
    contractDescInfo: "",
    contractSourceOrderTypey: "",
    contractSourceOrderNum: "",
    contractDeliveryCount: "",
    contractNoDeliveryCount: "",
    contractInvoiceCount: "",
    contractNoInvoiceCount: "",
    contractPaymentCount: "",
    contractNoPaymentCount: "",
    industryLittleCategory: "",
    industryBigCategory: "",
    subjectUserName: "",
  },
  personPercents: [],
  supportServices: [],
  paymentConditions: [],
  paymentPlans: [],
  attachIds: [],
  attachs: [],
  productDatas: [],
};
// defaultInfo={}
export default {
  name: "contractOrderAdd",
  components: {
    PersonCom,
    ServiceCom,
    PayCom,
    RePayCom,
    ProductCom,
    AttachCom,
    connectContractDialog,
    selectCustomer,
    selectProject,
  },
  data() {
    return {
      connectDialog: {
        dialogVisible: false,
        rowObj: {},
      },
      projectDialog: {
        dialogVisible: false,
        rowObj: {},
      },
      customerDialog: {
        dialogVisible: false,
        rowObj: {},
      },
      isEdit: false,
      loading: false,
      refList: [
        { refName: "PersonCom", labelName: "personPercents" },
        { refName: "ServiceCom", labelName: "supportServices" },
        { refName: "PayCom", labelName: "paymentConditions" },
        { refName: "RePayCom", labelName: "paymentPlans" },
        { refName: "ProductCom", labelName: "productDatas" },
        { refName: "AttachCom", labelName: "attachIds" },
      ],
      dictData: {
        subject_select: [],
        confirm_cert: [],
        convert_sale_contract: [],
        target_type: [],
        contract_type: [],
        check_dept: [],
        order_attributes: [],
        source_order: [],
        bill_person: [],
        soft_refund_tax: [],
        major_big_categories: [],
      },
      typeList: [
        { label: "基本信息", value: "Base" },
        { label: "动态信息", value: "Active" },
        { label: "人员分成", value: "Person" },
        { label: "维保服务", value: "Service" },
        { label: "产品管理", value: "Product" },
        { label: "付款条件", value: "Pay" },
        { label: "回款计划", value: "Repay" },
        { label: "附件", value: "Attach" },
        { label: "其他", value: "Other" },
      ],
      form: {},
      rules: {
        "baseData.subjectSelectName": [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        // "baseData.projectFollowNum": [
        //   {
        //     required: true,
        //     message: "请输入",
        //     trigger: "blur",
        //   },
        // ],
        "baseData.customerName": [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        // "baseData.contractTag": [
        //   {
        //     required: true,
        //     message: "请输入",
        //     trigger: "blur",
        //   },
        // ],
        "baseData.contractNum": [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        "baseData.contractName": [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        "baseData.contractType": [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        "baseData.contractTargetType": [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        "baseData.archiveDate": [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        "baseData.signDate": [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        "baseData.configContractCase": [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        // "baseData.salesPerson": [
        //   {
        //     required: true,
        //     message: "请选择",
        //     trigger: "change",
        //   },
        // ],
        "baseData.salesPersonId": [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        "baseData.salesAssessmentDept": [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
      },
      customerList: [],
      projectList: [],
      loading1: false,
      loading2: false,
    };
  },
  computed: {
    queryData() {
      return this.$route.query;
    },
    // 小类 字典code
    smallCateDictCode() {
      if (this.form.baseData.industryBigCategory) {
        let currentObj = this.dictData.major_big_categories.find(
          (item) => item.value == this.form.baseData.industryBigCategory,
        );
        if (currentObj && currentObj.extendData) {
          return (
            currentObj.extendData.find((item) => item.name == "level_2")?.val ||
            ""
          );
        }
      }
      return "";
    },
  },
  created() {
    this.isEdit = !!this.$route.query.id;
    this.initData();
    this.getDictList();
  },

  methods: {
    // 选择签约客户
    openCustomerDialog() {
      this.customerDialog.dialogVisible = true;
      // 根据项目机会跟进查询
      //   this.customerDialog.rowObj = {
      //     projectFollowNum: this.form.baseData.projectFollowNum,
      //   };
    },
    eventSuccAddCustomer(row) {
      this.form.baseData.customerId = row.id;
      this.form.baseData.customerName = row.customerName;
    },
    clearCustomer() {
      this.form.baseData.customerId = "";
      this.form.baseData.customerName = "";
    },
    // 选择框架合同
    openContractDialog() {
      this.connectDialog.dialogVisible = true;
    },
    eventSuccAddContract(row) {
      this.form.baseData.connectContractNum = row.contractNum;
      this.form.baseData.connectContractName = row.contractName;
    },
    clearContract() {
      this.form.baseData.connectContractNum = "";
      this.form.baseData.connectContractName = "";
    },
    // 选择项目机会跟进（项目立项）
    openProjectDialog() {
      this.projectDialog.dialogVisible = true;
    },
    eventSuccAddProject(row) {
      this.form.baseData.projectFollowNum = row.projectNum;
      this.form.baseData.projectName = row.projectName;
    },
    clearProject() {
      this.form.baseData.projectFollowNum = "";
      this.form.baseData.projectName = "";
    },
    onChangeBigCate(val) {
      this.form.baseData.industryLittleCategory = "";
      // 二级下拉
      this.querySmallDictList();
    },
    querySmallDictList() {
      this.getSysDictData([this.smallCateDictCode]).then((res) => {
        this.$set(
          this.dictData,
          [this.smallCateDictCode],
          res.data[this.smallCateDictCode] || [],
        );
      });
    },
    getCustomerList(customerName = "") {
      if (!customerName && this.form.baseData.customerId) {
        getCustomerList({
          id: this.form.baseData.customerId,
          pageNum: 1,
          pageSize: 100,
        })
          .then((res) => {
            this.customerList = res.data.list || res.data || [];
          })
          .finally(() => {
            this.loading1 = false;
          });
      } else {
        this.loading1 = true;
        getCustomerList({
          customerName: customerName,
          pageNum: 1,
          pageSize: 100,
        })
          .then((res) => {
            this.customerList = res.data.list || res.data || [];
          })
          .finally(() => {
            this.loading1 = false;
          });
      }
    },
    onChangeCustomer(val) {
      let currentObj = this.customerList.find((item) => item.id == val);
      this.form.baseData.customerName = currentObj.customerName;
    },
    getProjectFollowList(projectName = "") {
      if (!projectName && this.form.baseData.projectFollowNum) {
        this.loading2 = true;
        getOpportunityList({
          projectNum: this.form.baseData.projectFollowNum,
          pageNum: 1,
          pageSize: 100,
        })
          .then((res) => {
            this.projectList = res.data.list || res.data || [];
          })
          .finally(() => {
            this.loading2 = false;
          });
      } else {
        this.loading2 = true;
        getOpportunityList({
          projectNameLike: projectName,
          pageNum: 1,
          pageSize: 100,
        })
          .then((res) => {
            this.projectList = res.data.list || res.data || [];
          })
          .finally(() => {
            this.loading2 = false;
          });
      }
    },
    onChangeProject(val) {
      let currentObj = this.projectList.find((item) => item.projectNum == val);
      this.form.baseData.subjectUserName = currentObj.subjectUserName || "";
      this.form.baseData.industryBigCategory =
        currentObj.industryBigCategory || "";
      this.form.baseData.industryLittleCategory =
        currentObj.industryLittleCategory || "";
    },
    queryDictList() {
      console.log("重新获取字典");
      this.getDictList();
    },
    initData() {
      this.form = this.cloneDeep(defaultInfo);
      if (this.isEdit) {
        getContractDetail({ id: this.queryData.id }).then((res) => {
          this.form = {
            ...this.cloneDeep(defaultInfo),
            ...res.data,
            personPercents: res.data.personPercents || [],
            supportServices: res.data.supportServices || [],
            paymentConditions: res.data.paymentConditions || [],
            paymentPlans: res.data.paymentPlans || [],
            attachIds: res.data.attachIds || [],
            productDatas: res.data.productDatas || [],
            attachs: res.data.attachs || [],
          };
          this.getCustomerList();
          this.getProjectFollowList();
          this.form.baseData.projectPersonDatas = res.data.baseData
            .projectPersonDatas
            ? res.data.baseData.projectPersonDatas.map((item) => item.id)
            : [];
        });
      } else {
        this.getCustomerList();
        this.getProjectFollowList();
      }
    },

    onCloseClick() {
      this.$refs.formRef?.resetFields();
      this.refList.forEach((item) => {
        const component = this.$refs[item.refName];
        if (component && component.resetField) {
          component.resetField();
        }
      });
      this.$router.go(-1);
    },
    onSubmitClick() {
      const mainFormValidate = new Promise((resolve) => {
        this.$refs.formRef.validate((valid) => {
          resolve(valid);
        });
      });
      const componentValidates = this.refList.map((item) => {
        const component = this.$refs[item.refName];
        if (!component || !component.validForm) {
          return Promise.resolve(true);
        }
        return new Promise((resolve) => {
          const result = component.validForm();
          if (typeof result === "boolean") {
            resolve(result);
          } else if (result instanceof Promise) {
            resolve(result);
          } else {
            resolve(true);
          }
        });
      });
      Promise.all([mainFormValidate, ...componentValidates]).then((results) => {
        const isAllValid = results.every((result) => result === true);
        if (isAllValid) {
          this.handleAsyncFormSubmission();
        }
      });
    },
    async handleAsyncFormSubmission() {
      try {
        let request = this.isEdit ? editContract : addContract;
        this.loading = true;
        let params = this.cloneDeep(this.form);
        for (const item of this.refList) {
          const component = this.$refs[item.refName];
          if (component && component.getParams) {
            try {
              const result = await Promise.resolve(component.getParams());
              params[item.labelName] = result;
            } catch (paramError) {
              // 如果某个组件的getParams抛出错误，则停止执行
              return;
            }
          }
        }
        // TODO 项目成员
        params.baseData.projectPersonDatas =
          params.baseData.projectPersonDatas.map((item) => {
            return {
              id: item,
              nickName: item,
            };
          });
        if (params.productDatas.length == 0) {
          this.errorMsg("请填写产品订单信息");
          return false;
        }
        const res = await request(params);
        this.successMsg(this.isEdit ? "编辑成功" : "新增成功");
        this.onCloseClick();
      } catch (error) {
        console.error("提交失败:", error);
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>
<style lang="scss" scoped></style>
