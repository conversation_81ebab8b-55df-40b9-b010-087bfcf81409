<template>
  <div>
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="auto"
      size="small"
      label-position="right"
    >
      <search-table-content
        class="full-width"
        ref="searchTableRef"
        :columns="columns"
        :tableData="form.tableData"
        :table-loading="loading"
        :tablePage="tablePage"
        @reset="reset"
        @query="query"
        @pagination="pagination"
        :isFooter="false"
        :inner="true"
      >
        <template v-slot:actions>
          <action-btns :btns="actionBtns" />
        </template>
        <template #index="{ data, index }">
          {{ index + 1 }}
        </template>
        <template #supportServiceNodeYear="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.supportServiceNodeYear`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.supportServiceNodeYear"
          >
            <el-input
              v-model="data.supportServiceNodeYear"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>
        <template #supportServiceNode="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.supportServiceNode`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.supportServiceNode"
          >
            <dict-select
              v-model="data.supportServiceNode"
              :options="dictData.support_services"
              placeholder="请选择"
              dictName="维保阶段"
              dictCode="support_services"
              clearable
              @queryDictList="getDictList"
            />
          </sw-form-item>
        </template>
        <template #supportServiceNodeStartTime="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.supportServiceNodeStartTime`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.supportServiceNodeStartTime"
          >
            <el-date-picker
              v-model="data.supportServiceNodeStartTime"
              type="date"
              placeholder="请选择日期"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
            >
            </el-date-picker>
          </sw-form-item>
        </template>
        <!-- <template #supportServiceNodeEndTime="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.supportServiceNodeEndTime`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.supportServiceNodeEndTime"
          >
            <el-date-picker
              v-model="data.supportServiceNodeEndTime"
              type="date"
              placeholder="请选择日期"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
            >
            </el-date-picker>
          </sw-form-item>
        </template> -->
        <template #operation="{ data }">
          <operatebtns
            :row="data"
            :btns="operateBtns"
            :limit="operbtnsLimit"
          ></operatebtns>
        </template>
      </search-table-content>
    </el-form>
  </div>
</template>
<script>
import Page from "@/components/searchTableContent/mixins/page";
import { getUuid } from "@/utils/util";
let defaultData = {
  tableData: [],
};
let itemOption = {
  supportServiceNodeYear: "",
  supportServiceNode: "",
  supportServiceNodeStartTime: "",
  supportServiceNodeEndTime: "",
};

export default {
  mixins: [Page],
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      initQuery: false,
      loading: false,
      form: {
        tableData: [],
      },
      dictData: {
        support_services: [],
      },
      rules: {
        supportServiceNodeYear: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        supportServiceNode: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        supportServiceNodeStartTime: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        supportServiceNodeEndTime: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
      },
      actionBtns: [
        {
          name: "新增",
          class: "action-btn blue-color-btn",
          //   icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
      ],
      columns: [
        {
          label: "序号",
          prop: "index",
          render: "index",
          width: "60px",
          align: "center",
        },
        {
          label: "维保阶段",
          prop: "supportServiceNode",
          render: "supportServiceNode",
          minWidth: "120px",
        },
        {
          label: "维保节点年限",
          prop: "supportServiceNodeYear",
          render: "supportServiceNodeYear",
          minWidth: "120px",
        },
        {
          label: "开始时间",
          prop: "supportServiceNodeStartTime",
          render: "supportServiceNodeStartTime",
          minWidth: "120px",
        },
        {
          label: "结束时间", // 自动计算：开始时间+维保节点年限
          prop: "supportServiceNodeEndTime",
          //   render: "supportServiceNodeEndTime",
          minWidth: "120px",
          formatter: (row) => {
            let endTime = "";
            const startTime = row.supportServiceNodeStartTime;
            const yearStr = row.supportServiceNodeYear;
            if (!startTime) {
              endTime = "";
            } else {
              const yearNum = parseInt(yearStr);
              if (isNaN(yearNum)) {
                endTime = startTime;
              } else {
                const startDate = new Date(startTime);
                startDate.setFullYear(startDate.getFullYear() + yearNum);
                const year = startDate.getFullYear();
                const month = String(startDate.getMonth() + 1).padStart(2, "0");
                const day = String(startDate.getDate()).padStart(2, "0");
                endTime = `${year}-${month}-${day}`;
              }
              row.supportServiceNodeEndTime = endTime;
              return endTime;
            }
          },
        },
        {
          label: "操作",
          prop: "operation",
          render: "operation",
          width: "120px",
          align: "center",
          fixed: "right",
        },
      ],
      operateBtns: [
        {
          name: "移除",
          class: "delete-text-btn",
          permi: ["none:none:none"],
          func: this.onDeleteClick,
        },
      ],
      batchAddDialog: {
        dialogVisible: false,
        rowObj: {},
      },
      addDialog: {
        dialogVisible: false,
        rowObj: {},
      },
    };
  },
  beforeMount() {
    this.getDictList();
  },
  watch: {
    data: {
      handler(newVal) {
        this.form.tableData = newVal || [];
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    onAddClick() {
      this.form.tableData.push({
        ...this.cloneDeep(itemOption),
        id: getUuid(),
      });
      //   this.batchAddDialog.dialogVisible = true;
      //   this.batchAddDialog.rowObj = {};
    },
    onDeleteClick(row) {
      console.log(row);
      //   this.form.tableData.splice(index, 1);
      this.form.tableData = this.form.tableData.filter(
        (item) => item.id != row.id,
      );
    },
    eventSuccAdd(list) {
      list.forEach((row) => {
        this.form.tableData.push({
          ...this.cloneDeep(itemOption),
        });
      });
    },
    resetField() {
      this.$refs.formRef.resetFields();
      this.form.tableData = [];
    },
    getParams() {
      return this.form.tableData;
    },
    validForm() {
      return new Promise((resolve) => {
        if (this.$refs.formRef) {
          this.$refs.formRef.validate((valid) => {
            resolve(valid);
          });
        } else {
          resolve(true);
        }
      });
    },
  },
};
</script>
