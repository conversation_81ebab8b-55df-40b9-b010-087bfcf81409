<template>
  <div>
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="auto"
      size="small"
      label-position="right"
    >
      <search-table-content
        class="full-width"
        ref="searchTableRef"
        :columns="columns"
        :tableData="form.tableData"
        :table-loading="loading"
        :tablePage="tablePage"
        @reset="reset"
        @query="query"
        @pagination="pagination"
        :isFooter="false"
        :inner="true"
      >
        <template v-slot:actions>
          <action-btns :btns="actionBtns" />
        </template>
        <template #index="{ data, index }">
          {{ index + 1 }}
        </template>
        <!-- 合同产品名称 -->
        <template #contractProductName="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.contractProductName`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.contractProductName || {}"
          >
            <el-input
              v-model="data.contractProductName"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>
        <!-- 合同规格型号 -->
        <template #contractProductType="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.contractProductType`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.contractProductType || {}"
          >
            <el-input
              v-model="data.contractProductType"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>
        <!-- 税率 productTax-->
        <template #productTax="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.productTax`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.productTax || {}"
          >
            <el-input
              v-model="data.productTax"
              placeholder="请输入"
              clearable
              @blur="(val) => changePrice('productTax', val, data)"
            ></el-input>
          </sw-form-item>
        </template>

        <!-- 数量 productNum -->
        <template #productNum="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.productNum`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.productNum || {}"
          >
            <el-input
              v-model="data.productNum"
              placeholder="请输入"
              clearable
              @blur="(val) => changePrice('productNum', val, data)"
            ></el-input>
          </sw-form-item>
        </template>

        <!-- 单位 productUnit -->
        <template #productUnit="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.productUnit`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.productUnit || {}"
          >
            <el-input
              v-model="data.productUnit"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>

        <!-- 含税单价 unitTaxPrice -->
        <template #unitTaxPrice="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.unitTaxPrice`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.unitTaxPrice || {}"
          >
            <el-input
              v-model="data.unitTaxPrice"
              placeholder="请输入"
              clearable
              @blur="(val) => changePrice('unitTaxPrice', val, data)"
            ></el-input>
          </sw-form-item>
        </template>

        <!-- 不含税单价 noUnitTaxPrice -->
        <template #noUnitTaxPrice="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.noUnitTaxPrice`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.noUnitTaxPrice || {}"
          >
            <el-input
              v-model="data.noUnitTaxPrice"
              placeholder="请输入"
              clearable
              @blur="(val) => changePrice('noUnitTaxPrice', val, data)"
            ></el-input>
          </sw-form-item>
        </template>

        <!-- 价税合计 totalAmountPrice -->
        <template #totalAmountPrice="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.totalAmountPrice`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.totalAmountPrice || {}"
          >
            <el-input
              v-model="data.totalAmountPrice"
              placeholder="请输入"
              clearable
              @blur="(val) => changePrice('totalAmountPrice', val, data)"
            ></el-input>
          </sw-form-item>
        </template>

        <!-- 软件是否可退税 softwareTaxRefund -->
        <template #softwareTaxRefund="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.softwareTaxRefund`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.softwareTaxRefund || {}"
          >
            <el-select
              v-model="data.softwareTaxRefund"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="item in getEnum(['Boolean'])"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </sw-form-item>
        </template>

        <!-- 业务难度系数 bussComplexIndex -->
        <template #bussComplexIndex="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.bussComplexIndex`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.bussComplexIndex || {}"
          >
            <el-input
              v-model="data.bussComplexIndex"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>

        <!-- 折算后合同金额 convertContractAmount -->
        <template #convertContractAmount="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.convertContractAmount`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.convertContractAmount || {}"
          >
            <el-input
              v-model="data.convertContractAmount"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>

        <!-- 应付金额 paymentCount -->
        <template #paymentCount="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.paymentCount`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.paymentCount || {}"
          >
            <el-input
              v-model="data.paymentCount"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>

        <!-- 产品属性 contractProductProperties -->
        <template #contractProductProperties="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.contractProductProperties`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.contractProductProperties || {}"
          >
            <el-input
              v-model="data.contractProductProperties"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>

        <!-- 产品序列号 productNo -->
        <template #productNo="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.productNo`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.productNo || {}"
          >
            <el-input
              v-model="data.productNo"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>

        <!-- 生产批号 produceNo -->
        <template #produceNo="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.produceNo`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.produceNo || {}"
          >
            <el-input
              v-model="data.produceNo"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>

        <!-- 已发货数量 deliverNum -->
        <template #deliverNum="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.deliverNum`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.deliverNum || {}"
          >
            <el-input
              v-model="data.deliverNum"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>

        <!-- 待发货数量 noDeliverNum -->
        <template #noDeliverNum="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.noDeliverNum`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.noDeliverNum || {}"
          >
            <el-input
              v-model="data.noDeliverNum"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>

        <!-- 实际供货数量 supplyNum -->
        <template #supplyNum="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.supplyNum`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.supplyNum || {}"
          >
            <el-input
              v-model="data.supplyNum"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>
        <!-- 供货建议  -->
        <template #supplyRecommes="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.supplyRecommes`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.supplyRecommes || {}"
          >
            <el-input
              v-model="data.supplyRecommes"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>
        <template #remark="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.remark`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.remark || {}"
          >
            <el-input
              v-model="data.remark"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>
        <template #operation="{ data }">
          <operatebtns
            :row="data"
            :btns="operateBtns"
            :limit="operbtnsLimit"
          ></operatebtns>
        </template>
      </search-table-content>
      <div class="total-container">
        <div class="total-content">
          <span class="total-name"> 合计： </span>
          <span class="total-desc">
            {{ totalPrice }}
          </span>
        </div>
      </div>
    </el-form>
    <batchAddProduct
      :dialogVisible="batchAddDialog.dialogVisible"
      :rowObj="batchAddDialog.rowObj"
      :selectedList="(() => this.form.tableData)()"
      @eventClose="batchAddDialog.dialogVisible = false"
      @eventSucc="eventSuccAdd"
    ></batchAddProduct>
  </div>
</template>
<script>
import Page from "@/components/searchTableContent/mixins/page";
import { getUuid } from "@/utils/util";
import batchAddProduct from "@/views/components/batchAddProduct/index";
import { getProductDetailByU9 } from "@/api/infoSearch/product";
let defaultData = {
  tableData: [],
};
let itemOption = {
  id: "",
  contractId: "",
  productCode: "",
  contractProductName: "",
  contractProductType: "",
  productNum: "1",
  productUnit: "",
  productTax: "13",
  paymentCount: "",
  unitTaxPrice: "",
  noUnitTaxPrice: "",
  totalAmountPrice: "",
  noTotalAmountPrice: "",
  taxPrice: "",
  softwareTaxRefund: "",
  productNo: "",
  produceNo: "",
  deliverNum: "",
  noDeliverNum: "",
  supplyNum: "",
  bussComplexIndex: "",
  convertContractAmount: "",
  supplyRecommes: "",
  contractProductProperties: "",
  remark: "",
};

export default {
  mixins: [Page],
  components: {
    batchAddProduct,
  },
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      initQuery: false,
      loading: false,
      form: {
        tableData: [],
      },
      dictData: {},
      rules: {
        contractProductName: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        contractProductType: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        productTax: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        productNum: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        productUnit: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        unitTaxPrice: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        noUnitTaxPrice: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        totalAmountPrice: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        softwareTaxRefund: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],

        paymentCount: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
      },
      actionBtns: [
        {
          name: "新增",
          class: "action-btn blue-color-btn",
          //   icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "清空",
          class: "action-btn white-color-btn",
          //   icon: "iconfont icon-tianjia-line",
          func: this.resetField,
          permi: ["none:none:none"],
        },
      ],
      columns: [
        {
          label: "序号",
          prop: "index",
          render: "index",
          width: "60px",
          align: "center",
          fixed: "left",
        },
        {
          label: "U9编码",
          prop: "unineCode",
          minWidth: "120px",
          fixed: "left",
        },
        {
          label: "产品名称",
          prop: "productName",
          minWidth: "120px",
          fixed: "left",
        },
        {
          label: "产品型号",
          prop: "productModel",
          minWidth: "120px",
        },
        {
          label: "产品编码",
          prop: "productCode",
          minWidth: "120px",
        },

        {
          label: "国密批号",
          prop: "gmBatchNumber",
          minWidth: "120px",
        },
        {
          label: "合同产品名称",
          prop: "contractProductName",
          render: "contractProductName",
          minWidth: "120px",
        },
        {
          label: "合同规格型号",
          prop: "contractProductType",
          render: "contractProductType",
          minWidth: "120px",
        },
        {
          label: "税率（%）",
          prop: "productTax",
          render: "productTax",
          minWidth: "120px",
        },
        {
          label: "数量",
          prop: "productNum",
          render: "productNum",
          minWidth: "120px",
        },
        {
          label: "单位",
          prop: "productUnit",
          render: "productUnit",
          minWidth: "120px",
        },
        {
          label: "含税单价",
          prop: "unitTaxPrice",
          render: "unitTaxPrice",
          minWidth: "120px",
        },
        {
          label: "不含税单价",
          prop: "noUnitTaxPrice",
          render: "noUnitTaxPrice",
          minWidth: "120px",
        },
        {
          label: "价税合计",
          prop: "totalAmountPrice",
          render: "totalAmountPrice",
          minWidth: "120px",
        },
        {
          label: "不含税金额", // 自动计算
          prop: "noTotalAmountPrice",
          minWidth: "120px",
        },
        {
          label: "税额", // 自动计算
          prop: "taxPrice",
          minWidth: "120px",
        },
        {
          label: "软件是否可退税",
          prop: "softwareTaxRefund",
          render: "softwareTaxRefund",
          minWidth: "120px",
        },
        {
          label: "业务难度系数",
          prop: "bussComplexIndex",
          render: "bussComplexIndex",
          minWidth: "120px",
        },
        {
          label: "折算后合同金额",
          prop: "convertContractAmount",
          render: "convertContractAmount",
          minWidth: "120px",
        },

        {
          label: "应付金额",
          prop: "paymentCount",
          render: "paymentCount",
          minWidth: "120px",
        },
        {
          label: "研发部门",
          prop: "rdDepartment",
          minWidth: "120px",
        },
        {
          label: "大类",
          prop: "majorCategory",
          minWidth: "120px",
        },
        {
          label: "中类",
          prop: "mediumCategory",
          minWidth: "120px",
        },
        {
          label: "小类",
          prop: "minorCategory",
          minWidth: "120px",
        },
        {
          label: "产品属性",
          prop: "contractProductProperties",
          prop: "contractProductProperties",
          minWidth: "120px",
        },
        {
          label: "产品序列号",
          prop: "productNo",
          render: "productNo",
          minWidth: "120px",
        },
        {
          label: "生产批号",
          prop: "produceNo",
          render: "produceNo",
          minWidth: "120px",
        },
        {
          label: "已发货数量",
          prop: "deliverNum",
          render: "deliverNum",
          minWidth: "120px",
        },
        {
          label: "待发货数量",
          prop: "noDeliverNum",
          render: "noDeliverNum",
          minWidth: "120px",
        },
        {
          label: "实际供货数量",
          prop: "supplyNum",
          render: "supplyNum",
          minWidth: "120px",
        },
        {
          label: "供货建议",
          prop: "supplyRecommes",
          render: "supplyRecommes",
          minWidth: "120px",
        },
        {
          label: "备注",
          prop: "remark",
          render: "remark",
          minWidth: "120px",
        },
        {
          label: "操作",
          prop: "operation",
          render: "operation",
          width: "120px",
          align: "center",
          fixed: "right",
        },
      ],
      operateBtns: [
        {
          name: "复制",
          class: "oper-text-btn",
          permi: ["none:none:none"],
          func: this.onCopyClick,
        },
        {
          name: "移除",
          class: "delete-text-btn",
          permi: ["none:none:none"],
          func: this.onDeleteClick,
        },
      ],
      batchAddDialog: {
        dialogVisible: false,
        rowObj: {},
      },
      addDialog: {
        dialogVisible: false,
        rowObj: {},
      },
    };
  },
  computed: {
    totalPrice() {
      if (
        !this.form ||
        !this.form.tableData ||
        !Array.isArray(this.form.tableData)
      ) {
        return 0;
      }
      return this.form.tableData.reduce((sum, item) => {
        const price = parseFloat(item.totalAmountPrice) || 0;
        return sum + price;
      }, 0);
    },
  },
  beforeMount() {},
  watch: {
    data: {
      handler(newVal) {
        this.form.tableData = newVal || [];
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    onAddClick() {
      this.batchAddDialog.dialogVisible = true;
      this.batchAddDialog.rowObj = {};
    },
    onCopyClick(row) {
      this.form.tableData.push({
        ...this.cloneDeep(row),
      });
    },
    onDeleteClick(row) {
      this.form.tableData = this.form.tableData.filter(
        (item) => item.id !== row.id,
      );
    },
    async eventSuccAdd(list) {
      for (let i = 0; i < list.length; i++) {
        try {
          const row = list[i];
          const res = await getProductDetailByU9({ unineCode: row.unineCode });
          this.form.tableData.push({
            ...this.cloneDeep(itemOption),
            ...this.cloneDeep(res.data || {}),
          });
        } catch (error) {}
      }
    },
    changePrice(field, val, data) {
      const toNumber = (value) => {
        const num = parseFloat(value);
        return isNaN(num) ? 0 : num;
      };
      const formatDecimal = (value) => {
        return parseFloat(value.toFixed(6));
      };
      data.taxPrice = toNumber(data.taxPrice);
      data.productNum = toNumber(data.productNum);
      data.unitTaxPrice = toNumber(data.unitTaxPrice);
      data.noUnitTaxPrice = toNumber(data.noUnitTaxPrice);
      data.totalAmountPrice = toNumber(data.totalAmountPrice);
      data.productTax = toNumber(data.productTax);

      switch (field) {
        // 改变税率
        case "productTax":
          data.noUnitTaxPrice = formatDecimal(
            data.unitTaxPrice / (1 + data.productTax * 0.01),
          );
          data.totalAmountPrice = formatDecimal(
            data.unitTaxPrice * data.productNum,
          );
          data.noTotalAmountPrice = formatDecimal(
            data.noUnitTaxPrice * data.productNum,
          );
          data.taxPrice = formatDecimal(
            data.totalAmountPrice - data.noTotalAmountPrice,
          );

          break;
        // 改变数量
        case "productNum":
          data.totalAmountPrice = formatDecimal(
            data.unitTaxPrice * data.productNum,
          );
          data.noTotalAmountPrice = formatDecimal(
            data.noUnitTaxPrice * data.productNum,
          );
          data.taxPrice = formatDecimal(
            data.totalAmountPrice - data.noTotalAmountPrice,
          );
          break;
        // 改变含税单价
        case "unitTaxPrice":
          data.totalAmountPrice = formatDecimal(
            data.unitTaxPrice * data.productNum,
          );
          data.noUnitTaxPrice = formatDecimal(
            data.unitTaxPrice / (1 + data.productTax * 0.01),
          );
          data.noTotalAmountPrice = formatDecimal(
            data.noUnitTaxPrice * data.productNum,
          );
          data.taxPrice = formatDecimal(
            data.totalAmountPrice - data.noTotalAmountPrice,
          );
          break;
        // 改变不含税单价
        case "noUnitTaxPrice":
          data.noTotalAmountPrice = formatDecimal(
            data.noUnitTaxPrice * data.productNum,
          );
          data.unitTaxPrice = formatDecimal(
            data.noUnitTaxPrice * (1 + data.productTax * 0.01),
          );
          data.totalAmountPrice = formatDecimal(
            data.unitTaxPrice * data.productNum,
          );
          data.taxPrice = formatDecimal(
            data.totalAmountPrice - data.noTotalAmountPrice,
          );
          break;
        // 改变价税合计
        case "totalAmountPrice":
          if (data.productNum !== 0) {
            data.unitTaxPrice = formatDecimal(
              data.totalAmountPrice / data.productNum,
            );
            data.noUnitTaxPrice = formatDecimal(
              data.unitTaxPrice / (1 + data.productTax * 0.01),
            );
            data.noTotalAmountPrice = formatDecimal(
              data.noUnitTaxPrice * data.productNum,
            );
            data.taxPrice = formatDecimal(
              data.totalAmountPrice - data.noTotalAmountPrice,
            );
          } else {
            data.unitTaxPrice = 0;
            data.noUnitTaxPrice = 0;
            data.noTotalAmountPrice = 0;
            data.taxPrice = 0;
          }
          break;
      }
    },
    resetField() {
      this.$refs.formRef.resetFields();
      this.form.tableData = [];
    },
    getParams() {
      return this.form.tableData;
    },
    validForm() {
      return new Promise((resolve) => {
        if (this.$refs.formRef) {
          this.$refs.formRef.validate((valid) => {
            resolve(valid);
          });
        } else {
          resolve(true);
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.total-container {
  font-size: 14px;
  .total-content {
    color: rgba(0, 0, 0, 0.65);
  }
  .total-name {
    font-weight: 700;
  }
}
</style>
