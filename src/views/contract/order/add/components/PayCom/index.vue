<template>
  <div>
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="auto"
      size="small"
      label-position="right"
    >
      <search-table-content
        class="full-width"
        ref="searchTableRef"
        :columns="columns"
        :tableData="form.tableData"
        :table-loading="loading"
        :tablePage="tablePage"
        @reset="reset"
        @query="query"
        @pagination="pagination"
        :isFooter="false"
        :inner="true"
      >
        <template v-slot:actions>
          <action-btns :btns="actionBtns" />
        </template>
        <template #index="{ data, index }">
          {{ index + 1 }}
        </template>
        <template #stageName="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.stageName`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.stageName"
          >
            <dict-select
              v-model="data.stageName"
              :options="dictData.payment_stages"
              placeholder="请选择"
              dictName="付款条件_阶段"
              dictCode="payment_stages"
              clearable
              @queryDictList="getDictList"
            />
          </sw-form-item>
        </template>
        <template #paymentPercent="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.paymentPercent`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.paymentPercent"
          >
            <el-input
              v-model="data.paymentPercent"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>
        <template #stagePeriod="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.stagePeriod`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.stagePeriod"
          >
            <el-input
              v-model="data.stagePeriod"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>
        <template #paymentCount="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.paymentCount`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.paymentCount"
          >
            <el-input
              v-model="data.paymentCount"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>

        <template #remark="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.remark`"
            label-width="0"
            style="margin-top: 16px"
          >
            <el-input
              v-model="data.remark"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>
        <template #operation="{ data }">
          <operatebtns
            :row="data"
            :btns="operateBtns"
            :limit="operbtnsLimit"
          ></operatebtns>
        </template>
      </search-table-content>
    </el-form>
  </div>
</template>
<script>
import Page from "@/components/searchTableContent/mixins/page";
import { getUuid } from "@/utils/util";
let defaultData = {
  tableData: [],
};
let itemOption = {
  stageName: "",
  paymentPercent: "",
  stagePeriod: "",
  paymentCount: "",
  remark: "",
};

export default {
  mixins: [Page],
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      initQuery: false,
      loading: false,
      form: {
        tableData: [],
      },
      dictData: {
        payment_stages: [],
      },
      rules: {
        stageName: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        stagePeriod: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        paymentCount: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        paymentPercent: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
      },
      actionBtns: [
        {
          name: "新增",
          class: "action-btn blue-color-btn",
          //   icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
      ],
      columns: [
        {
          label: "序号",
          prop: "index",
          render: "index",
          width: "60px",
          align: "center",
        },
        {
          label: "阶段",
          prop: "stageName",
          render: "stageName",
          minWidth: "120px",
        },
        {
          label: "付款比例",
          prop: "paymentPercent",
          render: "paymentPercent",
          minWidth: "120px",
        },
        {
          label: "周期",
          prop: "stagePeriod",
          render: "stagePeriod",
          minWidth: "120px",
        },
        {
          label: "应付金额",
          prop: "paymentCount",
          render: "paymentCount",
          minWidth: "120px",
        },
        {
          label: "备注",
          prop: "remark",
          render: "remark",
          minWidth: "160px",
        },
        {
          label: "操作",
          prop: "operation",
          render: "operation",
          width: "120px",
          align: "center",
          fixed: "right",
        },
      ],
      operateBtns: [
        {
          name: "移除",
          class: "delete-text-btn",
          permi: ["none:none:none"],
          func: this.onDeleteClick,
        },
      ],
      batchAddDialog: {
        dialogVisible: false,
        rowObj: {},
      },
      addDialog: {
        dialogVisible: false,
        rowObj: {},
      },
    };
  },
  beforeMount() {
    this.getDictList();
  },
  watch: {
    data: {
      handler(newVal) {
        this.form.tableData = newVal || [];
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    onAddClick() {
      this.form.tableData.push({
        ...this.cloneDeep(itemOption),
        id: getUuid(),
        // TODO 组织架构
        personId: getUuid(),
        personName: "随便一个名称" + this.form.tableData.length,
      });
      //   this.batchAddDialog.dialogVisible = true;
      //   this.batchAddDialog.rowObj = {};
    },
    onDeleteClick(row) {
      //   this.form.tableData.splice(index, 1);
      this.form.tableData = this.form.tableData.filter(
        (item) => item.id != row.id,
      );
    },
    eventSuccAdd(list) {
      list.forEach((row) => {
        this.form.tableData.push({
          ...this.cloneDeep(itemOption),
        });
      });
    },
    resetField() {
      this.$refs.formRef.resetFields();
      this.form.tableData = [];
    },
    getParams() {
      return this.form.tableData;
    },
    validForm() {
      return new Promise((resolve) => {
        if (this.$refs.formRef) {
          this.$refs.formRef.validate((valid) => {
            resolve(valid);
          });
        } else {
          resolve(true);
        }
      });
    },
  },
};
</script>
