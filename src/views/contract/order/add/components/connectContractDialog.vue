<template>
  <el-dialog
    title="选择合同订单"
    :visible="dialogVisible"
    width="1000px"
    append-to-body
    :close-on-click-modal="false"
    :before-close="cancelClick"
    @open="onOpenDig"
  >
    <search-table-content
      style="height: 500px"
      :table-loading="loading"
      :ref="searchTableRef"
      :tableData="tableData"
      :tablePage="tablePage"
      :columns="columns"
      :isSelected="false"
      @query="query"
      @reset="reset"
      @pagination="pagination"
      :inner="true"
      :heightAuto="false"
    >
      <template #search>
        <search-item label="合同名称" prop="contractName">
          <el-input
            v-model="searchForm.contractName"
            :placeholder="$t('placeholder.place')"
            clearable
          />
        </search-item>
      </template>
      <template #radio="{ data }">
        <el-radio v-model="currentRow" :label="data.contractNum"
          ><span></span
        ></el-radio>
      </template>
    </search-table-content>
    <span slot="footer" class="dialog-footer">
      <el-button
        class="oper-btn cancel-btn"
        @click="cancelClick"
        :disabled="loading"
        >{{ $t("common.cancel") }}</el-button
      >
      <el-button
        type="primary"
        class="oper-btn"
        @click="submitClick"
        :disabled="loading"
        >{{ $t("common.submit") }}</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
import Page from "@/components/searchTableContent/mixins/page";
import { getContractList } from "@/api/contract/order";
export default {
  name: "selectCustomer",
  mixins: [Page],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    rowObj: {
      type: Object,
      default: () => {},
    },
    selectedList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loading: false,
      searchForm: {
        contractName: "",
      },
      columns: [
        {
          label: "",
          render: "radio",
          align: "center",
          width: "50px",
        },
        {
          label: "项目机会跟进编码",
          prop: "projectFollowNum",
        },
        {
          label: "客户名称",
          prop: "customerName",
        },
        {
          label: "合同名称",
          prop: "contractName",
        },
        {
          label: "内部合同编号",
          prop: "contractNum",
        },
        {
          label: "销售人员",
          prop: "salesPerson",
        },
        // {
        //   label: "已退货数量",
        //   prop: "",
        // },
        // {
        //   label: "合计",
        //   prop: "",
        // },
      ],
      selectList: [],
      initQuery: false,
      currentRow: "",
    };
  },
  methods: {
    request: getContractList,

    onOpenDig() {
      this.reset();
    },
    tableLoadData() {
      this.$nextTick(() => {
        const tableRef = this.$refs?.[this.searchTableRef]?.getTableRef();
        console.log(this.selectedList);
        this.selectedList.forEach((item) => {
          this.tableData.forEach((i) => {
            if (i.contractNum == item.contractNum) {
              //   tableRef.toggleRowSelection(i, true);
              this.currentRow = i.contractNum;
            }
          });
        });
      });
    },
    cancelClick() {
      this.$refs[this.searchTableRef].clearSelection();
      this.tableData = [];
      //   this.selectList = [];
      this.currentRow = "";
      this.$emit("eventClose");
    },
    submitClick() {
      if (!this.currentRow) {
        this.errorMsg("请选择合同订单");
        return false;
      }
      let select = this.tableData.find(
        (item) => item.contractNum == this.currentRow,
      );
      this.$emit("eventSucc", select);
      this.cancelClick();
    },
  },
};
</script>
