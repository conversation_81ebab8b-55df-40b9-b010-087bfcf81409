<template>
  <div>
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="auto"
      size="small"
      label-position="right"
    >
      <search-table-content
        class="full-width"
        ref="searchTableRef"
        :columns="columns"
        :tableData="form.tableData"
        :table-loading="loading"
        :tablePage="tablePage"
        @reset="reset"
        @query="query"
        @pagination="pagination"
        :isFooter="false"
        :inner="true"
      >
        <template v-slot:actions>
          <action-btns :btns="actionBtns" />
        </template>
        <template #index="{ data, index }">
          {{ index + 1 }}
        </template>
        <template #personPercent="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.personPercent`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.personPercent"
          >
            <el-input
              v-model="data.personPercent"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>
        <template #remark="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.remark`"
            label-width="0"
            style="margin-top: 16px"
          >
            <el-input
              v-model="data.remark"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>
        <template #operation="{ data }">
          <operatebtns
            :row="data"
            :btns="operateBtns"
            :limit="operbtnsLimit"
          ></operatebtns>
        </template>
      </search-table-content>
    </el-form>
  </div>
</template>
<script>
import Page from "@/components/searchTableContent/mixins/page";
import { getUuid } from "@/utils/util";
let defaultData = {
  tableData: [],
};
let itemOption = {
  personId: "",
  personName: "",
  personPercent: "",
  remark: "",
};

export default {
  mixins: [Page],
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      initQuery: false,
      loading: false,
      form: {
        tableData: [],
      },
      dictData: {},
      rules: {
        personPercent: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
      },
      actionBtns: [
        {
          name: "新增",
          class: "action-btn blue-color-btn",
          //   icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
      ],
      columns: [
        {
          label: "序号",
          prop: "index",
          render: "index",
          width: "60px",
          align: "center",
        },
        {
          label: "人员名称",
          prop: "personName",
          minWidth: "120px",
        },
        {
          label: "人员占比",
          prop: "personPercent",
          render: "personPercent",
          minWidth: "120px",
        },
        {
          label: "备注",
          prop: "remark",
          render: "remark",
          minWidth: "160px",
        },
        {
          label: "操作",
          prop: "operation",
          render: "operation",
          width: "120px",
          align: "center",
          fixed: "right",
        },
      ],
      operateBtns: [
        {
          name: "移除",
          class: "delete-text-btn",
          permi: ["none:none:none"],
          func: this.onDeleteClick,
        },
      ],
      batchAddDialog: {
        dialogVisible: false,
        rowObj: {},
      },
      addDialog: {
        dialogVisible: false,
        rowObj: {},
      },
    };
  },
  beforeMount() {},
  watch: {
    data: {
      handler(newVal) {
        this.form.tableData = newVal || [];
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    onAddClick() {
      this.form.tableData.push({
        ...this.cloneDeep(itemOption),
        id: getUuid(),
        // TODO 组织架构
        personId: getUuid(),
        personName: "随便一个名称" + this.form.tableData.length,
      });
      //   this.batchAddDialog.dialogVisible = true;
      //   this.batchAddDialog.rowObj = {};
    },
    onDeleteClick(row) {
      //   this.form.tableData.splice(index, 1);
      this.form.tableData = this.form.tableData.filter(
        (item) => item.id != row.id,
      );
    },
    eventSuccAdd(list) {
      list.forEach((row) => {
        this.form.tableData.push({
          ...this.cloneDeep(itemOption),
        });
      });
    },
    resetField() {
      this.$refs.formRef.resetFields();
      this.form.tableData = [];
    },
    getParams() {
      return this.form.tableData;
    },
    validForm() {
      return new Promise((resolve) => {
        if (this.$refs.formRef) {
          this.$refs.formRef.validate((valid) => {
            resolve(valid);
          });
        } else {
          resolve(true);
        }
      });
    },
  },
};
</script>
