<template>
  <div class="full-height full-width page-container">
    <smart-search-table-content
      class="full-height full-width"
      ref="searchTableRef"
      :searchForm="searchForm"
      :advanceSearchForm="advanceSearchForm"
      :columns="columns"
      :tableData="tableData"
      :table-loading="loading"
      :tablePage="tablePage"
      @reset="reset"
      @query="queryBase"
      @pagination="pagination"
      :isSelected="true"
      :isAdvance="isAdvance"
      @queryAdvance="queryAdvance"
      @selectionChange="selectionChange"
      :selectable="selectable"
    >
      <template #pageTitle>
        <page-header title="合同订单">
          <!-- <template #titleWarning>{{ $t("pageTitle.apiKey.desc") }}</template> -->
        </page-header>
      </template>
      <template #search>
        <search-item label="合同名称" prop="contractName">
          <el-input
            v-model="searchForm.contractName"
            :placeholder="$t('placeholder.place')"
            clearable
          />
        </search-item>
      </template>
      <template v-slot:actions>
        <batch-action-btns
          :btns="actionBtns"
          :batchBtns="batchBtns"
          :limit="batchLimit"
          :showBatch="selectList.length > 0"
          :getSelectList="() => selectList"
          :isSelectTotal="isSelectTotal"
          :total="tablePage.total"
          @onSelectTotalClick="onSelectTotalClick"
          @clearSelectList="clearSelectList"
        />
      </template>
      <template #operation="{ data }">
        <operatebtns
          :row="data"
          :btns="operateBtns"
          :limit="operbtnsLimit"
        ></operatebtns>
      </template>
    </smart-search-table-content>
  </div>
</template>

<script>
import Page from "@/components/smartSearchTableContent/mixins/page";
import { getContractList, deleteContract } from "@/api/contract/order";
export default {
  mixins: [Page],
  components: {},
  data() {
    return {
      addDialog: {
        dialogVisible: false,
        rowObj: {},
        isEdit: false,
      },
      detailDialog: {
        dialogVisible: false,
        rowObj: {},
      },
      actionBtns: [
        {
          name: "新建",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "查重",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onCheckClick,
          permi: ["none:none:none"],
        },
        {
          name: "导入",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-shangchuan1-line",
          func: this.onImportClick,
          permi: ["none:none:none"],
        },
        {
          name: "导出",
          id: "exportBtnId",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-daochu",
          func: this.onExportClick,
          permi: ["none:none:none"],
        },
      ],
      batchBtns: [
        {
          name: "分配",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
          rule: () => {
            return true;
          },
        },
        {
          name: "删除",
          class: "danger-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "导出",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "共享",
          class: "oper-text-btn",
          icon: "iconfont icon-shangchuan1-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "批量编辑",
          class: "oper-text-btn",
          icon: "iconfont icon-daochu",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "提交审批",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "下载全部附件",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
          rule: () => {
            return true;
          },
        },
      ],
      columns: [
        {
          label: "标的类型",
          prop: "contractTargetType",
        },
        {
          label: "合同类型",
          prop: "contractType",
        },
        {
          label: "实际考核部门",
          prop: "salesAssessmentDept",
        },
        {
          label: "更新时间",
          prop: "updateTime",
        },
        {
          label: "主体选择ID",
          prop: "subjectSelectId",
        },
        {
          label: "是否进入案例库",
          prop: "configContractCase",
        },
        {
          label: "签订日期",
          prop: "signDate",
        },
        {
          label: "客户名称",
          prop: "customerName",
        },
        {
          label: "创建时间",
          prop: "createTime",
        },
        {
          label: "创建者",
          prop: "createId",
        },
        {
          label: "归档日期",
          prop: "archiveDate",
        },
        {
          label: "销售人员部门",
          prop: "salesPersonDept",
        },
        {
          label: "客户id",
          prop: "customerId",
        },
        {
          label: "合同名称",
          prop: "contractName",
        },
        {
          label: "内部合同编号",
          prop: "contractNum",
        },
        {
          label: "订单id",
          prop: "id",
        },
        {
          label: "销售人员",
          prop: "salesPerson",
        },
        {
          label: "主体选择名字",
          prop: "subjectSelectName",
        },
        {
          label: "项目机会跟进编码",
          prop: "projectFollowNum",
        },
        {
          label: "合同标签",
          prop: "contractTag",
        },
        {
          label: "框架合同编号",
          prop: "connectContractNum",
        },
        {
          label: "客户合同编号",
          prop: "customerContractNum",
        },
        {
          label: "合同有效截止日期",
          prop: "contractEndDate",
        },
        {
          label: "是否含有外采产品",
          prop: "thirdPartyProd",
        },
        {
          label: "收入确认凭证",
          prop: "revenueConfirmCert",
        },
        {
          label: "签收/验收条件",
          prop: "conditionsAcceptReceipt",
        },
        {
          label: "订单属性",
          prop: "contractOrderProperty",
        },
        {
          label: "团队成员",
          prop: "teamPersonNames",
        },
        {
          label: "备注",
          prop: "contractRemark",
        },
        {
          label: "合同表述项目信息",
          prop: "contractDescInfo",
        },
        {
          label: "选择源单类型",
          prop: "contractSourceOrderTypey",
        },
        {
          label: "选择源单编码",
          prop: "contractSourceOrderNum",
        },
        {
          label: "已发货金额",
          prop: "contractDeliveryCount",
        },
        {
          label: "未发货金额",
          prop: "contractNoDeliveryCount",
        },
        {
          label: "已开票金额",
          prop: "contractInvoiceCount",
        },
        {
          label: "未开票金额",
          prop: "contractNoInvoiceCount",
        },
        {
          label: "已回款金额",
          prop: "contractPaymentCount",
        },
        {
          label: "应收余额",
          prop: "contractNoPaymentCount",
        },
        {
          label: "操作",
          prop: "operation",
          render: "operation",
          width: "120px",
          align: "center",
          fixed: "right",
        },
      ],
      operateBtns: [
        {
          name: this.$t("common.detail"),
          class: "oper-text-btn",
          permi: ["none:none:none"],
          func: this.onDetailClick,
        },
        {
          name: this.$t("common.edit"),
          class: "oper-text-btn",
          permi: ["none:none:none"],
          func: this.onEditClick,
        },
        {
          name: this.$t("common.delete"),
          class: "delete-text-btn",
          permi: ["none:none:none"],
          func: (row) => {
            this.confirmInfoOperFun(
              this.$t("tips.deleteTip"),
              deleteContract,
              { id: row.id },
              this.$t("msg.delete"),
            );
          },
        },
      ],
      // 查询参数
      searchForm: {
        contractName: "",
      },
      dictData: {
        product_type: [],
        // company_brand: [],
        // is_disable: [],
        // third_party_product: [],
        // select_config: [],
        // push_pdm: [],
      },
    };
  },
  created() {
    this.getDictList();
  },
  methods: {
    request: getContractList,

    onAddClick() {
      this.$router.push({
        name: "contractOrderAdd",
        query: {},
      });
    },
    onCheckClick() {},
    onImportClick() {},
    onExportClick() {},
    onDetailClick(row) {},
    onEditClick(row) {
      this.$router.push({
        name: "contractOrderAdd",
        query: {
          id: row.id,
          name: row.contractName,
        },
      });
    },
  },
  beforeDestroy() {},
};
</script>
