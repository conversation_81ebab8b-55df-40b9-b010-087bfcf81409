<template>
  <content-detail :content="isEdit ? `编辑-${queryData.name}` : '新增'">
    <menu-form-content :typeList="typeList">
      <el-form
        slot="rightContent"
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="auto"
        size="small"
        label-position="right"
      >
        <div class="right-content-item" id="Base">
          <base-title title="基本信息"></base-title>
          <el-row :gutter="20">
            <el-col :span="12">
              <sw-form-item label="内部合同号" prop="baseData.contractNum">
                <el-input
                  v-model="form.baseData.contractNum"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="签约主体" prop="baseData.contractParty">
                <dict-select
                  v-model="form.baseData.contractParty"
                  :options="dictData.subject_select"
                  placeholder="请选择"
                  dictName="签约主体"
                  dictCode="subject_select"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="源单类型" prop="baseData.sourceContractType">
                <dict-select
                  v-model="form.baseData.sourceContractType"
                  :options="dictData.internal_source_order_type"
                  placeholder="请选择"
                  dictName="内部合同-源单类型"
                  dictCode="internal_source_order_type"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col
              :span="12"
              v-if="form.baseData.sourceContractType == '合同订单'"
            >
              <sw-form-item label="合同订单" prop="baseData.sourceContractId">
                <el-input
                  v-model="form.baseData.sourceContractId"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <sw-form-item label="合同订单" prop="baseData.sourceContractName">
                <el-input
                  v-model="form.baseData.sourceContractName"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col> -->
            <el-col :span="12">
              <sw-form-item label="销售负责人" prop="baseData.salesManagerId">
                <el-input
                  v-model="form.baseData.salesManagerId"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="销售负责人部门"
                prop="baseData.salesManagerDept"
              >
                <el-input
                  v-model="form.baseData.salesManagerDept"
                  placeholder=""
                  disabled
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="24">
              <sw-form-item label="备注" prop="baseData.remark">
                <el-input
                  type="textarea"
                  maxlength="1000"
                  show-word-limit
                  rows="4"
                  v-model="form.baseData.remark"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="Product">
          <base-title title="产品信息"></base-title>
          <el-row :gutter="20">
            <el-col :span="23" :offset="1">
              <ProductCom
                :data="form.productDatas"
                ref="ProductCom"
              ></ProductCom>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="Attach">
          <base-title title="附件"></base-title>
          <el-row :gutter="20">
            <el-col :span="23" :offset="1">
              <AttachCom
                :data="form.attachIds"
                ref="AttachCom"
                :attachs="form.attachs"
              ></AttachCom>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </menu-form-content>
    <div slot="footer" class="page-content-footer">
      <el-button
        class="oper-btn cancel-btn mgr10"
        @click="onCloseClick"
        :loading="loading"
        >{{ $t("common.cancel") }}</el-button
      >
      <el-button
        type="primary"
        class="oper-btn"
        :loading="loading"
        @click="onSubmitClick"
        >{{ $t("common.submit") }}</el-button
      >
    </div>
  </content-detail>
</template>
<script>
import {
  getInterContractDetail,
  addInterContract,
  editInterContract,
} from "@/api/contract/internal";
import ProductCom from "./components/ProductCom/index.vue";
import AttachCom from "./components/AttachCom/index.vue";
let defaultInfo = {
  baseData: {
    contractNum: "",
    contractParty: "",
    sourceContractType: "",
    sourceContractId: "",
    sourceContractName: "",
    salesManagerId: "",
    salesManagerDept: "",
    remark: "",
  },
  productDatas: [],
  attachIds: [],
  attachs: [],
};
let productOption = {};
let attachOption = {};
export default {
  name: "contractInternalAdd",
  components: {
    ProductCom,
    AttachCom,
  },
  data() {
    return {
      isEdit: false,
      loading: false,
      dictData: {
        internal_source_order_type: [],
        subject_select: [],
      },
      typeList: [
        { label: "基本信息", value: "Base" },
        { label: "产品信息", value: "Product" },
        { label: "附件", value: "Attach" },
      ],
      form: {},
      rules: {
        name: [{ required: true, message: "请输入", trigger: "blur" }],
      },
      customerList: [],
      loading1: false,
    };
  },
  computed: {
    queryData() {
      return this.$route.query;
    },
  },
  created() {
    this.isEdit = !!this.$route.query.id;
    this.initData();
    this.getDictList();
  },

  methods: {
    onChangeCustomer(val) {
      let currentObj = this.customerList.find((item) => item.id == val);
      this.form.customerName = currentObj.customerName;
    },
    queryDictList() {
      console.log("重新获取字典");
      this.getDictList();
    },
    initData() {
      this.form = this.cloneDeep(defaultInfo);
      if (this.isEdit) {
        getInterContractDetail({ id: this.queryData.id }).then((res) => {
          this.form = {
            ...this.cloneDeep(defaultInfo),
            ...res.data,
            productDatas: res.data.productDatas || [],
            attachIds: res.data.attachIds || [],
            attachs: res.data.attachs || [],
          };
        });
      }
    },

    onCloseClick() {
      this.$refs.formRef?.resetFields();
      this.refList.forEach((item) => {
        const component = this.$refs[item.refName];
        if (component && component.resetField) {
          component.resetField();
        }
      });
      this.$router.go(-1);
    },
    onSubmitClick() {
      const mainFormValidate = new Promise((resolve) => {
        this.$refs.formRef.validate((valid) => {
          resolve(valid);
        });
      });
      const componentValidates = this.refList.map((item) => {
        const component = this.$refs[item.refName];
        if (!component || !component.validForm) {
          return Promise.resolve(true);
        }
        return new Promise((resolve) => {
          const result = component.validForm();
          if (typeof result === "boolean") {
            resolve(result);
          } else if (result instanceof Promise) {
            resolve(result);
          } else {
            resolve(true);
          }
        });
      });
      Promise.all([mainFormValidate, ...componentValidates]).then((results) => {
        const isAllValid = results.every((result) => result === true);
        if (isAllValid) {
          this.handleAsyncFormSubmission();
        }
      });
    },
    async handleAsyncFormSubmission() {
      try {
        let request = this.isEdit ? editInterContract : addInterContract;
        this.loading = true;
        let params = this.cloneDeep(this.form);
        for (const item of this.refList) {
          const component = this.$refs[item.refName];
          if (component && component.getParams) {
            try {
              const result = await Promise.resolve(component.getParams());
              params[item.labelName] = result;
            } catch (paramError) {
              // 如果某个组件的getParams抛出错误，则停止执行
              return;
            }
          }
        }
        if (params.productDatas.length == 0) {
          this.errorMsg("请填写产品订单信息");
          return false;
        }
        const res = await request(params);
        this.successMsg(this.isEdit ? "编辑成功" : "新增成功");
        this.onCloseClick();
      } catch (error) {
        console.error("提交失败:", error);
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>
<style lang="scss" scoped></style>
