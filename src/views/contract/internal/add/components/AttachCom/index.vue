<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="auto"
    label-position="right"
    @submit.native.prevent
  >
    <sw-form-item
      label="附件"
      prop="fileList"
      ref="uploadFormRef"
      :class="false ? 'is-required' : ''"
    >
      <el-upload
        ref="uploadRef"
        class="upload-demo"
        :auto-upload="false"
        action=""
        :on-remove="onRemove"
        :on-change="onChange"
        :file-list="form.fileList"
        :accept="fileType"
        :limit="0"
        multiple
      >
        <el-button
          class="action-btn white-color-btn"
          icon="el-icon-upload2"
          type="primary"
          >{{ $t("common.clickUpload") }}</el-button
        >
        <div
          class="el-upload__tip text-info"
          style="word-break: break-all"
          slot="tip"
          v-if="fileType"
        >
          {{ $t("tips.uploadFileTip", [fileType, "500MB"]) }}
        </div>
      </el-upload>
    </sw-form-item>
  </el-form>
</template>
<script>
import {} from "@/utils/validate";
import { cloneDeep } from "lodash";
import { getFileId } from "@/api/attach/index";
import { getUuid } from "@/utils/util";

let defaultOptions = {
  fileList: [],
  idList: [],
};
export default {
  props: {
    data: {
      type: Array,
      default: () => {
        return [];
      },
    },
    attachs: {
      type: Array,
      default: () => {
        return [
          // {id:'xxx',fileName:'xxx'}
        ];
      },
    },
  },
  data() {
    let checkFileList = (rule, value, callback) => {
      if (this.form.fileList && this.form.fileList.length > 0) {
        callback();
      } else {
        callback(new Error("请上传附件"));
      }
    };
    return {
      form: cloneDeep(defaultOptions),
      fileType:
        ".doc,.docx,.xls,.xlsx,.csv,.ppt,.pptx,.pdf,.txt,.png,.jpg,.jpeg,.gif,.bmp,.ico,.rar,.zip,.iso,.chm,.chw,.dat,.dot,.dps,.dpt,.et,.ett,.html,.ini,.js,.ptf,.pot,.pps,.rtf,.sql,.wps,.wpt,.xml,.xlt,.eml,.msg,.dwg,.vsd,.sn,.mpp,.ai,.mp4",
      rules: {
        // fileList: [{ validator: checkFileList, trigger: "blur" }],
      },
      oldForm: {},
    };
  },
  computed: {},
  watch: {
    attachs: {
      handler(newVal) {
        if (newVal && Array.isArray(newVal) && newVal.length > 0) {
          this.form.fileList = newVal.map((item) => {
            return {
              name: item.fileName || item.name || "未知文件",
              url: item.url || "",
              id: item.id,
              status: "success", // 设置为已上传状态
              uid: getUuid(),
            };
          });
          this.form.idList = newVal.map((item) => item.id).filter((id) => id);
        } else {
          this.form.fileList = [];
          this.form.idList = [];
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    resetField() {
      this.$refs.formRef.resetFields();
      this.form = cloneDeep(defaultOptions);
    },
    onRemove(file, fileList) {
      this.form.fileList = fileList.map((item) => {
        if (item.raw) {
          return item.raw;
        }
        return item;
      });
    },
    // 两种文件形式，一是通过attachs回显，一是通过el-upload上传的真实文件
    onChange(file, fileList) {
      if (file.raw) {
        let newFile = file.raw;
        if (!this.judgeFile(newFile, true)) {
          this.$nextTick(() => {
            this.$refs.uploadRef?.abort(file); // 取消上传
            // 重新设置文件列表为校验通过的文件
            const validFiles = fileList.filter((f) => f !== file);
            this.form.fileList = validFiles.map((item) => {
              return item.raw || item;
            });
          });
          return false;
        }

        this.form.fileList = fileList.map((item) => {
          return item.raw || item;
        });
      } else {
        this.form.fileList = fileList.map((item) => {
          return item.raw || item;
        });
      }

      // 上传后校验不消失问题
      this.$refs.uploadFormRef?.$children[0]?.clearValidate();
    },
    judgeFile(file, isReset = false) {
      let flag = true;
      let fileNames = file.name.split(".");
      let type = fileNames[fileNames.length - 1];
      let fileTypeList = this.fileType
        .split(",")
        .map((item) => item.replace(".", ""));
      if (!fileTypeList.includes(type)) {
        this.errorMsg(this.$t("tips.uploadFileType", [this.fileType]));
        if (isReset) {
          this.form.fileList = [];
        }
        return false;
      }
      //   let fileSize = file.size / 1024 / 1024 / 1024;
      let fileSize = file.size / 1024 / 1024;
      if (fileSize > 500) {
        this.errorMsg(this.$t("tips.fileSize", ["500MB"]));
        if (isReset) {
          this.form.fileList = [];
        }
        flag = false;
      }
      return flag;
    },
    removeFile() {
      this.$refs.uploadRef?.clearFiles();
      this.form.fileList = [];
    },
    async getParams() {
      // 分离新文件和已存在的文件
      const newFiles = this.form.fileList.filter(
        (file) => file instanceof File,
      );
      const existingFiles = this.form.fileList.filter(
        (file) => !(file instanceof File),
      );

      try {
        // 处理新上传的文件
        let newFileIds = [];
        if (newFiles.length > 0) {
          const promises = newFiles.map((item) => {
            let formData = new FormData();
            formData.append("file", item);
            console.log("file", item);
            return getFileId(formData);
          });

          const results = await Promise.all(promises);
          newFileIds = results.map((res) => res.data);
        }

        // 获取已存在文件的ID
        const existingFileIds = existingFiles
          .map((file) => file.id)
          .filter((id) => id);

        // 合并所有文件ID
        const idList = [...existingFileIds, ...newFileIds];
        this.form.idList = idList;
        return idList;
      } catch (error) {
        throw new Error("附件上传失败: " + (error.message || "未知错误"));
      }
    },
    validForm() {
      return new Promise((resolve) => {
        if (this.$refs.formRef) {
          this.$refs.formRef.validate((valid) => {
            resolve(valid);
          });
        } else {
          resolve(true);
        }
      });
    },
  },
};
</script>
