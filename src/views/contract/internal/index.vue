<template>
  <div class="full-height full-width page-container">
    <smart-search-table-content
      class="full-height full-width"
      ref="searchTableRef"
      :searchForm="searchForm"
      :advanceSearchForm="advanceSearchForm"
      :columns="columns"
      :tableData="tableData"
      :table-loading="loading"
      :tablePage="tablePage"
      @reset="reset"
      @query="queryBase"
      @pagination="pagination"
      :isSelected="true"
      :isAdvance="isAdvance"
      @queryAdvance="queryAdvance"
      @selectionChange="selectionChange"
      :selectable="selectable"
    >
      <template #pageTitle>
        <page-header title="内部关联合同">
          <!-- <template #titleWarning>{{ $t("pageTitle.apiKey.desc") }}</template> -->
        </page-header>
      </template>
      <template #search>
        <search-item label="内部合同号" prop="contractNum">
          <el-input
            v-model="searchForm.contractNum"
            :placeholder="$t('placeholder.place')"
            clearable
          />
        </search-item>
      </template>
      <template v-slot:actions>
        <batch-action-btns
          :btns="actionBtns"
          :batchBtns="batchBtns"
          :limit="batchLimit"
          :showBatch="selectList.length > 0"
          :getSelectList="() => selectList"
          :isSelectTotal="isSelectTotal"
          :total="tablePage.total"
          @onSelectTotalClick="onSelectTotalClick"
          @clearSelectList="clearSelectList"
        />
      </template>
      <template #operation="{ data }">
        <operatebtns
          :row="data"
          :btns="operateBtns"
          :limit="operbtnsLimit"
        ></operatebtns>
      </template>
    </smart-search-table-content>
  </div>
</template>

<script>
import Page from "@/components/smartSearchTableContent/mixins/page";
import {
  getInterContractList,
  deleteInterContract,
} from "@/api/contract/internal";
export default {
  mixins: [Page],
  components: {},
  data() {
    return {
      addDialog: {
        dialogVisible: false,
        rowObj: {},
        isEdit: false,
      },
      detailDialog: {
        dialogVisible: false,
        rowObj: {},
      },
      actionBtns: [
        {
          name: "新建",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "查重",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onCheckClick,
          permi: ["none:none:none"],
        },
        {
          name: "导入",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-shangchuan1-line",
          func: this.onImportClick,
          permi: ["none:none:none"],
        },
        {
          name: "导出",
          id: "exportBtnId",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-daochu",
          func: this.onExportClick,
          permi: ["none:none:none"],
        },
      ],
      batchBtns: [
        {
          name: "分配",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
          rule: () => {
            return true;
          },
        },
        {
          name: "删除",
          class: "danger-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "导出",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "共享",
          class: "oper-text-btn",
          icon: "iconfont icon-shangchuan1-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "批量编辑",
          class: "oper-text-btn",
          icon: "iconfont icon-daochu",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "提交审批",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "下载全部附件",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
          rule: () => {
            return true;
          },
        },
      ],
      columns: [
        {
          label: "主键ID",
          prop: "id",
        },
        {
          label: "签约主体",
          prop: "contractParty",
        },
        {
          label: "内部合同号",
          prop: "contractNum",
        },
        {
          label: "合同订单的类型",
          prop: "sourceContractType",
        },
        // {
        //   label: "合同订单ID",
        //   prop: "sourceContractId",
        // },
        {
          label: "合同订单",
          prop: "sourceContractName",
        },
        {
          label: "销售负责人ID",
          prop: "salesManagerId",
        },
        {
          label: "销售负责人部门",
          prop: "salesManagerDept",
        },
        {
          label: "备注",
          prop: "remark",
        },
        {
          label: "创建者",
          prop: "createId",
        },
        {
          label: "创建时间",
          prop: "createTime",
        },
        {
          label: "更新时间",
          prop: "updateTime",
        },
        {
          label: "操作",
          prop: "operation",
          render: "operation",
          width: "120px",
          align: "center",
          fixed: "right",
        },
      ],
      operateBtns: [
        {
          name: this.$t("common.detail"),
          class: "oper-text-btn",
          permi: ["none:none:none"],
          func: this.onDetailClick,
        },
        {
          name: this.$t("common.edit"),
          class: "oper-text-btn",
          permi: ["none:none:none"],
          func: this.onEditClick,
        },
        {
          name: this.$t("common.delete"),
          class: "delete-text-btn",
          permi: ["none:none:none"],
          func: (row) => {
            this.confirmInfoOperFun(
              this.$t("tips.deleteTip"),
              deleteInterContract,
              { paramList: [row.id] },
              this.$t("msg.delete"),
            );
          },
        },
      ],
      // 查询参数
      searchForm: {
        contractNum: "",
      },
      dictData: {
        product_type: [],
        // company_brand: [],
        // is_disable: [],
        // third_party_product: [],
        // select_config: [],
        // push_pdm: [],
      },
    };
  },
  created() {
    this.getDictList();
  },
  methods: {
    request: getInterContractList,

    onAddClick() {
      this.$router.push({
        name: "contractInternalAdd",
        query: {},
      });
    },
    onCheckClick() {},
    onImportClick() {},
    onExportClick() {},
    onDetailClick(row) {},
    onEditClick(row) {
      this.$router.push({
        name: "contractInternalAdd",
        query: {
          id: row.id,
          name: row.contractNum,
        },
      });
    },
  },
  beforeDestroy() {},
};
</script>
