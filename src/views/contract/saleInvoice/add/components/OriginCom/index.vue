<template>
  <div>
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="auto"
      size="small"
      label-position="right"
    >
      <search-table-content
        class="full-width"
        ref="searchTableRef"
        :columns="columns"
        :tableData="form.tableData"
        :table-loading="loading"
        :tablePage="tablePage"
        @reset="reset"
        @query="query"
        @pagination="pagination"
        :isFooter="false"
        :inner="true"
      >
        <!-- <template v-slot:actions>
          <action-btns :btns="actionBtns" />
        </template> -->
        <template #index="{ data, index }">
          {{ index + 1 }}
        </template>

        <template #operation="{ data }">
          <operatebtns
            :row="data"
            :btns="operateBtns"
            :limit="operbtnsLimit"
          ></operatebtns>
        </template>
      </search-table-content>
    </el-form>
  </div>
</template>
<script>
import Page from "@/components/searchTableContent/mixins/page";
import { getUuid } from "@/utils/util";
import { getApplyInfoByContract } from "@/api/contract/saleInvoice";
let defaultData = {
  tableData: [],
};
let itemOption = {
  recentInvoiceDate: "",
  applyAmount: "",
  invoicedAmount: "",
  contractAmount: "",
  archiveDate: "",
  sourceOrderNo: "",
};

export default {
  mixins: [Page],
  props: {
    // 回显
    data: {
      type: Array,
      default: () => [],
    },
    // 赋值
    contractList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      initQuery: false,
      loading: false,
      form: {
        tableData: [],
      },
      dictData: {
        support_services: [],
      },
      rules: {},
      actionBtns: [
        {
          name: "新增",
          class: "action-btn blue-color-btn",
          //   icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
      ],
      columns: [
        {
          label: "序号",
          prop: "index",
          render: "index",
          width: "60px",
          align: "center",
        },
        {
          label: "源单",
          prop: "sourceOrderNo",
          minWidth: "120px",
        },
        {
          label: "归档日期（计入考核）",
          prop: "archiveDate",
          minWidth: "120px",
        },
        {
          label: "金额",
          prop: "contractAmount",
          minWidth: "120px",
        },
        {
          label: "已开票金额",
          prop: "invoicedAmount",
          minWidth: "120px",
        },
        {
          label: "已提申请金额",
          prop: "applyAmount",
          minWidth: "120px",
        },
        {
          label: "最近开票日期",
          prop: "recentInvoiceDate",
          minWidth: "120px",
        },

        // {
        //   label: "操作",
        //   prop: "operation",
        //   render: "operation",
        //   width: "120px",
        //   align: "center",
        //   fixed: "right",
        // },
      ],
      operateBtns: [
        {
          name: "移除",
          class: "delete-text-btn",
          permi: ["none:none:none"],
          func: this.onDeleteClick,
        },
      ],
      batchAddDialog: {
        dialogVisible: false,
        rowObj: {},
      },
      addDialog: {
        dialogVisible: false,
        rowObj: {},
      },
    };
  },
  beforeMount() {
    this.getDictList();
  },
  watch: {
    // data: {
    //   handler(newVal) {
    //     this.form.tableData = newVal || [];
    //   },
    //   immediate: true,
    //   deep: true,
    // },
  },
  methods: {
    onAddClick() {
      this.form.tableData.push({
        ...this.cloneDeep(itemOption),
        id: getUuid(),
      });
      //   this.batchAddDialog.dialogVisible = true;
      //   this.batchAddDialog.rowObj = {};
    },
    onDeleteClick(row) {
      //   this.form.tableData.splice(index, 1);
      this.form.tableData = this.form.tableData.filter(
        (item) => item.id != row.id,
      );
    },
    //  父组件调用 不需要调用接口挨个获取，直接赋值
    initTableData(list) {
      this.form.tableData = list;
    },
    // 父组件调用 数据仅需要展示，因此通过合同编号获取全部源单信息，无需判断叠加
    async setTableData(list) {
      this.form.tableData = [];
      await this.eventSuccAdd(list);
    },
    async eventSuccAdd(list) {
      //   let contractNum = list.map((item) => item.contractNum).join(",");
      for (let i = 0; i < list.length; i++) {
        try {
          const row = list[i];
          const res = await getApplyInfoByContract({
            contractNum: row.contractNum,
          });
          this.form.tableData.push({
            ...this.cloneDeep(itemOption),
            sourceOrderNo: row.contractSourceOrderNum,
            archiveDate: row.archiveDate,
            contractAmount: row.contractAmount, // 合同金额？？
            invoicedAmount: row.contractInvoiceCount,
            ...this.cloneDeep(res.data?.length ? res.data[0] : {}),
          });
        } catch (error) {}
      }
    },
    resetField() {
      this.$refs.formRef.resetFields();
      this.form.tableData = [];
    },
    getParams() {
      return this.form.tableData;
    },
    validForm() {
      return new Promise((resolve) => {
        if (this.$refs.formRef) {
          this.$refs.formRef.validate((valid) => {
            resolve(valid);
          });
        } else {
          resolve(true);
        }
      });
    },
  },
};
</script>
