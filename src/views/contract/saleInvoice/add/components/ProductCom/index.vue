<template>
  <div>
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="auto"
      size="small"
      label-position="right"
    >
      <search-table-content
        class="full-width"
        ref="searchTableRef"
        :columns="columns"
        :tableData="form.tableData"
        :table-loading="loading"
        :tablePage="tablePage"
        @reset="reset"
        @query="query"
        @pagination="pagination"
        :isFooter="false"
        :inner="true"
      >
        <!-- <template v-slot:actions>
          <action-btns :btns="actionBtns" />
        </template> -->
        <template #index="{ data, index }">
          {{ index + 1 }}
        </template>
        <!-- 合同产品名称 -->
        <template #contractProductName="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.contractProductName`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.contractProductName || {}"
          >
            <el-input
              v-model="data.contractProductName"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>
        <!-- 合同规格型号 -->
        <template #contractProductType="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.contractProductType`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.contractProductType || {}"
          >
            <el-input
              v-model="data.contractProductType"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>

        <!-- 嵌入式软件名称  -->
        <template #softwareName="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.softwareName`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.softwareName || {}"
          >
            <el-input
              v-model="data.softwareName"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>

        <!-- 税率 productTax-->
        <template #productTax="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.productTax`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.productTax || {}"
          >
            <el-input
              v-model="data.productTax"
              placeholder="请输入"
              clearable
              @blur="(val) => changePrice('productTax', val, data)"
            ></el-input>
          </sw-form-item>
        </template>

        <!-- 数量 productNum -->
        <template #productNum="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.productNum`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.productNum || {}"
          >
            <el-input
              v-model="data.productNum"
              placeholder="请输入"
              clearable
              @blur="(val) => changePrice('productNum', val, data)"
            ></el-input>
          </sw-form-item>
        </template>

        <!-- 单位 productUnit -->
        <template #productUnit="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.productUnit`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.productUnit || {}"
          >
            <el-input
              v-model="data.productUnit"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>

        <!-- 含税单价 unitTaxPrice -->
        <template #unitTaxPrice="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.unitTaxPrice`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.unitTaxPrice || {}"
          >
            <el-input
              v-model="data.unitTaxPrice"
              placeholder="请输入"
              clearable
              @blur="(val) => changePrice('unitTaxPrice', val, data)"
            ></el-input>
          </sw-form-item>
        </template>

        <!-- 不含税单价 noUnitTaxPrice -->
        <template #noUnitTaxPrice="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.noUnitTaxPrice`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.noUnitTaxPrice || {}"
          >
            <el-input
              v-model="data.noUnitTaxPrice"
              placeholder="请输入"
              clearable
              @blur="(val) => changePrice('noUnitTaxPrice', val, data)"
            ></el-input>
          </sw-form-item>
        </template>

        <!-- 价税合计 totalAmountPrice -->
        <template #totalAmountPrice="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.totalAmountPrice`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.totalAmountPrice || {}"
          >
            <el-input
              v-model="data.totalAmountPrice"
              placeholder="请输入"
              clearable
              @blur="(val) => changePrice('totalAmountPrice', val, data)"
            ></el-input>
          </sw-form-item>
        </template>

        <!-- 软件是否可退税 softwareTaxRefund -->
        <template #softwareTaxRefund="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.softwareTaxRefund`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.softwareTaxRefund || {}"
          >
            <el-select
              v-model="data.softwareTaxRefund"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="item in getEnum(['Boolean'])"
                :key="item.value"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </sw-form-item>
        </template>
        <!-- 发票类型 -->
        <template #invoiceType="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.invoiceType`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.invoiceType || {}"
          >
            <dict-select
              v-model="data.invoiceType"
              :options="dictData.invoice_type"
              placeholder="请选择"
              dictName="发票类型"
              dictCode="invoice_type"
              clearable
              @queryDictList="getDictList"
            />
          </sw-form-item>
        </template>
        <!-- 财务分类 -->
        <template #clasificacionContable="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.clasificacionContable`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.clasificacionContable || {}"
          >
            <el-input
              v-model="data.clasificacionContable"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>
        <!-- 税收编码 -->
        <template #taxCode="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.taxCode`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.taxCode || {}"
          >
            <el-input
              v-model="data.taxCode"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>

        <template #description="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.description`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.description || {}"
          >
            <el-input
              v-model="data.description"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>
        <template #operation="{ data }">
          <operatebtns
            :row="data"
            :btns="operateBtns"
            :limit="operbtnsLimit"
          ></operatebtns>
        </template>
      </search-table-content>
      <div class="total-container">
        <div class="total-content">
          <span class="total-name"> 合计： </span>
          <span class="total-desc">
            {{ totalPrice }}
          </span>
        </div>
        <div class="text-danger" style="font-weight: 500">
          开票形式选择
          1自产硬件交付未拆分，客户同意软著开在备注时，开票备注+嵌入式软件名称+空格符拼接总长度不能超过200，现长度：{{
            totalLength
          }}
        </div>
      </div>
    </el-form>
    <batchAddProduct
      :dialogVisible="batchAddDialog.dialogVisible"
      :rowObj="batchAddDialog.rowObj"
      :selectedList="(() => this.form.tableData)()"
      @eventClose="batchAddDialog.dialogVisible = false"
      @eventSucc="eventSuccAdd"
    ></batchAddProduct>
  </div>
</template>
<script>
import Page from "@/components/searchTableContent/mixins/page";
import { getUuid } from "@/utils/util";
import batchAddProduct from "@/views/components/batchAddProduct/index";
import { getProductDetailByU9 } from "@/api/infoSearch/product";
import { checkMaxLen } from "@/utils/validate";
let defaultData = {
  tableData: [],
};
let itemOption = {
  unineCode: "",
  productName: "",
  productCode: "",
  contractProductName: "",
  contractProductType: "",
  softwareName: "",
  contractType: "",
  contractNum: "",
  productNum: "",
  productUnit: "",
  productTax: "",
  taxPrice: "",
  unitTaxPrice: "",
  noUnitTaxPrice: "",
  totalAmountPrice: "",
  noTotalAmountPrice: "",
  invoiceType: "",
  description: "",
  softwareTaxRefund: "",
  clasificacionContable: "",
  taxCode: "",
};

export default {
  mixins: [Page],
  components: {
    batchAddProduct,
  },
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    invoiceRemark: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      initQuery: false,
      loading: false,
      form: {
        tableData: [],
      },
      dictData: {
        invoice_type: [],
      },
      rules: {
        contractProductName: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        contractProductType: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
          { validator: checkMaxLen(40) },
        ],
        softwareName: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        productTax: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        productNum: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        productUnit: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        unitTaxPrice: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        noUnitTaxPrice: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        totalAmountPrice: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        softwareTaxRefund: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],

        paymentCount: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        invoiceType: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        clasificacionContable: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        taxCode: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
      },
      actionBtns: [
        {
          name: "新增",
          class: "action-btn blue-color-btn",
          //   icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "清空",
          class: "action-btn white-color-btn",
          //   icon: "iconfont icon-tianjia-line",
          func: this.resetField,
          permi: ["none:none:none"],
        },
      ],
      columns: [
        {
          label: "序号",
          prop: "index",
          render: "index",
          width: "60px",
          align: "center",
          fixed: "left",
        },
        {
          label: "U9编码",
          prop: "unineCode",
          minWidth: "120px",
          fixed: "left",
        },
        {
          label: "产品名称",
          prop: "productName",
          minWidth: "120px",
          fixed: "left",
        },
        {
          label: "产品编码",
          prop: "productCode",
          minWidth: "120px",
        },
        {
          label: "合同产品名称",
          prop: "contractProductName",
          render: "contractProductName",
          minWidth: "120px",
        },
        {
          label: "合同规格型号",
          prop: "contractProductType",
          render: "contractProductType",
          minWidth: "120px",
        },
        {
          label: "嵌入式软件名称", // 新的
          prop: "softwareName",
          render: "softwareName",
          minWidth: "120px",
        },
        {
          label: "源单类型", // 新的
          prop: "contractType",
          minWidth: "120px",
        },
        {
          label: "源单", // 新的
          prop: "contractNum",
          minWidth: "120px",
        },
        {
          label: "税率（%）",
          prop: "productTax",
          render: "productTax",
          minWidth: "120px",
        },
        {
          label: "数量",
          prop: "productNum",
          render: "productNum",
          minWidth: "120px",
        },
        {
          label: "单位",
          prop: "productUnit",
          render: "productUnit",
          minWidth: "120px",
        },
        {
          label: "含税单价",
          prop: "unitTaxPrice",
          render: "unitTaxPrice",
          minWidth: "120px",
        },
        {
          label: "不含税单价",
          prop: "noUnitTaxPrice",
          render: "noUnitTaxPrice",
          minWidth: "120px",
        },
        {
          label: "价税合计",
          prop: "totalAmountPrice",
          render: "totalAmountPrice",
          minWidth: "120px",
        },
        {
          label: "不含税金额", // 自动计算
          prop: "noTotalAmountPrice",
          minWidth: "120px",
        },
        {
          label: "税额", // 自动计算
          prop: "taxPrice",
          minWidth: "120px",
        },
        {
          label: "发票类型", // 新的
          prop: "invoiceType",
          render: "invoiceType",
          minWidth: "120px",
        },
        {
          label: "描述",
          prop: "description",
          render: "description",
          minWidth: "120px",
        },
        {
          label: "软件是否可退税",
          prop: "softwareTaxRefund",
          render: "softwareTaxRefund",
          minWidth: "120px",
        },
        {
          label: "财务分类", // 新的
          prop: "clasificacionContable",
          render: "clasificacionContable",
          minWidth: "120px",
        },
        {
          label: "税收编码", // 新的
          prop: "taxCode",
          render: "taxCode",
          minWidth: "120px",
        },
      ],
      operateBtns: [
        {
          name: "复制",
          class: "oper-text-btn",
          permi: ["none:none:none"],
          func: this.onCopyClick,
        },
        {
          name: "移除",
          class: "delete-text-btn",
          permi: ["none:none:none"],
          func: this.onDeleteClick,
        },
      ],
      batchAddDialog: {
        dialogVisible: false,
        rowObj: {},
      },
      addDialog: {
        dialogVisible: false,
        rowObj: {},
      },
    };
  },
  computed: {
    totalPrice() {
      if (
        !this.form ||
        !this.form.tableData ||
        !Array.isArray(this.form.tableData)
      ) {
        return 0;
      }
      return this.form.tableData.reduce((sum, item) => {
        const price = parseFloat(item.totalAmountPrice) || 0;
        return sum + price;
      }, 0);
    },
    totalLength() {
      const contents = this.form.tableData.map(
        (product) => product.softwareName,
      );
      // 去重
      const addContent = [...new Set(contents)].join("  "); // 去重并拼接成字符串，元素之间使用空格隔开

      const sRemark = this.invoiceRemark + "  " + addContent; //追加开票备注
      return sRemark.length;
    },
  },
  beforeMount() {
    this.getDictList();
  },
  watch: {
    // data: {
    //   handler(newVal) {
    //     // this.form.tableData = newVal || [];
    //     // this.resetField();
    //     let list = newVal || [];
    //     // 持续叠加，除非父组件调用clear会清空
    //     this.eventSuccAdd(list);
    //   },
    //   immediate: true,
    //   deep: true,
    // },
  },
  methods: {
    getTotalLength() {
      return this.totalLength;
    },
    onAddClick() {
      this.batchAddDialog.dialogVisible = true;
      this.batchAddDialog.rowObj = {};
    },
    onCopyClick(row) {
      this.form.tableData.push({
        ...this.cloneDeep(row),
      });
    },
    onDeleteClick(row) {
      this.form.tableData = this.form.tableData.filter(
        (item) => item.id !== row.id,
      );
    },
    // 父组件调用，赋值产品信息
    initTableData(list) {
      this.form.tableData = list;
    },
    // 父组件调用，叠加产品信息
    async eventSuccAdd(list) {
      for (let i = 0; i < list.length; i++) {
        try {
          const row = list[i];
          const res = await getProductDetailByU9({ unineCode: row.unineCode });
          this.form.tableData.push({
            ...this.cloneDeep(itemOption),
            ...row, // 合同的
            ...this.cloneDeep(res.data || {}), // 产品的
          });
        } catch (error) {}
      }
    },
    changePrice(field, val, data) {
      const toNumber = (value) => {
        const num = parseFloat(value);
        return isNaN(num) ? 0 : num;
      };
      const formatDecimal = (value) => {
        return parseFloat(value.toFixed(6));
      };
      data.taxPrice = toNumber(data.taxPrice);
      data.productNum = toNumber(data.productNum);
      data.unitTaxPrice = toNumber(data.unitTaxPrice);
      data.noUnitTaxPrice = toNumber(data.noUnitTaxPrice);
      data.totalAmountPrice = toNumber(data.totalAmountPrice);
      data.productTax = toNumber(data.productTax);

      switch (field) {
        // 改变税率
        case "productTax":
          data.noUnitTaxPrice = formatDecimal(
            data.unitTaxPrice / (1 + data.productTax * 0.01),
          );
          data.totalAmountPrice = formatDecimal(
            data.unitTaxPrice * data.productNum,
          );
          data.noTotalAmountPrice = formatDecimal(
            data.noUnitTaxPrice * data.productNum,
          );
          data.taxPrice = formatDecimal(
            data.totalAmountPrice - data.noTotalAmountPrice,
          );

          break;
        // 改变数量
        case "productNum":
          data.totalAmountPrice = formatDecimal(
            data.unitTaxPrice * data.productNum,
          );
          data.noTotalAmountPrice = formatDecimal(
            data.noUnitTaxPrice * data.productNum,
          );
          data.taxPrice = formatDecimal(
            data.totalAmountPrice - data.noTotalAmountPrice,
          );
          break;
        // 改变含税单价
        case "unitTaxPrice":
          data.totalAmountPrice = formatDecimal(
            data.unitTaxPrice * data.productNum,
          );
          data.noUnitTaxPrice = formatDecimal(
            data.unitTaxPrice / (1 + data.productTax * 0.01),
          );
          data.noTotalAmountPrice = formatDecimal(
            data.noUnitTaxPrice * data.productNum,
          );
          data.taxPrice = formatDecimal(
            data.totalAmountPrice - data.noTotalAmountPrice,
          );
          break;
        // 改变不含税单价
        case "noUnitTaxPrice":
          data.noTotalAmountPrice = formatDecimal(
            data.noUnitTaxPrice * data.productNum,
          );
          data.unitTaxPrice = formatDecimal(
            data.noUnitTaxPrice * (1 + data.productTax * 0.01),
          );
          data.totalAmountPrice = formatDecimal(
            data.unitTaxPrice * data.productNum,
          );
          data.taxPrice = formatDecimal(
            data.totalAmountPrice - data.noTotalAmountPrice,
          );
          break;
        // 改变价税合计
        case "totalAmountPrice":
          if (data.productNum !== 0) {
            data.unitTaxPrice = formatDecimal(
              data.totalAmountPrice / data.productNum,
            );
            data.noUnitTaxPrice = formatDecimal(
              data.unitTaxPrice / (1 + data.productTax * 0.01),
            );
            data.noTotalAmountPrice = formatDecimal(
              data.noUnitTaxPrice * data.productNum,
            );
            data.taxPrice = formatDecimal(
              data.totalAmountPrice - data.noTotalAmountPrice,
            );
          } else {
            data.unitTaxPrice = 0;
            data.noUnitTaxPrice = 0;
            data.noTotalAmountPrice = 0;
            data.taxPrice = 0;
          }
          break;
      }
    },
    resetField() {
      this.$refs.formRef.resetFields();
      this.form.tableData = [];
    },
    getParams() {
      return this.form.tableData;
    },
    validForm() {
      return new Promise((resolve) => {
        if (this.$refs.formRef) {
          this.$refs.formRef.validate((valid) => {
            console.log(valid);
            resolve(valid);
          });
        } else {
          resolve(true);
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.total-container {
  font-size: 14px;
  .total-content {
    color: rgba(0, 0, 0, 0.65);
  }
  .total-name {
    font-weight: 700;
  }
}
</style>
