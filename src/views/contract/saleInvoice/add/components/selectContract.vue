<template>
  <el-dialog
    title="选择合同订单-产品"
    :visible="dialogVisible"
    width="1000px"
    append-to-body
    :close-on-click-modal="false"
    :before-close="cancelClick"
    @open="onOpenDig"
  >
    <search-table-content
      class="contract-product-table"
      style="height: 500px"
      :table-loading="loading"
      :ref="searchTableRef"
      :tableData="tableData"
      :tablePage="tablePage"
      :columns="columns"
      :isSelected="true"
      @query="query"
      @reset="reset"
      @pagination="pagination"
      @selectionChange="selectionChange"
      :inner="true"
      :heightAuto="false"
      :selectable="selectable"
      :span-method="objectSpanMethod"
    >
      <template #search>
        <search-item label="合同名称" prop="contractName">
          <el-input
            v-model="searchForm.contractName"
            :placeholder="$t('placeholder.place')"
            clearable
          />
        </search-item>
      </template>
    </search-table-content>
    <span slot="footer" class="dialog-footer">
      <el-button
        class="oper-btn cancel-btn"
        @click="cancelClick"
        :disabled="loading"
        >{{ $t("common.cancel") }}</el-button
      >
      <el-button
        type="primary"
        class="oper-btn"
        @click="submitClick"
        :disabled="loading"
        >{{ $t("common.submit") }}</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
import Page from "@/components/searchTableContent/mixins/page";
import { getContractProductList } from "@/api/contract/order";

export default {
  name: "batchAddContract",
  mixins: [Page],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    rowObj: {
      type: Object,
      default: () => {},
    },
    selectedList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loading: false,
      searchForm: {
        contractName: "",
        customerId: "", // 如果selectedList中有数据，则需要查询同一个客户底下的数据
      },
      columns: [
        {
          label: "项目机会跟进编码",
          prop: "projectFollowNum",
        },
        {
          label: "客户名称",
          prop: "customerName",
        },
        {
          label: "合同名称",
          prop: "contractName",
        },
        {
          label: "内部合同编号",
          prop: "contractNum",
        },
        {
          label: "销售人员",
          prop: "salesPerson",
        },
        {
          label: "已发货金额",
          prop: "contractDeliveryCount",
        },
        {
          label: "未发货金额",
          prop: "contractNoDeliveryCount",
        },
        {
          label: "已开票金额",
          prop: "contractInvoiceCount",
        },
        {
          label: "未开票金额",
          prop: "contractNoInvoiceCount",
        },
        {
          label: "已回款金额",
          prop: "contractPaymentCount",
        },
        {
          label: "应收余额",
          prop: "contractNoPaymentCount",
        },
        {
          label: "U9编码",
          prop: "unineCode",
          fixed: "right",
        },
        {
          label: "产品名称",
          prop: "productName",
          fixed: "right",
        },
        {
          label: "产品编码",
          prop: "productCode",
          fixed: "right",
        },
        {
          label: "合同产品名称",
          prop: "contractProductName",
          fixed: "right",
        },
      ],
      selectList: [],
      initQuery: false,
      productLabelList: [], // 产品相关字段
    };
  },
  methods: {
    request: getContractProductList,
    onOpenDig() {
      if (this.selectedList.length) {
        this.searchForm.customerId = this.selectedList[0].customerId;
      } else {
        this.searchForm.customerId = "";
      }
      this.query();
    },
    async query() {
      try {
        this.loading = true;
        const res = await this.doQuery();
        let list = res.data.list ? res.data.list : res.data;
        //   将合同数据展平为表格数据
        this.flattenContractData(list);
        this.tablePage.total = res.data.total;

        if (this.tableLoadData) {
          this.tableLoadData();
        }
        if (this.tablePage.total !== 0 && this.tableData.length === 0) {
          if (this.tablePage.pageNum > 1) {
            this.tablePage.pageNum--;
            this.query();
          }
          return;
        }
      } catch (err) {
      } finally {
        this.loading = false;
        // 表格回到顶部
        if (this.$refs[this.searchTableRef]) {
          this.$refs[
            this.searchTableRef
          ].getTableRef().bodyWrapper.scrollTop = 0;
          this.$refs[this.searchTableRef].getTableRef().doLayout();
        }
      }
    },
    selectable(row) {
      //   let obj = this.selectedList.find((item) => item.id === row.id);
      //   return !obj;
      return true;
    },
    // 表格内容合并
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 多选框不合并
      if (column.type == "selection") {
        return {
          rowspan: 1,
          colspan: 1,
        };
      }
      // 如果是产品相关列，不进行合并
      const isProductColumn = this.productLabelList.find(
        (item) => column.property == item.prop,
      );
      if (isProductColumn) {
        return {
          rowspan: 1,
          colspan: 1,
        };
      }
      // 计算当前行所在合同的合并信息
      const spanInfo = this.getSpanInfo(rowIndex);
      if (spanInfo.isFirstRow) {
        // 当前行是该合同的第一行，需要合并
        return {
          rowspan: spanInfo.rowspan,
          colspan: 1,
        };
      } else {
        // 当前行不是该合同的第一行，不显示（合并到第一行）
        return {
          rowspan: 0,
          colspan: 0,
        };
      }
    },

    // 计算行合并信息
    getSpanInfo(rowIndex) {
      const currentRow = this.tableData[rowIndex];
      if (!currentRow) {
        return { isFirstRow: false, rowspan: 0 };
      }
      // 查找当前行所属合同的第一行位置
      let firstRowIndex = rowIndex;
      let contractRowCount = 1;
      // 向前查找同合同的行
      for (let i = rowIndex - 1; i >= 0; i--) {
        if (this.isSameContract(this.tableData[i], currentRow)) {
          firstRowIndex = i;
        } else {
          break;
        }
      }
      // 向后查找同合同的行
      for (let i = rowIndex + 1; i < this.tableData.length; i++) {
        if (this.isSameContract(this.tableData[i], currentRow)) {
          contractRowCount++;
        } else {
          break;
        }
      }

      return {
        isFirstRow: firstRowIndex === rowIndex,
        rowspan: firstRowIndex === rowIndex ? contractRowCount : 0,
      };
    },

    // 判断两行是否属于同一合同
    isSameContract(row1, row2) {
      return row1.contractNum === row2.contractNum;
    },
    flattenContractData(list) {
      const result = [];
      list.forEach((item) => {
        (item.productDatas || []).forEach((product, index) => {
          result.push({
            ...item, // 合同信息
            ...product, // 产品信息
          });
        });
      });
      // 获取产品相关列、合同相关列
      if (list.length && list[0].productDatas?.length) {
        let keyList = Object.keys(list[0].productDatas[0]);
        this.productLabelList = this.columns.filter((item) =>
          keyList.includes(item.prop),
        );
      } else {
        this.productLabelList = [];
      }
      this.tableData = result;
    },
    // 回显
    tableLoadData() {
      //   this.$nextTick(() => {
      //     const tableRef = this.$refs?.[this.searchTableRef]?.getTableRef();
      //     this.selectedList.forEach((item) => {
      //       this.tableData.forEach((i) => {
      //         if (i.id == item.id) {
      //           tableRef.toggleRowSelection(i, true);
      //         }
      //       });
      //     });
      //   });
    },
    // selectionChange(val) {
    //   this.selectList = val;
    //   },
    selectionChange(val) {
      this.selectList = val;
    },

    getFirstRowIndex(rowIndex) {
      const currentRow = this.tableData[rowIndex];
      let firstRowIndex = rowIndex;

      // 向前查找同合同的行
      for (let i = rowIndex - 1; i >= 0; i--) {
        if (this.isSameContract(this.tableData[i], currentRow)) {
          firstRowIndex = i;
        } else {
          break;
        }
      }

      return firstRowIndex;
    },
    cancelClick() {
      this.$refs[this.searchTableRef].clearSelection();
      this.tableData = [];
      this.selectList = [];
      this.$emit("eventClose");
    },

    submitClick() {
      if (this.selectList.length == 0 && this.selectedList.length == 0) {
        this.errorMsg("请选择合同订单-产品");
        return false;
      }
      // 传入的selectedList可能只包含了合同编码，需要查询出其他信息
      // 合并之前已选择的和现在已选择的
      let allList = [...this.selectedList, ...this.selectList];

      // 选择的合同不能属于多个客户档案
      const uniqueCustomerIds = [
        ...new Set(allList.map((item) => item.customerId)),
      ];
      if (uniqueCustomerIds.length > 1) {
        this.errorMsg(
          "您选择的记录包含多个客户档案的单据，只允许选择同一个客户档案的单据",
        );
        return false;
      }

      // 将选中的产品列表转换为树状结构
      let obj = {};
      //   this.selectList.forEach((item) => {
      //     // 去重
      //     if (this.selectedList.length > 0) {
      //       this.selectedList.forEach((pro) => {
      //         if (pro.productDatas.find((val) => val.id == item.id)) {
      //           return;
      //         }
      //       });
      //     } else if (obj[item.contractNum]) {
      //       obj[item.contractNum].productDatas.push({ ...item });
      //     } else {
      //       obj[item.contractNum] = {
      //         ...item,
      //         productDatas: [{ ...item }],
      //       };
      //     }
      //   });
      this.selectList.forEach((item) => {
        let isDuplicate = false;
        if (this.selectedList.length > 0) {
          for (let pro of this.selectedList) {
            if (
              pro.productDatas &&
              pro.productDatas.find((val) => val.id == item.id)
            ) {
              isDuplicate = true;
              break;
            }
          }
        }
        console.log(isDuplicate);
        if (!isDuplicate) {
          if (obj[item.contractNum]) {
            obj[item.contractNum].productDatas.push({ ...item });
          } else {
            obj[item.contractNum] = {
              ...item,
              productDatas: [{ ...item }],
            };
          }
        }
      });
      const treeList = Object.values(obj);
      this.$emit("eventSucc", treeList);
      this.cancelClick();
    },
  },
};
</script>

<style scoped lang="scss">
// 取消高亮、hover颜色

.contract-product-table {
  // 取消所有类型的选中行颜色
  ::v-deep .el-table tr.current-row > td,
  ::v-deep .el-table tr.current-row:hover > td {
    background-color: transparent !important;
  }

  // 取消所有类型的hover颜色
  ::v-deep .el-table tbody tr:hover > td {
    background-color: transparent !important;
  }

  // 特别针对固定列的样式
  ::v-deep .el-table__fixed .current-row > td,
  ::v-deep .el-table__fixed tr:hover > td,
  ::v-deep .el-table__fixed tr.current-row:hover > td {
    background-color: transparent !important;
  }

  // 特别针对右侧固定列的样式
  ::v-deep .el-table__fixed-right .current-row > td,
  ::v-deep .el-table__fixed-right tr:hover > td,
  ::v-deep .el-table__fixed-right tr.current-row:hover > td {
    background-color: transparent !important;
  }

  // 重置固定列的默认背景色
  ::v-deep .el-table__fixed td,
  ::v-deep .el-table__fixed-right td {
    background-color: #fff !important;
  }
}
</style>
