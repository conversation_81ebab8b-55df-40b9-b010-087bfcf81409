<template>
  <content-detail :content="isEdit ? `编辑` : '新增'">
    <menu-form-content :typeList="typeList">
      <el-form
        slot="rightContent"
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="auto"
        size="small"
        label-position="right"
      >
        <div class="right-content-item" id="Base">
          <base-title title="基本信息"></base-title>
          <el-row :gutter="20">
            <el-col :span="12">
              <sw-form-item label="源单类型" prop="orderType">
                <dict-select
                  v-model="form.orderType"
                  :options="dictData.invoice_order_type"
                  placeholder="请选择"
                  dictName="源单类型-销售发票"
                  dictCode="invoice_order_type"
                  clearable
                  @queryDictList="queryDictList"
                  @change="changeOrderType"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="选择合同" prop="contractNum">
                <add-btn-input
                  v-model="form.contractNum"
                  clearable
                  @open="openSelectContract"
                  @clear="clearContract"
                >
                </add-btn-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="主体名称" prop="subjectSelectName">
                <dict-select
                  v-model="form.subjectSelectName"
                  :options="dictData.subject_select"
                  placeholder="请选择"
                  dictName="主体名称"
                  dictCode="subject_select"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="公司税号" prop="taxNo">
                <el-input
                  v-model="form.taxNo"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="客户名称" prop="customerName">
                <el-input
                  v-model="form.customerName"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="开票日期" prop="invoiceDate">
                <el-date-picker
                  v-model="form.invoiceDate"
                  type="date"
                  placeholder="请选择日期"
                  value-format="yyyy-MM-dd"
                  format="yyyy-MM-dd"
                  clearable
                >
                </el-date-picker>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="发票种类" prop="invoiceLine">
                <dict-select
                  v-model="form.invoiceLine"
                  :options="dictData.invoice_line"
                  placeholder="请选择"
                  dictName="发票种类"
                  dictCode="invoice_line"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="发票张数" prop="invoiceNum">
                <el-input
                  v-model="form.invoiceNum"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="发票申请单据号" prop="invoiceApplyCode">
                <el-input
                  v-model="form.invoiceApplyCode"
                  placeholder="请输入"
                  :disabled="isEdit"
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="合同签订日期" prop="signDate">
                <el-date-picker
                  v-model="form.signDate"
                  type="date"
                  placeholder="请选择日期"
                  value-format="yyyy-MM-dd"
                  format="yyyy-MM-dd"
                  clearable
                >
                </el-date-picker>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="开票进度" prop="invoiceProgress">
                <dict-select
                  v-model="form.invoiceProgress"
                  :options="dictData.invoice_progress"
                  placeholder="请选择"
                  dictName="开票进度"
                  dictCode="invoice_progress"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="是否加急开票" prop="urgentFlag">
                <dict-select
                  v-model="form.urgentFlag"
                  :options="dictData.urgent_flag"
                  placeholder="请选择"
                  dictName="是否加急开票"
                  dictCode="urgent_flag"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="物流公司" prop="logisticsCompany">
                <dict-select
                  v-model="form.logisticsCompany"
                  :options="dictData.logistics_company"
                  placeholder="请选择"
                  dictName="物流公司"
                  dictCode="logistics_company"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="物流单号" prop="logisticsNo">
                <el-input
                  v-model="form.logisticsNo"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="销售负责人" prop="salesPerson">
                <el-input
                  v-model="form.salesPerson"
                  placeholder="请输入"
                  clearable
                ></el-input>
                <!-- TODO -->
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="发票变更、作废" prop="invalidInvoice">
                <el-input
                  v-model="form.invalidInvoice"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="销售部门" prop="salesPersonDept">
                <el-input
                  v-model="form.salesPersonDept"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="已收总额" prop="contractPaymentCount">
                <el-input
                  v-model="form.contractPaymentCount"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="业务进度确认" prop="businessProgress">
                <dict-select
                  v-model="form.businessProgress"
                  :options="dictData.business_progress"
                  placeholder="请选择"
                  dictName="业务进度确认"
                  dictCode="business_progress"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="应收余额" prop="contractNoPaymentCount">
                <el-input
                  v-model="form.contractNoPaymentCount"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="已发货金额" prop="contractDeliveryCount">
                <el-input
                  v-model="form.contractDeliveryCount"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="商务部审批人" prop="operationAudit">
                <el-input
                  v-model="form.operationAudit"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="商务部确认进度" prop="operationProgess">
                <dict-select
                  v-model="form.operationProgess"
                  :options="dictData.operation_progess"
                  placeholder="请选择"
                  dictName="商务部确认进度"
                  dictCode="operation_progess"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="已开票金额（不含本次）"
                prop="contractInvoiceCount"
              >
                <el-input
                  v-model="form.contractInvoiceCount"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="合同金额" prop="contractSum">
                <el-input
                  v-model="form.contractSum"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="未发货金额" prop="contractNoDeliveryCount">
                <el-input
                  v-model="form.contractNoDeliveryCount"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="开票形式" prop="invoiceForm">
                <dict-select
                  v-model="form.invoiceForm"
                  :options="dictData.invoice_form"
                  placeholder="请选择"
                  dictName="开票形式"
                  dictCode="invoice_form"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="开票复核人（销售助理）"
                prop="invoiceReviewer"
              >
                <el-input
                  v-model="form.invoiceReviewer"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="开票人" prop="clerk">
                <el-input
                  v-model="form.clerk"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="发票编号" prop="invoiceNo">
                <el-input
                  v-model="form.invoiceNo"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="是否涉及外采" prop="purchaseFlag">
                <dict-select
                  v-model="form.purchaseFlag"
                  :options="dictData.purchase_flag"
                  placeholder="请选择"
                  dictName="是否涉及外采"
                  dictCode="purchase_flag"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="24">
              <sw-form-item label="开负数发票原因" prop="negativeInvoiceReason">
                <el-input
                  type="textarea"
                  maxlength="200"
                  show-word-limit
                  rows="4"
                  v-model="form.negativeInvoiceReason"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>

            <el-col :span="24">
              <sw-form-item
                label="客户不同意原因说明"
                prop="customerDissentReason"
              >
                <el-input
                  type="textarea"
                  maxlength="200"
                  show-word-limit
                  rows="4"
                  v-model="form.customerDissentReason"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
        <div
          class="right-content-item"
          id="Origin"
          v-if="form.contractNumList.length > 0 && form.orderType == '合同订单'"
        >
          <base-title title="源单信息"></base-title>
          <el-row :gutter="20">
            <el-col :span="23" :offset="1">
              <OriginCom :data="form.sourceOrders" ref="OriginCom"></OriginCom>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="BillInfo">
          <base-title title="开票信息"></base-title>
          <el-row :gutter="20">
            <el-col :span="12">
              <sw-form-item label="公司名称" prop="companyName">
                <el-input
                  v-model="form.companyName"
                  placeholder=""
                  disabled
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="纳税人识别号" prop="taxpayerId">
                <el-input
                  v-model="form.taxpayerId"
                  placeholder=""
                  disabled
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="开户银行" prop="bankName">
                <el-input
                  v-model="form.bankName"
                  placeholder=""
                  disabled
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="银行账户" prop="bankAccount">
                <el-input
                  v-model="form.bankAccount"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="电话" prop="phoneNumber">
                <el-input
                  v-model="form.phoneNumber"
                  placeholder=""
                  disabled
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="企业注册类型" prop="taxpayerType">
                <el-input
                  v-model="form.taxpayerType"
                  placeholder=""
                  disabled
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="开票地址" prop="invoiceAddress">
                <el-input
                  v-model="form.invoiceAddress"
                  placeholder=""
                  disabled
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="24">
              <sw-form-item label="发票备注" prop="invoiceRemark">
                <el-input
                  type="textarea"
                  maxlength="200"
                  show-word-limit
                  rows="4"
                  v-model="form.invoiceRemark"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="三未地址" prop="salerAddressSw">
                <el-input
                  v-model="form.salerAddressSw"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="三未电话" prop="salerTelSw">
                <el-input
                  v-model="form.salerTelSw"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="三未开户行及账号" prop="salerAccountSw">
                <el-input
                  v-model="form.salerAccountSw"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="科友地址" prop="salerAddressKy">
                <el-input
                  v-model="form.salerAddressKy"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="科友电话" prop="salerTelKy">
                <el-input
                  v-model="form.salerTelKy"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="科友开户行及账号" prop="salerAccountKy">
                <el-input
                  v-model="form.salerAccountKy"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="是否显示银行账户" prop="showAccountFlag">
                <dict-select
                  v-model="form.showAccountFlag"
                  :options="dictData.show_account_flag"
                  placeholder="请选择"
                  dictName="是否显示银行账户"
                  dictCode="show_account_flag"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="是否显示地址和电话" prop="showAddrerssFlag">
                <dict-select
                  v-model="form.showAddrerssFlag"
                  :options="dictData.show_addrerss_flag"
                  placeholder="请选择"
                  dictName="是否显示地址和电话"
                  dictCode="show_addrerss_flag"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="是否手动开票" prop="manualInvoiceFlag">
                <dict-select
                  v-model="form.manualInvoiceFlag"
                  :options="dictData.manual_invoice_flag"
                  placeholder="请选择"
                  dictName="是否手动开票"
                  dictCode="manual_invoice_flag"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="Email">
          <base-title title="发票邮寄"></base-title>
          <el-row :gutter="20">
            <el-col :span="24">
              <sw-form-item label="发票邮寄信息" prop="maillingAddress">
                <el-input
                  type="textarea"
                  maxlength="200"
                  show-word-limit
                  rows="4"
                  v-model="form.maillingAddress"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="三未财务开票人邮箱" prop="invoiceEmailSw">
                <el-input
                  v-model="form.invoiceEmailSw"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="科友财务开票人邮箱" prop="invoiceEmailKy">
                <el-input
                  v-model="form.invoiceEmailKy"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="Attach">
          <base-title title="电子发票下载"></base-title>
          <el-row :gutter="20">
            <el-col :span="24">
              <sw-form-item label="备注" prop="remark">
                <el-input
                  type="textarea"
                  maxlength="200"
                  show-word-limit
                  rows="4"
                  v-model="form.remark"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="24">附件 </el-col>
          </el-row>
        </div>

        <div class="right-content-item" id="Dock">
          <base-title title="对接信息"></base-title>
          <el-row :gutter="20">
            <el-col :span="12">
              <sw-form-item label="是否已传U9" prop="u9Flag">
                <el-input
                  v-model="form.u9Flag"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="传U9时间" prop="u9Time">
                <el-input
                  v-model="form.u9Time"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="对应U9单据编号" prop="u9Code">
                <el-input
                  v-model="form.u9Code"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="对接错误信息" prop="u9Error">
                <el-input
                  v-model="form.u9Error"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="是否需要传递航信" prop="nuonuoFlag">
                <el-input
                  v-model="form.nuonuoFlag"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="航信最新同步结果" prop="nuonuoResult">
                <el-input
                  v-model="form.nuonuoResult"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="航信最新同步时间" prop="nuonuoTime">
                <el-input
                  v-model="form.nuonuoTime"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="航信申请序列号" prop="nuonuoSerialNum">
                <el-input
                  v-model="form.nuonuoSerialNum"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="Product">
          <base-title title="产品管理"></base-title>
          <el-row :gutter="20">
            <el-col :span="23" :offset="1">
              <ProductCom
                ref="ProductCom"
                :invoiceRemark="form.invoiceRemark"
              ></ProductCom>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </menu-form-content>
    <div slot="footer" class="page-content-footer">
      <el-button
        class="oper-btn cancel-btn mgr10"
        @click="onCloseClick"
        :loading="loading"
        >{{ $t("common.cancel") }}</el-button
      >
      <el-button
        type="primary"
        class="oper-btn"
        :loading="loading"
        @click="onSubmitClick"
        >{{ $t("common.submit") }}</el-button
      >
    </div>
    <selectContract
      :dialogVisible="contractDialog.dialogVisible"
      :rowObj="contractDialog.rowObj"
      :selectedList="(() => JSON.parse(JSON.stringify(form.contractNumList)))()"
      @eventClose="contractDialog.dialogVisible = false"
      @eventSucc="eventSuccAddContract"
    ></selectContract>
  </content-detail>
</template>
<script>
import {
  getInvoiceDetail,
  addInvoice,
  editInvoice,
} from "@/api/contract/saleInvoice";
import { getCustomerDetail } from "@/api/business/customer";
import { getUuid } from "@/utils/util";
import selectContract from "./components/selectContract.vue";
import { cloneDeep } from "lodash";
import { getSysDict } from "@/api/dict/index.js";
import ProductCom from "./components/ProductCom/index.vue";
import OriginCom from "./components/OriginCom/index.vue";
let defaultInfo = {
  orderType: "合同订单",
  contractNum: "", // 接口真正提交的数据，多条使用逗号隔开
  contractNumList: [], // 原CRM系统运行传多个合同，基本信息中只展示第一个合同的相关信息（这个逻辑后续优化）
  subjectSelectName: "",
  taxNo: "",
  customerName: "",
  invoiceDate: "",
  invoiceLine: "增值税专用电子发票",
  invoiceNum: "",
  invoiceApplyCode: "",
  signDate: "",
  invoiceProgress: "未开票",
  urgentFlag: "否",
  logisticsCompany: "",
  logisticsNo: "",
  salesPerson: "常明福",
  invalidInvoice: "",
  salesPersonDept: "商务部",
  contractPaymentCount: "",
  businessProgress: "",
  contractNoPaymentCount: "",
  contractDeliveryCount: "",
  operationAudit: "张云如",
  operationProgess: "",
  contractInvoiceCount: "",
  contractSum: "",
  contractNoDeliveryCount: "",
  invoiceForm: "",
  invoiceReviewer: "",
  clerk: "",
  invoiceNo: "",
  negativeInvoiceReason: "",
  purchaseFlag: "",
  customerDissentReason: "",
  companyName: "",
  taxpayerId: "",
  bankName: "",
  bankAccount: "",
  phoneNumber: "",
  taxpayerType: "",
  invoiceAddress: "",
  invoiceRemark: "",
  salerAddressSw: "北京市朝阳区创远路34号院1号楼12层1201内1201室",
  salerTelSw: "010-************",
  salerAccountSw: "中国光大银行北京礼士路支行*****************",
  salerAddressKy:
    "广州高新技术产业开发区科学城科学大道162号创意大厦B3区第2层202单元",
  salerTelKy: "020-********",
  salerAccountKy: "建行广州市天河工业园支行44001470513050318576",
  showAccountFlag: "否",
  showAddrerssFlag: "否",
  manualInvoiceFlag: "否",
  maillingAddress: "",
  invoiceEmailSw: "<EMAIL>",
  invoiceEmailKy: "<EMAIL>",
  remark: "",
  u9Flag: "",
  u9Time: "",
  u9Code: "",
  u9Error: "",
  nuonuoFlag: "",
  nuonuoResult: "",
  nuonuoTime: "",
  nuonuoSerialNum: "",
  details: [],
  sourceOrders: [],
};

let channelOption = {
  //   projectId: "",
  //   planNo: "",
  customerId: "",
  customerName: "",
  customerSubjectSelectName: "",
  confirmSignContract: "",
  remark: "",
};
let planOption = {
  //   projectId: "",
  //   planNo: "",
  planContent: "",
};
export default {
  name: "InvoiceAdd",
  components: { selectContract, ProductCom, OriginCom },
  mixins: [],
  data() {
    return {
      contractDialog: {
        dialogVisible: false,
        rowObj: {},
      },
      isEdit: false,
      loading: false,
      dictData: {
        subject_select: [],
        invoice_order_type: [],
        invoice_line: [],
        invoice_progress: [],
        urgent_flag: [],
        logistics_company: [],
        business_progress: [],
        operation_progess: [],
        invoice_form: [],
        purchase_flag: [],
        show_account_flag: [],
        show_addrerss_flag: [],
        manual_invoice_flag: [],
        invoice_type: [],
      },
      typeList: [
        { label: "基本信息", value: "Base" },
        {
          label: "源单信息",
          value: "Origin",
          hidden: () => {
            return (
              this.form.contractNumList.length == 0 ||
              this.form.orderType !== "合同订单"
            );
          },
        },
        { label: "开票信息", value: "BillInfo" },
        { label: "发票邮寄", value: "Email" },
        { label: "电子发票下载", value: "Attach" },
        { label: "对接信息", value: "Dock" },
        { label: "产品管理", value: "Product" },
      ],
      form: this.cloneDeep(defaultInfo),
      rules: {
        orderType: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        contractNum: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        subjectSelectName: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        customerName: [
          {
            required: true,
            message: "请输入",
            trigger: ["blur", "change"],
          },
        ],
        invoiceDate: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        invoiceLine: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        urgentFlag: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        salesPerson: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        invoiceReviewer: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        purchaseFlag: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        invoiceEmailSw: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        invoiceEmailKy: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
      },
      customerList: [],

      initQuery: false,
      batchAddDialog: {
        dialogVisible: false,
        rowObj: {},
      },
      baseContractData: {}, // 基础信息中的合同信息(多个合同则默认第一条)
      refList: [
        {
          refName: "ProductCom",
          labelName: "details",
        },
        { refName: "OriginCom", labelName: "sourceOrders" },
      ],
    };
  },
  computed: {
    queryData() {
      return this.$route.query;
    },
  },
  created() {
    this.isEdit = !!this.$route.query.id;
    this.initData();
    this.getDictList();
  },

  methods: {
    initData() {
      this.form = this.cloneDeep(defaultInfo);
      if (this.isEdit) {
        getInvoiceDetail({ id: this.queryData.id }).then((res) => {
          this.form = {
            ...this.cloneDeep(defaultInfo),
            ...res.data,
            sourceOrders: res.data.sourceOrders || [],
            details: res.data.details || [],
          };
          this.form.contractNumList = res.data.contractNum
            .split(",")
            .map((item) => {
              return {
                contractNum: item,
                productDatas: this.form.details,
              };
            });
          // 赋值源单信息/产品信息
          this.$nextTick(() => {
            if (this.$refs.OriginCom) {
              this.$refs.OriginCom.initTableData(this.form.sourceOrders);
            }
            if (this.$refs.ProductCom) {
              this.$refs.ProductCom.initTableData(this.form.details);
            }
          });
        });
      } else {
        // 开票日期默认选择今天
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, "0");
        const day = String(today.getDate()).padStart(2, "0");
        this.form.invoiceDate = `${year}-${month}-${day}`;
      }
    },
    // 切换源单类型需要重新选择合同，清空合同/产品/源单信息
    changeOrderType(val) {
      this.clearContract();
    },
    // 选择合同订单(多条)
    openSelectContract() {
      this.contractDialog.dialogVisible = true;
    },
    // 叠加 合同/产品/源单
    eventSuccAddContract(list) {
      if (this.form.contractNumList.length > 0) {
        list.forEach((newItem) => {
          const existingItem = this.form.contractNumList.find(
            (item) => item.contractNum === newItem.contractNum,
          );
          // 如果contractNum存在
          if (existingItem) {
            existingItem.productDatas.push(...newItem.productDatas);
          } else {
            this.form.contractNumList.push(newItem);
          }
        });
      } else {
        this.form.contractNumList = list;
      }
      this.form.contractNum = this.form.contractNumList
        .map((item) => item.contractNum)
        .join(",");
      // 产品管理
      let productList = [];
      list.forEach((item) => {
        if (item.productDatas && item.productDatas.length) {
          productList.push(...item.productDatas);
        }
      });
      this.form.details = [...this.form.details, ...productList];
      this.$nextTick(() => {
        if (this.$refs.ProductCom) {
          this.$refs.ProductCom.eventSuccAdd(productList);
        }
        // 源单管理 （仅展示，因此直接通过合同编号重新赋值，无需判断叠加）
        if (this.$refs.OriginCom) {
          this.$refs.OriginCom.setTableData(this.form.contractNumList);
        }
      });
      // 基本信息
      this.baseContractData =
        this.form.contractNumList.length > 0
          ? this.form.contractNumList[0]
          : {};
      // 基本信息中的关联字段
      // 主体、公司税号、客户名称、合同签订日期、已收总额，应收余额，已发货金额，已开票金额、合同金额、未发货金额
      this.form.subjectSelectName = this.baseContractData.subjectSelectName;
      this.form.taxNo = this.baseContractData.taxNo;
      // this.baseContractData.taxNo
      this.form.customerId = this.baseContractData.customerId;
      this.form.customerName = this.baseContractData.customerName;
      this.form.signDate = this.baseContractData.signDate;
      this.form.contractPaymentCount =
        this.baseContractData.contractPaymentCount;
      this.form.contractNoPaymentCount =
        this.baseContractData.contractNoPaymentCount;
      this.form.contractDeliveryCount =
        this.baseContractData.contractDeliveryCount;
      this.form.contractInvoiceCount =
        this.baseContractData.contractInvoiceCount;
      this.form.contractSum = this.baseContractData.contractSum;
      // 合同金额？税号？？？
      this.form.contractNoDeliveryCount =
        this.baseContractData.contractNoDeliveryCount;
      // 根据客户信息关联字段
      // 公司名称、纳税人识别号、开户银行、银行账户、电话、企业注册类型、开票地址
      if (this.form.customerId) {
        getCustomerDetail({ id: this.form.customerId }).then((res) => {
          this.form.companyName = res.data.companyName || "";
          this.form.taxpayerId = res.data.taxpayerId || "";
          this.form.bankName = res.data.bankName || "";
          this.form.bankAccount = res.data.bankAccount || "";
          this.form.phoneNumber = res.data.phoneNumber || "";
          this.form.taxpayerType = res.data.taxpayerType || "";
          this.form.invoiceAddress = res.data.invoiceAddress || "";
        });
      } else {
        this.form.companyName = "";
        this.form.taxpayerId = "";
        this.form.bankName = "";
        this.form.bankAccount = "";
        this.form.phoneNumber = "";
        this.form.taxpayerType = "";
        this.form.invoiceAddress = "";
      }
    },
    clearContract() {
      this.form.contractNumList = [];
      this.form.contractNum = "";
      this.form.details = [];
      this.baseContractData = {};
      this.form.subjectSelectName = "";
      this.form.taxNo = "";
      this.form.customerId = "";
      this.form.customerName = "";
      this.form.signDate = "";
      this.form.contractPaymentCount = "";
      this.form.contractNoPaymentCount = "";
      this.form.contractDeliveryCount = "";
      this.form.contractInvoiceCount = "";
      this.form.contractSum = "";
      this.form.contractNoDeliveryCount = "";
      this.form.companyName = "";
      this.form.taxpayerId = "";
      this.form.bankName = "";
      this.form.bankAccount = "";
      this.form.phoneNumber = "";
      this.form.taxpayerType = "";
      this.form.invoiceAddress = "";
      // 清空合同需清空产品信息和源单
      this.$nextTick(() => {
        this.$refs.ProductCom?.resetField();
        this.$refs.OriginCom?.resetField();
      });
    },
    queryDictList() {
      console.log("重新获取字典");
      this.getDictList();
    },
    onCloseClick() {
      this.$refs.formRef?.resetFields();
      this.$router.go(-1);
    },
    onSubmitClick() {
      const mainFormValidate = new Promise((resolve) => {
        this.$refs.formRef.validate((valid) => {
          resolve(valid);
        });
      });
      const componentValidates = this.refList.map((item) => {
        const component = this.$refs[item.refName];
        if (!component || !component.validForm) {
          return Promise.resolve(true);
        }
        return new Promise((resolve) => {
          const result = component.validForm();
          if (typeof result === "boolean") {
            resolve(result);
          } else if (result instanceof Promise) {
            resolve(result);
          } else {
            resolve(true);
          }
        });
      });
      Promise.all([mainFormValidate, ...componentValidates]).then((results) => {
        const isAllValid = results.every((result) => result === true);
        if (isAllValid) {
          this.handleAsyncFormSubmission();
        }
      });
    },
    async handleAsyncFormSubmission() {
      let length = Number(this.$refs.ProductCom.getTotalLength());
      if (
        length > 200 &&
        this.form.invoiceForm == "1自产硬件交付未拆分，客户同意软著开在备注"
      ) {
        this.errorMsg(
          "开票备注+嵌入式软件名称+空格符拼接总长度不能超过200，现长度：" +
            length,
        );
        return;
      }
      try {
        let request = this.isEdit ? editInvoice : addInvoice;
        this.loading = true;
        let params = this.cloneDeep(this.form);
        for (const item of this.refList) {
          const component = this.$refs[item.refName];
          if (component && component.getParams) {
            try {
              const result = await Promise.resolve(component.getParams());
              params[item.labelName] = result;
            } catch (paramError) {
              // 如果某个组件的getParams抛出错误，则停止执行
              return;
            }
          }
        }
        const res = await request(params);
        this.successMsg(this.isEdit ? "编辑成功" : "新增成功");
        this.onCloseClick();
      } catch (error) {
        console.error("提交失败:", error);
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.oper-icon-btn-box {
  line-height: 32px;
  display: inline-block;
  i {
    cursor: pointer;
  }
  i + i {
    margin-left: 8px;
  }
}
</style>
