<template>
  <div class="full-height full-width page-container">
    <smart-search-table-content
      class="full-height full-width"
      ref="searchTableRef"
      :searchForm="searchForm"
      :advanceSearchForm="advanceSearchForm"
      :columns="columns"
      :tableData="tableData"
      :table-loading="loading"
      :tablePage="tablePage"
      @reset="reset"
      @query="queryBase"
      @pagination="pagination"
      :isSelected="true"
      :isAdvance="isAdvance"
      @queryAdvance="queryAdvance"
      @selectionChange="selectionChange"
      :selectable="selectable"
    >
      <template #pageTitle>
        <page-header title="销售发票">
          <!-- <template #titleWarning>{{ $t("pageTitle.apiKey.desc") }}</template> -->
        </page-header>
      </template>
      <template #search>
        <search-item label="合同编码" prop="contractNum">
          <el-input
            v-model="searchForm.contractNum"
            :placeholder="$t('placeholder.place')"
            clearable
          />
        </search-item>
        <search-item label="发票申请编码" prop="invoiceApplyCode">
          <el-input
            v-model="searchForm.invoiceApplyCode"
            :placeholder="$t('placeholder.place')"
            clearable
          />
        </search-item>
      </template>
      <template v-slot:actions>
        <batch-action-btns
          :btns="actionBtns"
          :batchBtns="batchBtns"
          :limit="batchLimit"
          :showBatch="selectList.length > 0"
          :getSelectList="() => selectList"
          :isSelectTotal="isSelectTotal"
          :total="tablePage.total"
          @onSelectTotalClick="onSelectTotalClick"
          @clearSelectList="clearSelectList"
        />
      </template>
      <template #operation="{ data }">
        <operatebtns
          :row="data"
          :btns="operateBtns"
          :limit="operbtnsLimit"
        ></operatebtns>
      </template>
    </smart-search-table-content>
  </div>
</template>

<script>
import Page from "@/components/smartSearchTableContent/mixins/page";
import { getInvoiceList, deleteInvoice } from "@/api/contract/saleInvoice";
import { getMock } from "@/utils/util";

export default {
  mixins: [Page],
  components: {},
  data() {
    return {
      addDialog: {
        dialogVisible: false,
        rowObj: {},
        isEdit: false,
      },
      detailDialog: {
        dialogVisible: false,
        rowObj: {},
      },
      actionBtns: [
        {
          name: "新建",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "查重",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onCheckClick,
          permi: ["none:none:none"],
        },
        {
          name: "导入",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-shangchuan1-line",
          func: this.onImportClick,
          permi: ["none:none:none"],
        },
        {
          name: "导出",
          id: "exportBtnId",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-daochu",
          func: this.onExportClick,
          permi: ["none:none:none"],
        },
      ],
      batchBtns: [
        {
          name: "分配",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
          rule: () => {
            return true;
          },
        },
        {
          name: "删除",
          class: "danger-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "导出",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "共享",
          class: "oper-text-btn",
          icon: "iconfont icon-shangchuan1-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "批量编辑",
          class: "oper-text-btn",
          icon: "iconfont icon-daochu",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "提交审批",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "下载全部附件",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
          rule: () => {
            return true;
          },
        },
      ],
      columns: [
        {
          label: "对应合同编号",
          prop: "contractNum",
        },
        {
          label: "主体选择",
          prop: "subjectSelectName",
        },
        {
          label: "公司税号",
          prop: "taxNo",
        },
        {
          label: "客户名称",
          prop: "customerName",
        },
        {
          label: "开票日期",
          prop: "invoiceDate",
        },
        {
          label: "发票种类",
          prop: "invoiceLine",
        },
        {
          label: "发票张数",
          prop: "invoiceNum",
        },
        {
          label: "发票申请单据号",
          prop: "invoiceApplyCode",
        },
        {
          label: "合同签订日期",
          prop: "signDate",
        },
        {
          label: "开票进度",
          prop: "invoiceProgress",
        },
        {
          label: "是否加急开票",
          prop: "urgentFlag",
        },
        {
          label: "物流公司",
          prop: "logisticsCompany",
        },
        {
          label: "物流单号",
          prop: "logisticsNo",
        },
        {
          label: "销售负责人",
          prop: "salesPerson",
        },
        {
          label: "发票变更、作废",
          prop: "invalidInvoice",
        },
        {
          label: "销售部门",
          prop: "salesPersonDept",
        },
        {
          label: "已收总额",
          prop: "contractPaymentCount",
        },
        {
          label: "业务进度确认",
          prop: "businessProgress",
        },
        {
          label: "应收余额",
          prop: "contractNoPaymentCount",
        },
        {
          label: "已发货金额",
          prop: "contractDeliveryCount",
        },
        {
          label: "商务部审批人",
          prop: "operationAudit",
        },
        {
          label: "商务部确认进度",
          prop: "operationProgess",
        },
        {
          label: "已开票金额（不含本次）",
          prop: "contractInvoiceCount",
        },
        {
          label: "合同金额",
          prop: "contractSum",
        },
        {
          label: "未发货金额",
          prop: "contractNoDeliveryCount",
        },
        {
          label: "开票形式",
          prop: "invoiceForm",
        },
        {
          label: "开票复核人（销售助理）",
          prop: "invoiceReviewer",
        },
        {
          label: "开票人",
          prop: "clerk",
        },
        {
          label: "发票编号",
          prop: "invoiceNo",
        },
        {
          label: "开负数发票原因",
          prop: "negativeInvoiceReason",
        },
        {
          label: "是否涉及外采",
          prop: "purchaseFlag",
        },
        {
          label: "客户不同意原因说明",
          prop: "customerDissentReason",
        },
        {
          label: "公司名称",
          prop: "companyName",
        },
        {
          label: "纳税人识别号",
          prop: "taxpayerId",
        },
        {
          label: "开户银行",
          prop: "bankName",
        },
        {
          label: "银行账户",
          prop: "bankAccount",
        },
        {
          label: "电话",
          prop: "phoneNumber",
        },
        {
          label: "企业注册类型",
          prop: "taxpayerType",
        },
        {
          label: "开票地址",
          prop: "invoiceAddress",
        },
        {
          label: "发票备注",
          prop: "invoiceRemark",
        },
        {
          label: "三未地址",
          prop: "salerAddressSw",
        },
        {
          label: "三未电话",
          prop: "salerTelSw",
        },
        {
          label: "三未开户行及账号",
          prop: "salerAccountSw",
        },
        {
          label: "科友地址",
          prop: "salerAddressKy",
        },
        {
          label: "科友电话",
          prop: "salerTelKy",
        },
        {
          label: "科友开户行及账号",
          prop: "salerAccountKy",
        },
        {
          label: "是否显示银行账户",
          prop: "showAccountFlag",
        },
        {
          label: "是否显示地址和电话",
          prop: "showAddrerssFlag",
        },
        {
          label: "是否手动开票",
          prop: "manualInvoiceFlag",
        },
        {
          label: "发票邮寄信息",
          prop: "maillingAddress",
        },
        {
          label: "三未财务开票人邮箱",
          prop: "invoiceEmailSw",
        },
        {
          label: "科友财务开票人邮箱",
          prop: "invoiceEmailKy",
        },
        {
          label: "备注",
          prop: "remark",
        },
        {
          label: "详情",
          prop: "details",
        },
        {
          label: "操作",
          prop: "operation",
          render: "operation",
          width: "120px",
          align: "center",
          fixed: "right",
        },
      ],
      operateBtns: [
        // {
        //   name: this.$t("common.detail"),
        //   class: "oper-text-btn",
        //   permi: ["none:none:none"],
        //   func: this.onDetailClick,
        // },
        {
          name: this.$t("common.edit"),
          class: "oper-text-btn",
          permi: ["none:none:none"],
          func: this.onEditClick,
        },
        // {
        //   name: this.$t("common.delete"),
        //   class: "delete-text-btn",
        //   permi: ["none:none:none"],
        //   func: (row) => {
        //     this.confirmInfoOperFun(
        //       this.$t("tips.deleteTip"),
        //       deleteInvoice,
        //       { id: row.id },
        //       this.$t("msg.delete"),
        //     );
        //   },
        // },
      ],
      // 查询参数
      searchForm: {
        contractNum: "",
        invoiceApplyCode: "",
      },
      dictData: {
        subject_select: [],
      },
    };
  },
  created() {
    this.getDictList();
  },
  methods: {
    request: getInvoiceList,

    onAddClick() {
      this.$router.push({
        name: "saleInvoiceAdd",
        query: {},
      });
    },
    onCheckClick() {},
    onImportClick() {},
    onExportClick() {},
    onDetailClick(row) {},
    onEditClick(row) {
      this.$router.push({
        name: "saleInvoiceAdd",
        query: {
          id: row.id,
        },
      });
    },
  },
  beforeDestroy() {},
};
</script>
