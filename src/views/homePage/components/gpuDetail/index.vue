<template>
  <div class="gpu-memory-detail full-width full-height">
    <div class="common-box" v-for="col in panelData.childrenPanel">
      <component
        :is="item.type"
        :style="item.panelStyle || {}"
        v-for="(item, i) in col"
        :ref="item.ref"
        :panelData="item"
      ></component>
    </div>
  </div>
</template>
<script>
import { cloneDeep } from "lodash";
import qiuChart from "./components/qiu/index.vue";
import timerlineCurve from "./components/timerlineCurve/index.vue";
export default {
  name: "gpuDetail",
  components: {
    qiuChart,
    timerlineCurve,
  },
  props: {
    panelData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      chartOption: {},
    };
  },
  mounted() {
    this.panelData.childrenPanel.forEach((list, i) => {
      list.forEach((item, i) => {
        if (this.$refs[item.ref][0] && this.$refs[item.ref][0].setConfig) {
          this.$refs[item.ref][0].setConfig(item.config);
        }
      });
    });
  },
  methods: {
    setData(data) {
      //   data = {
      //     id: "xxx",
      //     usageRate: 66,
      //     memUsageRate: 55,
      //   };
      this.panelData.childrenPanel.forEach((list) => {
        list.forEach((item, i) => {
          if (this.$refs[item.ref][0] && this.$refs[item.ref][0].setData) {
            this.$refs[item.ref][0].setData({
              dataList: [
                {
                  value: data[item.fields.value],
                },
              ],
            });
          }
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.gpu-memory-detail {
  padding: 0 20px 20px;
  display: flex;
  flex-direction: row !important;
  justify-content: space-between;
  .common-box {
    display: flex;
    padding: 15px;
    width: calc(50% - 10px);
    height: 100%;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.15);
  }
}
</style>
