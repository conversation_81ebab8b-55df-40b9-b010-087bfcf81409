<template>
  <div class="full-width full-height">
    <div class="echart" v-if="chartOption?.series?.length > 0">
      <Chart
        class="chart-box"
        ref="ebRingPieChart"
        :options="chartOption"
        autoresize
      ></Chart>
    </div>
    <div class="panel-no-data-flex" v-else>{{ $t("common.noData") }}</div>
  </div>
</template>
<script>
import Chart from "@/components/vueEcharts/ECharts.vue";
import { parseTime } from "@/utils/util";
import { cloneDeep } from "lodash";
import mockData from "./mock.js";
import { options } from "./defaultOptions.js";
export default {
  name: "timerlineCurve",
  components: {
    Chart,
  },
  data() {
    return {
      chartOption: cloneDeep(options),
    };
  },
  props: {
    panelData: {
      type: Object,
      default: () => mockData,
    },
    seriesLength: {
      type: Number,
      default: 39,
    },
  },
  computed: {},
  methods: {
    setConfig(config) {
      let series = [];
      let xData = [];
      let legend = [];
      // 线形图
      let option = {};
      config.forEach((configItem, index) => {
        if (configItem.chartOption) {
          option = configItem.chartOption;
        }
        series.push({
          ...option,
          //   metricsSetCode: configItem.metricsSetCode,
          key: configItem.key,
          name: configItem.name,
          show: configItem.show,
          data: [],
          smooth: configItem?.smooth ?? true, // 是否平滑
          symbol: configItem?.symbol ?? "circle",
          symbolSize: 1,
          type: configItem.chartType,
          itemStyle: {
            normal: {
              color: configItem.color,
            },
          },
          areaStyle: {
            normal: {
              color: {
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: configItem.graphic
                      ? configItem.color + "66"
                      : configItem.color,
                  },
                  {
                    offset: 1,
                    color: configItem.graphic
                      ? configItem.color + "00"
                      : configItem.color,
                  },
                ],
              },
            },
          },
        });
        legend.push(configItem.name);
      });

      this.chartOption.xAxis.name = this.panelData.chartOption.xlabel;
      this.chartOption.xAxis.data = xData;
      this.chartOption.yAxis.name = this.panelData.chartOption.y1label;
      this.chartOption.legend.data = legend;
      this.chartOption.series = series;
    },
    setData(data) {
      let time = parseTime(new Date().getTime(), "{h}:{i}:{s}");
      if (this.chartOption.xAxis.data.length > this.seriesLength) {
        this.chartOption.xAxis.data.splice(0, 1);
      }
      if (!this.chartOption.xAxis.data.includes(time)) {
        this.chartOption.xAxis.data.push(time);
      }
      data.dataList.forEach((dataItem) => {
        // let chartItem = this.chartOption.series.find(
        //   (item) =>
        //     item.key == dataItem.key &&
        //     item.metricsSetCode == dataItem.metricsSetCode,
        // );
        let chartItem = this.chartOption.series[0];
        if (chartItem?.data?.length > this.seriesLength) {
          chartItem?.data.splice(0, 1);
        }
        chartItem?.data?.push(dataItem.value);
      });
      // y轴最大值
      if (this.panelData.chartOption.y1Max) {
        this.chartOption.yAxis.max = this.panelData.chartOption.y1Max;
      }
    },
  },
  mounted() {},
};
</script>
<style lang="scss" scoped>
.echart {
  width: 100%;
  height: 100%;
  .chart-box {
    width: 100%;
    height: 100%;
  }
}
</style>
