const mockData = {
  static: false,
  i: "2",
  type: "timerlineCurve",
  title: "系统CPU使用率",
  hiddenTitle: false,
  chartOption: {
    xlabel: "Time",
    y1label: "%",
  },
  oder: 0,
  w: 100,
  x: 0,
  y: 50,
  h: 30,
  updateFrequency: 3,
  actions: [],
  source: {
    url: "/swmonitor/manage/v1_1/multiplePanel/tablePanelCalculate6",
    method: "post",
    relyFields: [],
    apiParams: {},
  },
  config: [
    {
      width: 1.5,
      font: "12",
      chartType: "line",
      color: "#09bcb7",
      graphic: true,
      smooth: true,
      symbol: "emptyCircle",
      calculationRules: "100-#{value}",
      oid: ".*******.4.1.746364.1.1.3",
      unit: "%",
      round: "#.##",
      name: "系统CPU使用率",
      key: "cpuPercent",
      aggregation: "mean",
    },
  ],
};

export default mockData;
