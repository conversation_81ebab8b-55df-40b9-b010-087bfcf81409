export const options = {
  tooltip: {
    show: true,
    trigger: "axis",
    axisPointer: {
      type: "line",
      lineStyle: {
        opacity: 0,
      },
    },
    padding: [12, 36, 12, 12],
    //  backgroundColor: "#001a5566",
    //  borderColor: "#00dbfd",
    borderWidth: 1,
    textStyle: {
      color: "#888888",
      fontSize: 12,
    },
  },
  grid: {
    left: 20,
    right: 50,
    bottom: 16,
    top: 40,
    containLabel: true,
  },
  color: [],
  xAxis: {
    name: "",
    type: "category",
    axisLabel: {
      color: "#888888",
    },
    nameTextStyle: {
      color: "#888888",
    },
    axisLine: {
      show: true,
      lineStyle: {
        color: "#D0D2D4",
      },
    },
    axisTick: {
      show: false,
      alignWithLabel: true,
    },
    data: [],
  },
  yAxis: {
    name: "",
    type: "value",
    axisLabel: {
      color: "#888888",
    },
    nameTextStyle: {
      color: "#888888",
    },
    axisLine: {
      show: true,
      lineStyle: {
        color: "#D0D2D4",
      },
    },
    splitLine: {
      lineStyle: {
        type: "dashed",
        color: "#E8E8E8",
      },
      show: true,
    },
    splitArea: {
      show: true,
      areaStyle: {
        color: ["transparent"],
      },
    },
    // scale: true
  },
  legend: {
    show: true,
    right: 60,
    top: 0,
    data: [],
    icon: "circle",
    itemWidth: 8,
    textStyle: {
      color: "#888888",
    },
  },
  series: [],
};
