<template>
  <div class="qiu-chart full-width full-height">
    <vue-echarts
      v-if="Object.keys(chartOption).length > 0"
      class="echarts-box"
      :options="chartOption"
      autoresize
    />
  </div>
</template>
<script>
let defaultOption = {
  title: [
    {
      text: "",
      x: "center",
      y: "52%",
      textStyle: {
        fontSize: 12,
        fontWeight: "normal",
        color: "#444444",
      },
    },
  ],
  series: [
    {
      type: "liquidFill",
      radius: "90%",
      center: ["50%", "50%"],
      color: [
        {
          type: "linear",
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: "rgba(0, 121, 251, 0.502)",
            },
            {
              offset: 1,
              color: "rgba(151, 200, 253, 0)",
            },
          ],
          globalCoord: false,
        },
      ],
      data: [],
      backgroundStyle: {
        borderWidth: 0,
        color: "#fff",
      },
      label: {
        normal: {
          position: ["50%", "45%"],
          insideColor: "transparent",
          textStyle: {
            fontSize: 20,
            fontWeight: "bold",
            color: "#444444",
          },
        },
      },
      outline: {
        borderDistance: 0,
        itemStyle: {
          borderWidth: 6,
          borderColor: "#E3ECFF",
          shadowBlur: 1,
          shadowColor: "#fff",
        },
      },
      itemStyle: {
        shadowBlur: 0,
        shadowColor: "#fff",
      },
    },
  ],
};
import { cloneDeep } from "lodash";
import "echarts-liquidfill"; // 水球图
export default {
  name: "qiuChart",
  props: {
    panelData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      chartOption: {},
    };
  },
  methods: {
    setData(data) {
      this.chartOption = cloneDeep(defaultOption);
      let qiuData = [];
      for (let i = 0; i < 2; i++) {
        let value = data.dataList.length > 0 ? data.dataList[0].value : 0;
        qiuData.push({
          value: value > 0 ? value / 100 : 0,
        });
      }
      let colors = this.panelData.colors || [];
      this.chartOption.series[0] = {
        ...this.chartOption.series[0],
        data: qiuData,
        color: [
          {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: colors[0],
              },
              {
                offset: 1,
                color: colors[1],
              },
            ],
            globalCoord: false,
          },
        ],
      };
      this.chartOption.title[0].text = this.panelData.config[0].name;
    },
  },
};
</script>
<style lang="scss" scoped>
.echarts-box {
  width: 100%;
  height: 100%;
}
</style>
