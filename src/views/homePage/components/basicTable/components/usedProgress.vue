<template>
  <div class="progress-box">
    <el-progress :percentage="percentage" :format="format"></el-progress>
  </div>
</template>
<script>
export default {
  props: {
    row: {
      type: Object,
      default: () => {},
    },
    fields: {
      type: Object,
      default: () => {
        return {
          used: "used",
          total: "total",
          percent: "percent",
        };
      },
    },
  },

  data() {
    return {};
  },
  computed: {
    percentage() {
      if (this.row[this.fields.percent]) {
        return Math.floor(this.row[this.fields.percent]);
      } else {
        return 0;
      }
    },
  },
  methods: {
    format() {
      return `${this.row[this.fields.used]}/${this.row[this.fields.total]}`;
    },
  },
};
</script>
<style scoped lang="scss">
.progress-box {
  //   padding: 0 10px;
  ::v-deep .el-progress {
    display: flex;
    align-items: center;
  }
  ::v-deep .el-progress__text {
    font-size: 14px !important;
    color: rgba(0, 0, 0, 0.45);
  }
}
</style>
