<template>
  <div style="display: flex; flex-direction: column">
    <div class="table-container">
      <el-table :data="dataList" height="100%">
        <el-table-column
          v-for="(item, index) in columns"
          :key="index"
          :label="item.label"
          :prop="item.prop"
          :min-width="item.minWidth"
          :width="item.width"
          :align="item.align"
          v-bind="item"
          show-overflow-tooltip
        >
          <template v-if="item.render" #default="{ row, $index }">
            <template v-if="item.render == 'index'">
              {{ $index + 1 }}
            </template>
            <component
              v-else
              :is="renders[item.render].componentName"
              :row="row"
              :fields="renders[item.render].fields"
            />
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import usedProgress from "./components/usedProgress";
export default {
  name: "basicTable",
  mixins: [],
  components: { usedProgress },
  props: {
    panelData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      dataList: [],
      columns: [],
      renders: {},
      dictData: {
        assetTaskStatus: [],
      },
    };
  },
  beforeMount() {},
  mounted() {},
  methods: {
    getData() {},
    setData(data) {
      this.dataList = JSON.parse(JSON.stringify(data.dataList || []));
      this.renders = this.panelData.renders || {};
      this.columns = this.panelData.columns || [];
    },
  },
};
</script>

<style lang="scss" scoped>
.table-container {
  padding: 0 20px 20px;
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  ::v-deep .el-table .el-table__header-wrapper th,
  .el-table .el-table__fixed-header-wrapper th {
    background-color: rgba(0, 0, 0, 0.04);
    border: 1px 0px 1px 0px solid #e8e8e8;
  }
}
</style>
