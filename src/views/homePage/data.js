import config from "@/api/configs";
import i18n from "@/i18n";
export const demoAllPanelData = [
  {
    componentName: "basicTable",
    ref: "gpuTable",
    w: 100,
    h: 16,
    x: 0,
    y: 0,
    i: "home",
    moved: false,
    title: "显卡详情",
    attrs: {
      title: "显卡详情",
    },
    columns: [
      {
        label: "ID",
        prop: "index",
        render: "index",
        width: 60,
        align: "center",
      },
      {
        label: "名称",
        prop: "name",
      },
      {
        label: "功率(W)",
        prop: "power",
      },
      {
        label: "温度(℃)",
        prop: "temp",
      },
      {
        label: "使用率",
        prop: "usageRate",
      },
      {
        label: "已用显存/总显存(GB)",
        prop: "used",
        render: "used",
      },
    ],
    renders: {
      used: {
        componentName: "usedProgress",
        fields: {
          used: "memUsage",
          total: "memTotal",
          percent: "memUsageRate",
        },
      },
    },
  },
  {
    componentName: "gpuDetail",
    ref: "item_1",
    w: 100,
    h: 20,
    x: 0,
    y: 0,
    i: "item_1",
    moved: false,
    title: "显卡名称_1",
    attrs: {},
    childrenPanel: [
      [
        {
          type: "qiuChart",
          ref: "item_1_0_0",
          oder: 1,
          w: 25,
          x: 0,
          y: 0,
          h: 20,
          actions: [],
          updateFrequency: -1,
          colors: ["rgba(0, 121, 251, 0.502)", "rgba(151, 200, 253, 0)"],
          config: [
            {
              name: "显卡使用率",
              unit: "%",
            },
          ],
          fields: {
            value: "usageRate",
          },
          panelStyle: {
            width: "25%",
          },
        },
        {
          type: "timerlineCurve",
          ref: "item_1_0_1",
          chartOption: {
            xlabel: "Time",
            y1label: "%",
            y1Max: 100,
            smooth: true,
            symbol: "",
            graphic: true,
          },
          oder: 2,
          w: 25,
          x: 0,
          y: 20,
          h: 30,
          actions: [],
          updateFrequency: 3,
          chartType: "line",
          fields: {
            value: "usageRate",
          },
          config: [
            {
              chartType: "line",
              color: "#1864FD",
              graphic: true,
              smooth: true,
              unit: "%",
              name: "显卡使用率",
            },
          ],
          uuid: "acd3bad9-098b-4fe2-a04e-11388876aea6",
          moved: false,
          dataList: [
            {
              value: "1.25",
            },
          ],
          mainList: [],
          style: {
            width: "70%",
            height: "100%",
          },
        },
      ],
      [
        {
          type: "qiuChart",
          ref: "item_1_1_0",
          updateFrequency: -1,
          colors: ["rgba(0, 121, 251, 0.502)", "rgba(151, 200, 253, 0)"],
          config: [
            {
              name: "显存使用率",
              unit: "%",
            },
          ],
          fields: {
            value: "memUsageRate",
          },
          panelStyle: {
            width: "25%",
          },
        },
        {
          type: "timerlineCurve",
          ref: "item_1_1_1",
          chartOption: {
            xlabel: "Time",
            y1label: "%",
            y1Max: 100,
            smooth: true,
            symbol: "",
            graphic: true,
          },
          oder: 2,
          w: 25,
          x: 0,
          y: 20,
          h: 30,
          actions: [],
          updateFrequency: 3,
          chartType: "line",
          fields: {
            value: "memUsageRate",
          },
          config: [
            {
              chartType: "line",
              color: "#1864FD",
              graphic: true,
              smooth: true,
              unit: "%",
              name: "显存使用率",
            },
          ],
          uuid: "acd3bad9-098b-4fe2-a04e-11388876aea6",
          moved: false,
          dataList: [
            {
              value: "1.25",
            },
          ],
          mainList: [],
          style: {
            width: "70%",
            height: "100%",
          },
        },
      ],
    ],
  },
  {
    componentName: "gpuDetail",
    ref: "item_2",
    w: 100,
    h: 20,
    x: 0,
    y: 0,
    i: "item_2",
    moved: false,
    title: "显卡名称_2",
    attrs: {},
    childrenPanel: [
      [
        {
          type: "qiuChart",
          ref: "item_2_0_0",
          oder: 1,
          w: 25,
          x: 0,
          y: 0,
          h: 20,
          actions: [],
          updateFrequency: -1,
          colors: ["rgba(0, 121, 251, 0.502)", "rgba(151, 200, 253, 0)"],
          config: [
            {
              name: "显卡使用率",
              unit: "%",
            },
          ],
          fields: {
            value: "usageRate",
          },
          panelStyle: {
            width: "25%",
          },
        },
        {
          type: "timerlineCurve",
          ref: "item_2_0_1",
          chartOption: {
            xlabel: "Time",
            y1label: "%",
            y1Max: 100,
            smooth: true,
            symbol: "",
            graphic: true,
          },
          oder: 2,
          w: 25,
          x: 0,
          y: 20,
          h: 30,
          actions: [],
          updateFrequency: 3,
          chartType: "line",
          fields: {
            value: "usageRate",
          },
          config: [
            {
              chartType: "line",
              color: "#1864FD",
              graphic: true,
              smooth: true,
              unit: "%",
              name: "显卡使用率",
            },
          ],
          uuid: "acd3bad9-098b-4fe2-a04e-11388876aea6",
          moved: false,
          dataList: [
            {
              value: "1.25",
            },
          ],
          mainList: [],
          style: {
            width: "70%",
            height: "100%",
          },
        },
      ],
      [
        {
          type: "qiuChart",
          ref: "item_2_1_0",
          updateFrequency: -1,
          colors: ["rgba(0, 121, 251, 0.502)", "rgba(151, 200, 253, 0)"],
          config: [
            {
              name: "显存使用率",
              unit: "%",
            },
          ],
          fields: {
            value: "memUsageRate",
          },
          panelStyle: {
            width: "25%",
          },
        },
        {
          type: "timerlineCurve",
          ref: "item_2_1_1",
          chartOption: {
            xlabel: "Time",
            y1label: "%",
            y1Max: 100,
            smooth: true,
            symbol: "",
            graphic: true,
          },
          oder: 2,
          w: 25,
          x: 0,
          y: 20,
          h: 30,
          actions: [],
          updateFrequency: 3,
          chartType: "line",
          fields: {
            value: "memUsageRate",
          },
          config: [
            {
              chartType: "line",
              color: "#1864FD",
              graphic: true,
              smooth: true,
              unit: "%",
              name: "显存使用率",
            },
          ],
          uuid: "acd3bad9-098b-4fe2-a04e-11388876aea6",
          moved: false,
          dataList: [
            {
              value: "1.25",
            },
          ],
          mainList: [],
          style: {
            width: "70%",
            height: "100%",
          },
        },
      ],
    ],
  },
  {
    componentName: "gpuDetail",
    ref: "item_3",
    w: 100,
    h: 20,
    x: 0,
    y: 0,
    i: "item_3",
    moved: false,
    title: "显卡名称_3",
    attrs: {},
    childrenPanel: [
      [
        {
          type: "qiuChart",
          ref: "item_3_0_0",
          oder: 1,
          w: 25,
          x: 0,
          y: 0,
          h: 20,
          actions: [],
          updateFrequency: -1,
          colors: ["rgba(0, 121, 251, 0.502)", "rgba(151, 200, 253, 0)"],
          config: [
            {
              name: "显卡使用率",
              unit: "%",
            },
          ],
          fields: {
            value: "usageRate",
          },
          panelStyle: {
            width: "25%",
          },
        },
        {
          type: "timerlineCurve",
          ref: "item_3_0_1",
          chartOption: {
            xlabel: "Time",
            y1label: "%",
            y1Max: 100,
            smooth: true,
            symbol: "",
            graphic: true,
          },
          oder: 2,
          w: 25,
          x: 0,
          y: 20,
          h: 30,
          actions: [],
          updateFrequency: 3,
          chartType: "line",
          fields: {
            value: "usageRate",
          },
          config: [
            {
              chartType: "line",
              color: "#1864FD",
              graphic: true,
              smooth: true,
              unit: "%",
              name: "显卡使用率",
            },
          ],
          uuid: "acd3bad9-098b-4fe2-a04e-11388876aea6",
          moved: false,
          dataList: [
            {
              value: "1.25",
            },
          ],
          mainList: [],
          style: {
            width: "70%",
            height: "100%",
          },
        },
      ],
      [
        {
          type: "qiuChart",
          ref: "item_3_1_0",
          updateFrequency: -1,
          colors: ["rgba(0, 121, 251, 0.502)", "rgba(151, 200, 253, 0)"],
          config: [
            {
              name: "显存使用率",
              unit: "%",
            },
          ],
          fields: {
            value: "memUsageRate",
          },
          panelStyle: {
            width: "25%",
          },
        },
        {
          type: "timerlineCurve",
          ref: "item_3_1_1",
          chartOption: {
            xlabel: "Time",
            y1label: "%",
            y1Max: 100,
            smooth: true,
            symbol: "",
            graphic: true,
          },
          oder: 2,
          w: 25,
          x: 0,
          y: 20,
          h: 30,
          actions: [],
          updateFrequency: 3,
          chartType: "line",
          fields: {
            value: "memUsageRate",
          },
          config: [
            {
              chartType: "line",
              color: "#1864FD",
              graphic: true,
              smooth: true,
              unit: "%",
              name: "显存使用率",
            },
          ],
          uuid: "acd3bad9-098b-4fe2-a04e-11388876aea6",
          moved: false,
          dataList: [
            {
              value: "1.25",
            },
          ],
          mainList: [],
          style: {
            width: "70%",
            height: "100%",
          },
        },
      ],
    ],
  },
  {
    componentName: "gpuDetail",
    ref: "item_4",
    w: 100,
    h: 20,
    x: 0,
    y: 0,
    i: "item_4",
    moved: false,
    title: "显卡名称_4",
    attrs: {},
    childrenPanel: [
      [
        {
          type: "qiuChart",
          ref: "item_4_0_0",
          oder: 1,
          w: 25,
          x: 0,
          y: 0,
          h: 20,
          actions: [],
          updateFrequency: -1,
          colors: ["rgba(0, 121, 251, 0.502)", "rgba(151, 200, 253, 0)"],
          config: [
            {
              name: "显卡使用率",
              unit: "%",
            },
          ],
          fields: {
            value: "usageRate",
          },
          panelStyle: {
            width: "25%",
          },
        },
        {
          type: "timerlineCurve",
          ref: "item_4_0_1",
          chartOption: {
            xlabel: "Time",
            y1label: "%",
            y1Max: 100,
            smooth: true,
            symbol: "",
            graphic: true,
          },
          oder: 2,
          w: 25,
          x: 0,
          y: 20,
          h: 30,
          actions: [],
          updateFrequency: 3,
          chartType: "line",
          fields: {
            value: "usageRate",
          },
          config: [
            {
              chartType: "line",
              color: "#1864FD",
              graphic: true,
              smooth: true,
              unit: "%",
              name: "显卡使用率",
            },
          ],
          uuid: "acd3bad9-098b-4fe2-a04e-11388876aea6",
          moved: false,
          dataList: [
            {
              value: "1.25",
            },
          ],
          mainList: [],
          style: {
            width: "70%",
            height: "100%",
          },
        },
      ],
      [
        {
          type: "qiuChart",
          ref: "item_4_1_0",
          updateFrequency: -1,
          colors: ["rgba(0, 121, 251, 0.502)", "rgba(151, 200, 253, 0)"],
          config: [
            {
              name: "显存使用率",
              unit: "%",
            },
          ],
          fields: {
            value: "memUsageRate",
          },
          panelStyle: {
            width: "25%",
          },
        },
        {
          type: "timerlineCurve",
          ref: "item_4_1_1",
          chartOption: {
            xlabel: "Time",
            y1label: "%",
            y1Max: 100,
            smooth: true,
            symbol: "",
            graphic: true,
          },
          oder: 2,
          w: 25,
          x: 0,
          y: 20,
          h: 30,
          actions: [],
          updateFrequency: 3,
          chartType: "line",
          fields: {
            value: "memUsageRate",
          },
          config: [
            {
              chartType: "line",
              color: "#1864FD",
              graphic: true,
              smooth: true,
              unit: "%",
              name: "显存使用率",
            },
          ],
          uuid: "acd3bad9-098b-4fe2-a04e-11388876aea6",
          moved: false,
          dataList: [
            {
              value: "1.25",
            },
          ],
          mainList: [],
          style: {
            width: "70%",
            height: "100%",
          },
        },
      ],
    ],
  },
];
export const gpuTablePanelData = {
  componentName: "basicTable",
  ref: "gpuTable",
  w: 100,
  h: 24,
  x: 0,
  y: 0,
  i: "home",
  moved: false,
  title: i18n.t("homePage.gpuDetail"),
  attrs: {},
  columns: [
    // {
    //   label: "ID",
    //   prop: "index",
    //   render: "index",
    //   width: 60,
    //   align: "center",
    //   },
    {
      label: "ID",
      prop: "id",
      width: 60,
      align: "center",
    },
    { label: i18n.t("homePage.cardName"), prop: "name" },
    { label: i18n.t("homePage.power"), prop: "power" },
    { label: i18n.t("homePage.temp"), prop: "temp" },
    { label: i18n.t("homePage.usageRate"), prop: "usageRate" },
    { label: i18n.t("homePage.usedTotal"), prop: "used", render: "used" },
  ],
  renders: {
    used: {
      componentName: "usedProgress",
      fields: {
        used: "memUsage",
        total: "memTotal",
        percent: "memUsageRate",
      },
    },
  },
};
export const itemDetailPanelData = {
  componentName: "gpuDetail",
  ref: "", // gpuDetail_ + 显卡id
  w: 100,
  h: 20,
  x: 0,
  y: 0,
  i: "", // gpuDetail_ + 显卡id
  moved: false,
  title: "", // 显卡名称
  attrs: {},
  childrenPanel: [
    [
      {
        type: "qiuChart",
        ref: "",
        oder: 1,
        w: 25,
        x: 0,
        y: 0,
        h: 20,
        actions: [],
        updateFrequency: -1,
        colors: ["rgba(0, 121, 251, 0.502)", "rgba(151, 200, 253, 0)"],
        config: [
          {
            name: i18n.t("homePage.gpuRate"),
            unit: "%",
          },
        ],
        fields: {
          value: "usageRate",
        },
        panelStyle: { width: "25%" },
      },
      {
        type: "timerlineCurve",
        ref: "",
        chartOption: {
          xlabel: "Time",
          y1label: "%",
          y1Max: 100,
          smooth: true,
          symbol: "",
          graphic: true,
        },
        oder: 2,
        w: 25,
        x: 0,
        y: 20,
        h: 30,
        actions: [],
        updateFrequency: 3,
        chartType: "line",
        fields: {
          value: "usageRate",
        },
        config: [
          {
            chartType: "line",
            color: "#1864FD",
            graphic: true,
            smooth: true,
            unit: "%",
            name: i18n.t("homePage.gpuRate"),
          },
        ],
        uuid: "acd3bad9-098b-4fe2-a04e-11388876aea6",
        moved: false,
        dataList: [
          {
            value: "1.25",
          },
        ],
        mainList: [],
        style: {
          width: "70%",
          height: "100%",
        },
      },
    ],
    [
      {
        type: "qiuChart",
        ref: "",
        updateFrequency: -1,
        colors: ["rgba(0, 121, 251, 0.502)", "rgba(151, 200, 253, 0)"],
        config: [
          {
            name: i18n.t("homePage.memoryRate"),
            unit: "%",
          },
        ],
        fields: {
          value: "memUsageRate",
        },
        panelStyle: { width: "25%" },
      },
      {
        type: "timerlineCurve",
        ref: "",
        chartOption: {
          xlabel: "Time",
          y1label: "%",
          y1Max: 100,
          smooth: true,
          symbol: "",
          graphic: true,
        },
        oder: 2,
        w: 25,
        x: 0,
        y: 20,
        h: 30,
        actions: [],
        updateFrequency: 3,
        chartType: "line",
        fields: {
          value: "memUsageRate",
        },
        config: [
          {
            chartType: "line",
            color: "#1864FD",
            graphic: true,
            smooth: true,
            unit: "%",
            name: i18n.t("homePage.memoryRate"),
          },
        ],
        uuid: "acd3bad9-098b-4fe2-a04e-11388876aea6",
        moved: false,
        dataList: [
          {
            value: "1.25",
          },
        ],
        mainList: [],
        style: {
          width: "70%",
          height: "100%",
        },
      },
    ],
  ],
};
