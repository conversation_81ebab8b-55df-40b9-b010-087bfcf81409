<template>
  <div class="my-panel-container full-height">
    <div class="panel-container full-height">
      <grid-layout
        :layout.sync="allPanelData"
        :col-num="colNum"
        :row-height="rowHeight"
        :is-draggable="draggable"
        :is-resizable="resizable"
        :margin="[15, 15]"
        :autoSize="true"
        :vertical-compact="true"
        :use-css-transforms="true"
      >
        <grid-item
          :style="{
            background: '#fff',
            border: 'none',
            color: '#000',
            ...(item.gridStyle || {}),
          }"
          v-for="(item, index) in allPanelData"
          :key="`layout${index}`"
          :x="item.x"
          :y="item.y"
          :w="item.w"
          :h="item.h"
          :i="item.i"
          @resize="
            (i, newH, newW, newHPx, newWPx) => {
              // resizeEvent(i, newH, newW, newHPx, newWPx, item, index);
            }
          "
          @resized="
            (i, newH, newW, newHPx, newWPx) => {
              // resizedEvent(i, newH, newW, newHPx, newWPx, item, index);
            }
          "
          @moved="
            (i, newX, newY) => {
              // movedEvent(i, newX, newY, item);
            }
          "
        >
          <span
            style="
              position: absolute;
              bottom: 0px;
              left: 5px;
              opacity: 0.5;
              color: red;
            "
            v-if="draggable"
          >
            {{ item.type }},i:{{ item.i }},x:{{ item.x }},y:{{ item.y }},w:{{
              item.w
            }},h:{{ item.h }}
          </span>
          <div class="card-top" v-if="item.title && !item.hiddenTitle">
            <div class="small-title title-pre-bar">
              <span> {{ item.title }} </span>
            </div>
          </div>
          <component
            :is="item.componentName"
            :allPanelList="allPanelData"
            :panelData="item"
            :index="index"
            :ref="item.ref"
            :key="item.ref"
            v-bind="item.attrs"
            class="item-basicText-box"
            :style="{
              height:
                item.title && !item.hiddenTitle ? 'calc(100% - 52px)' : '100%',
            }"
            @publicEmit="
              (params) => {
                publicEmit(params, item);
              }
            "
          ></component>
        </grid-item>
      </grid-layout>
    </div>
  </div>
</template>

<script>
import { GridLayout, GridItem } from "vue-grid-layout";
import { gpuTablePanelData, itemDetailPanelData } from "./data.js";
import request from "@/utils/request";
import basicTable from "./components/basicTable/index.vue";
import gpuDetail from "./components/gpuDetail/index.vue";
import { getGPUDetail } from "@/api/dashboard/panel.js";
export default {
  name: "homePage",
  components: {
    GridLayout,
    GridItem,
    basicTable,
    gpuDetail,
  },
  computed: {},
  data() {
    return {
      searchForm: {},
      tenantList: [],
      colNum: 100,
      rowHeight: 5,
      draggable: false,
      resizable: false,
      allPanelData: [],
      timer: null,
    };
  },
  beforeMount() {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
    this.getData();
  },
  methods: {
    getItemDetailPanelData(item) {
      let data = JSON.parse(JSON.stringify(itemDetailPanelData));
      data.title = `${item.name} - ${item.id}`;
      data.i = `item_${item.id}`;
      data.ref = `item_${item.id}`;
      data.childrenPanel.forEach((list, i) => {
        list.forEach((listItem, index) => {
          listItem.ref = `item_${item.id}_${i}_${index}`;
        });
      });
      return data;
    },
    getData() {
      getGPUDetail({}).then((res) => {
        let dataList = res.result || [];
        let h = (48 + 40 + (dataList.length + 1) * 40) / 20;
        gpuTablePanelData.h = h > 15 ? h : 15;
        let detailPanelList = [];
        dataList.forEach((item, index) => {
          let obj = this.getItemDetailPanelData(item);
          detailPanelList.push(obj);
        });
        this.allPanelData = [gpuTablePanelData, ...detailPanelList];
        this.$nextTick(() => {
          this.$refs.gpuTable[0].setData({ dataList });
          detailPanelList.forEach((panelData, index) => {
            let currentData = dataList.find(
              (item) => panelData.i == `item_${item.id}`,
            );
            this.$refs[panelData.ref][0].setData(currentData);
          });
        });
        this.timer = setTimeout(() => {
          this.getData();
        }, 3000);
      });
    },
    changeSearchForm() {
      this.publicEmitParams();
    },
    publicEmitParams() {
      window.$eventBus.$emit("indexQueryChange", { ...this.searchForm });
    },
    publicEmit(params, configItem) {},
  },
  beforeRouteLeave(to, form, next) {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
    next();
  },
  beforeDestroy() {
    if (this.timer) {
      clearTimeout(this.timer);
      this.timer = null;
    }
  },
};
</script>

<style lang="scss">
@import "~@/assets/styles/myPanel.scss";
</style>
