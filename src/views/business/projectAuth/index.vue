<template>
  <div class="full-height full-width page-container">
    <smart-search-table-content
      class="full-height full-width"
      ref="searchTableRef"
      :searchForm="searchForm"
      :advanceSearchForm="advanceSearchForm"
      :columns="columns"
      :tableData="tableData"
      :table-loading="loading"
      :tablePage="tablePage"
      @reset="reset"
      @query="queryBase"
      @pagination="pagination"
      :isSelected="true"
      :isAdvance="isAdvance"
      @queryAdvance="queryAdvance"
      @selectionChange="selectionChange"
      :selectable="selectable"
    >
      <template #pageTitle>
        <page-header title="项目授权">
          <!-- <template #titleWarning>{{ $t("pageTitle.apiKey.desc") }}</template> -->
        </page-header>
      </template>
      <template #search>
        <search-item label="招标项目名称" prop="tenderProjectName">
          <el-input
            v-model="searchForm.tenderProjectName"
            :placeholder="$t('placeholder.place')"
            clearable
          />
        </search-item>
      </template>
      <template v-slot:actions>
        <batch-action-btns
          :btns="actionBtns"
          :batchBtns="batchBtns"
          :limit="batchLimit"
          :showBatch="selectList.length > 0"
          :getSelectList="() => selectList"
          :isSelectTotal="isSelectTotal"
          :total="tablePage.total"
          @onSelectTotalClick="onSelectTotalClick"
          @clearSelectList="clearSelectList"
        />
      </template>
      <template #operation="{ data }">
        <operatebtns
          :row="data"
          :btns="operateBtns"
          :limit="operbtnsLimit"
        ></operatebtns>
      </template>
    </smart-search-table-content>
  </div>
</template>

<script>
import Page from "@/components/smartSearchTableContent/mixins/page";
import {
  getProjectAuthPage,
  deleteProjectAuth,
} from "@/api/business/projectAuth";
export default {
  mixins: [Page],
  components: {},
  data() {
    return {
      addDialog: {
        dialogVisible: false,
        rowObj: {},
        isEdit: false,
      },
      detailDialog: {
        dialogVisible: false,
        rowObj: {},
      },
      actionBtns: [
        {
          name: "新建",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
      ],
      batchBtns: [
        {
          name: "删除",
          class: "danger-text-btn",
          icon: "iconfont icon-tianjia-line",
          permi: ["none:none:none"],
          func: (row) => {
            this.confirmInfoOperFun(
              this.$t("tips.deleteTip"),
              deleteProjectAuth,
              { ids: [row.id] },
              this.$t("msg.delete"),
            );
          },
        },
      ],
      columns: [
        {
          label: "公司主体",
          prop: "companyEntity",
        },
        {
          label: "项目省份",
          prop: "projectProvince",
        },
        {
          label: "项目编号",
          prop: "projectNumber",
        },
        {
          label: "项目名称",
          prop: "projectName",
        },
        {
          label: "招标项目名称",
          prop: "tenderProjectName",
        },
        {
          label: "申请人",
          prop: "requesterName",
        },
        {
          label: "申请部门",
          prop: "requestingDepartment",
        },
        {
          label: "是否唯一授权",
          prop: "isExclusiveAuthorization",
          formatter: (row) => {
            return this.getEnumLabelByValue(
              this.dictData.exclusive_authorization,
              row.isExclusiveAuthorization,
            );
          },
        },
        {
          label: "授权范围",
          prop: "authorizationScope",
        },
        {
          label: "授权时效",
          prop: "authorizationDuration",
          formatter: (row) => {
            return row.authorizationBeginTime + "-" + row.authorizationDuration;
          },
          minWidth: "150px",
        },
        {
          label: "被授权单位",
          prop: "authorizedEntity",
        },
        {
          label: "操作",
          prop: "operation",
          render: "operation",
          width: "120px",
          align: "center",
          fixed: "right",
        },
      ],
      operateBtns: [
        {
          name: this.$t("common.detail"),
          class: "oper-text-btn",
          permi: ["none:none:none"],
          func: this.onDetailClick,
        },
        {
          name: this.$t("common.edit"),
          class: "oper-text-btn",
          permi: ["none:none:none"],
          func: this.onEditClick,
        },
        {
          name: this.$t("common.delete"),
          class: "delete-text-btn",
          permi: ["none:none:none"],
          func: (row) => {
            this.confirmInfoOperFun(
              this.$t("tips.deleteTip"),
              deleteProjectAuth,
              { ids: [row.id] },
              this.$t("msg.delete"),
            );
          },
        },
      ],
      // 查询参数
      searchForm: {
        tenderProjectName: "",
      },
      dictData: {
        exclusive_authorization: [],
      },
    };
  },
  created() {
    this.getDictList();
  },
  methods: {
    request: getProjectAuthPage,
    onAddClick() {
      this.$router.push({
        name: "projectAuthOper",
      });
    },
    onCheckClick() {},
    onImportClick() {},
    onExportClick() {},
    onDetailClick(row) {},
    onEditClick(row) {
      this.$router.push({
        name: "projectAuthOper",
        query: {
          id: row.id,
          name: row.contactCode,
        },
      });
    },
  },
  beforeDestroy() {},
};
</script>
