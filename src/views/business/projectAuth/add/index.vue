<template>
  <content-detail :content="isEdit ? `编辑-${form.tenderProjectName}` : '新增'">
    <menu-form-content :typeList="typeList">
      <el-form
        slot="rightContent"
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="auto"
        size="small"
        label-position="right"
      >
        <div class="right-content-item" ref="basicInfoRef">
          <base-title title="项目信息"></base-title>
          <div class="flex-form-box">
            <sw-form-item label="项目编号" prop="projectNumber">
              <!-- <el-select v-model="form.projectId">
                <el-option
                  v-for="(item, index) in projectList"
                  :key="`project_${index}`"
                  :label="item.projectName"
                  :value="item.id"
                ></el-option>
              </el-select> -->
              <select-input
                v-model="form.projectNumber"
                @dialogClick="dialogClick"
                @clear="clear"
              ></select-input>
            </sw-form-item>
            <sw-form-item label="招标项目名称" prop="tenderProjectName">
              <el-input
                v-model="form.tenderProjectName"
                :placeholder="$t('placeholder.place')"
                clearable
              ></el-input>
            </sw-form-item>
            <sw-form-item
              style="margin-bottom: 0px; color: rgba(0, 0, 0, 0.45)"
              label="项目名称"
              prop="projectName"
            >
              <span>{{ form.projectName }}</span>
            </sw-form-item>
            <sw-form-item
              style="margin-bottom: 0px; color: rgba(0, 0, 0, 0.45)"
              label="项目省份"
              prop="projectProvince"
            >
              <span>{{ form.projectProvince }}</span>
            </sw-form-item>
            <sw-form-item
              style="margin-bottom: 0px; color: rgba(0, 0, 0, 0.45)"
              label="公司主体"
              prop="companyEntity"
            >
              <span>{{ form.companyEntity }}</span>
            </sw-form-item>
          </div>
        </div>
        <div class="right-content-item" ref="authContentRef">
          <base-title title="授权内容"></base-title>
          <div class="flex-form-box">
            <sw-form-item label="是否唯一授权" prop="isExclusiveAuthorization">
              <el-radio-group v-model="form.isExclusiveAuthorization">
                <el-radio
                  v-for="(item, index) in dictData.exclusive_authorization"
                  :key="`exauth_${index}`"
                  :label="item.value"
                  >{{ item.label }}</el-radio
                >
              </el-radio-group>
            </sw-form-item>
            <sw-form-item label="授权范围" prop="authorizationScope">
              <el-input
                v-model="form.authorizationScope"
                :placeholder="$t('placeholder.place')"
                clearable
              ></el-input>
            </sw-form-item>
            <sw-form-item label="授权时效" prop="dateRange">
              <el-date-picker
                v-model="form.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </sw-form-item>
            <sw-form-item label="被授权单位" prop="authorizedEntity">
              <el-input
                v-model="form.authorizedEntity"
                :placeholder="$t('placeholder.place')"
                clearable
              ></el-input>
            </sw-form-item>
            <sw-form-item label="授权文件类型" prop="exportFileType">
              <dict-select
                v-model="form.exportFileType"
                filterable
                clearable
                placeholder="请选择"
                dictName="授权文件类型"
                dictCode="export_file_type"
                :options="dictData.export_file_type"
              ></dict-select>
            </sw-form-item>
            <sw-form-item
              label="附件"
              prop="file"
              key="file"
              v-if="form.exportFileType == '自定义文件'"
              ref="fileRef"
            >
              <single-file-upload
                v-model="form.file"
                :autoUpload="false"
                :type="['docx', 'doc']"
                :size="10"
                unit="MB"
                :isShowTip="true"
                @change="onFileChange"
              ></single-file-upload>
            </sw-form-item>
          </div>
        </div>
      </el-form>
    </menu-form-content>
    <div slot="footer" class="page-content-footer">
      <el-button
        @click="onCloseClick"
        :loading="loading"
        class="oper-btn cancel-btn"
        >{{ $t("common.cancel") }}</el-button
      >
      <el-button
        type="primary"
        class="oper-btn"
        @click="onSubmitClick"
        :loading="loading"
        >{{ $t("common.submit") }}</el-button
      >
    </div>
    <project-table
      :dialogVisible="projectDialog.dialogVisible"
      @eventSubmit="changeProject"
      @eventClose="eventClose(projectDialog)"
    ></project-table>
  </content-detail>
</template>
<script>
import {
  addProjectAuth,
  editProjectAuth,
  getProjectAuthDetail,
} from "@/api/business/projectAuth";
import { cloneDeep } from "lodash";
import selectInput from "../common/selectInput";
import projectTable from "./projectTable";
export default {
  name: "businessCustomerAdd",
  components: {
    selectInput,
    projectTable,
  },
  data() {
    return {
      isEdit: false,
      loading: false,
      dictData: {
        exclusive_authorization: [],
        export_file_type: [],
      },
      activeName: "basicInfo",
      isClickType: false,
      typeList: [
        { label: "项目信息", value: "basicInfo" },
        { label: "授权内容", value: "authContent" },
      ],
      form: {
        projectNumber: "",
        projectName: "",
        projectProvince: "",
        companyEntity: "",
        projectId: "",
        tenderProjectName: "",
        isExclusiveAuthorization: "否",
        authorizationScope: "",
        dateRange: [],
        authorizationBeginTime: "",
        authorizationDuration: "",
        authorizedEntity: "",
        exportFileType: "",
        file: [],
      },
      rules: {
        projectNumber: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        tenderProjectName: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        authorizationScope: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        dateRange: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        isExclusiveAuthorization: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        projectId: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        authorizedEntity: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        exportFileType: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        file: [
          {
            required: true,
            message: "请上传文件",
            trigger: "change",
          },
        ],
      },
      projectList: [],
      isChangeFile: false,
      projectDialog: {
        dialogVisible: false,
      },
    };
  },
  computed: {
    queryData() {
      return this.$route.query;
    },
  },
  created() {
    this.isEdit = !!this.$route.query.id;
    this.getDictList();
    if (this.isEdit) {
      this.getDetail();
    }
    // this.getProjectList();
  },

  methods: {
    getDetail() {
      this.loading = true;
      getProjectAuthDetail({
        id: this.$route.query.id,
      })
        .then((res) => {
          this.form = {
            ...res.data,
            dateRange: [
              res.data.authorizationBeginTime,
              res.data.authorizationDuration,
            ],
            file: [{ name: res.data.exportFileName }],
          };
        })
        .finally(() => {
          this.loading = false;
        });
    },

    dialogClick() {
      this.projectDialog = {
        dialogVisible: true,
      };
    },

    onFileChange() {
      this.$refs.fileRef.$children[0].clearValidate();
      this.isChangeFile = true;
    },

    changeProject(item) {
      this.form.projectName = item.projectName;
      this.form.projectNumber = item.projectNum;
      this.form.projectId = item.id;
      this.form.projectProvince = item.projectProvince;
      this.form.companyEntity = item.subjectSelectName;
    },

    clear() {
      this.form.projectName = "";
      this.form.projectId = "";
      this.form.projectProvince = "";
      this.form.companyEntity = "";
    },

    eventClose(dialog) {
      dialog.dialogVisible = false;
    },

    // getProjectList() {
    //   getOpportunityList({
    //     pageNum: 1,
    //     pageSize: 9999,
    //   }).then((res) => {
    //     this.projectList = res.data.list;
    //   });
    // },

    onCloseClick() {
      this.$refs.formRef?.resetFields();
      this.$router.go(-1);
    },
    onSubmitClick() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          let request = this.isEdit ? editProjectAuth : addProjectAuth;
          this.loading = true;
          let params = cloneDeep(this.form);
          params["authorizationBeginTime"] = params.dateRange?.[0] ?? "";
          params["authorizationDuration"] = params.dateRange?.[1] ?? "";
          if (params.exportFileType == "自定义文件") {
            params["file"] = params.file[0];
          } else {
            delete params.file;
          }
          // 编辑并且没修改过文件信息
          if (this.isEdit && !this.isChangeFile) {
            delete params.file;
          }

          delete params.dateRange;
          request(params)
            .then((res) => {
              this.successMsg(this.isEdit ? "编辑成功" : "新增成功");
              this.onCloseClick();
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
