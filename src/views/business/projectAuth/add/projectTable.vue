<template>
  <el-dialog
    class="form-dialog"
    title="项目信息"
    :visible="dialogVisible"
    width="1000px"
    :close-on-click-modal="false"
    :append-to-body="true"
    @open="onOpenDig"
    :before-close="onCloseClick"
  >
    <search-table-content
      class="full-height full-width"
      ref="searchTableRef"
      :searchForm="searchForm"
      :columns="columns"
      :tableData="tableData"
      :table-loading="loading"
      :tablePage="tablePage"
      @reset="reset"
      @query="query"
      @pagination="pagination"
      @onDbRowClick="onDbRowClick"
      @onCellMouseEnter="onCellMouseEnter"
      @onCellMouseLeave="onCellMouseLeave"
      :inner="true"
    >
      <template #search>
        <search-item label="主体名称" prop="subjectSelectName">
          <el-select
            v-model="searchForm.subjectSelectName"
            :placeholder="$t('placeholder.select')"
            clearable
          >
            <el-option
              v-for="item in dictData.subject_select"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            ></el-option>
          </el-select>
        </search-item>
      </template>
    </search-table-content>
  </el-dialog>
</template>
<script>
import Page from "@/components/searchTableContent/mixins/page";
import { getOpportunityList } from "@/api/business/projectOpportunity";
export default {
  name: "projectTable",
  mixins: [Page],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      searchForm: {
        subjectSelectName: "",
      },
      dictData: {
        subject_select: [],
      },
      currentValue: "",
      columns: [
        {
          label: "项目编号",
          prop: "projectNum",
        },
        {
          label: "项目名称",
          prop: "projectName",
        },
        {
          label: "项目省份",
          prop: "projectProvince",
        },
        {
          label: "主体名称",
          prop: "subjectSelectName",
        },
      ],
      initQuery: false,
    };
  },
  created() {
    this.getDictList();
  },
  methods: {
    request: getOpportunityList,
    onOpenDig() {
      this.query();
    },
    onCellMouseEnter({ row, column, cell, event }) {
      this.createTips(event, row, "请双击选择信息");
    },
    onCellMouseLeave({ row }) {
      this.removeTips(row);
    },

    createTips(el, row, value) {
      const { id } = row;
      const tooltipDom = document.createElement("div");
      tooltipDom.style.cssText = `
            display: inline-block;
            max-width: 400px;
            max-height: 400px;
            position: absolute;
            top: ${el.clientY + 5}px;
            left: ${el.clientX}px;
            padding:5px 10px;
            overflow: auto;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #595959;
            background: #fff;
            border-radius: 5px;
            z-index: 19999;
            box-shadow: 0 4px 12px 1px #ccc;
          `;
      tooltipDom.innerHTML = value;
      tooltipDom.setAttribute("id", `tooltip-${id}`);
      // 将浮层插入到body中
      document.body.appendChild(tooltipDom);
    },
    removeTips(row) {
      const { id } = row;
      const tooltipDomLeave = document.querySelectorAll(`#tooltip-${id}`);
      if (tooltipDomLeave.length) {
        tooltipDomLeave.forEach((dom) => {
          document.body.removeChild(dom);
        });
      }
    },

    onCloseClick() {
      this.currentValue = "";
      this.$emit("eventClose");
    },
    onDbRowClick(rowInfo) {
      this.$emit("eventSubmit", rowInfo.row);
      this.onCloseClick();
    },
  },
};
</script>
