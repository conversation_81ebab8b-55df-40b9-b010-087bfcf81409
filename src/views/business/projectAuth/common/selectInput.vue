<template>
  <div
    class="input-box"
    @mouseenter="isHover = true"
    @mouseleave="isHover = false"
  >
    <el-input v-model="content" readonly> </el-input>
    <div class="icon-box">
      <i
        class="el-icon-close"
        v-if="content && isHover"
        @click="onClearClick"
      ></i>
      <i class="el-icon-plus" @click="onDialogClick"></i>
    </div>
  </div>
</template>
<script>
export default {
  name: "SelectInput",
  props: {
    value: {
      type: [String, Number, Array],
      default: "",
    },
  },
  data() {
    return {
      content: "",
      isHover: false,
    };
  },
  watch: {
    value: {
      handler(val) {
        this.content = val;
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    onClearClick() {
      this.content = "";
      this.$emit("input", this.content);
      this.$emit("clear");
    },
    onDialogClick() {
      this.$emit("dialogClick");
    },
  },
};
</script>
<style lang="scss" scoped>
.input-box {
  position: relative;
  .icon-box {
    position: absolute;
    top: 0px;
    right: 10px;
    i {
      cursor: pointer;
    }
    i + i {
      margin-left: 16px;
    }
  }
}
</style>
