<template>
  <content-detail :content="isEdit ? `编辑-${queryData.name}` : '新增'">
    <menu-form-content :typeList="typeList">
      <el-form
        slot="rightContent"
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="auto"
        size="small"
        label-position="right"
      >
        <div class="right-content-item" id="Base">
          <base-title title="基本信息"></base-title>
          <el-row :gutter="20">
            <el-col :span="12">
              <sw-form-item label="客户档案" prop="customerId">
                <el-select
                  v-model="form.customerId"
                  placeholder="请选择"
                  filterable
                  remote
                  reserve-keyword
                  :remote-method="getCustomerList"
                  :loading="loading1"
                  @change="onChangeCustomer"
                >
                  <el-option
                    v-for="(item, index) in customerList"
                    :key="item.id"
                    :value="item.id"
                    :label="item.customerName"
                  ></el-option>
                </el-select>
              </sw-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <sw-form-item label="客户名称" prop="customerName">
                <el-input
                  v-model="form.customerName"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col> -->
            <el-col :span="12">
              <sw-form-item label="联系人姓名" prop="name">
                <el-input
                  v-model="form.name"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12" v-if="form.contactCode">
              <sw-form-item label="联系人编号" prop="contactCode">
                <span style="line-height: 33px">{{ form.contactCode }}</span>
                <!-- <el-input
                  v-model="form.contactCode"
                  placeholder="请输入"
                  clearable
                ></el-input> -->
              </sw-form-item>
            </el-col>

            <el-col :span="12">
              <sw-form-item label="所在部门" prop="department">
                <el-input
                  v-model="form.department"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="职务" prop="position">
                <el-input
                  v-model="form.position"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="性别" prop="sex">
                <el-radio-group v-model="form.sex">
                  <el-radio
                    :label="item.value"
                    v-for="item in getEnum(['sex'])"
                    :key="item.value"
                    >{{ item.label }}</el-radio
                  >
                </el-radio-group>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="手机号" prop="mobile">
                <el-input
                  v-model="form.mobile"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="电话" prop="tel">
                <el-input
                  v-model="form.tel"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="微信" prop="wechat">
                <el-input
                  v-model="form.wechat"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="上级领导" prop="superiorLeader">
                <el-input
                  v-model="form.superiorLeader"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="Company">
          <base-title title="公司文件接收信息"></base-title>
          <el-row :gutter="20">
            <el-col :span="12">
              <sw-form-item label="是否首要联系人" prop="primaryContactFlag">
                <el-radio-group v-model="form.primaryContactFlag">
                  <el-radio
                    :label="item.value"
                    v-for="item in getEnum(['Boolean'])"
                    :key="item.value"
                    >{{ item.label }}</el-radio
                  >
                </el-radio-group>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="是否函证接收人"
                prop="confirmationReceiverFlag"
              >
                <el-radio-group v-model="form.confirmationReceiverFlag">
                  <el-radio
                    :label="item.value"
                    v-for="item in getEnum(['Boolean'])"
                    :key="item.value"
                    >{{ item.label }}</el-radio
                  >
                </el-radio-group>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="接收函证类型" prop="confirmationType">
                <dict-select
                  v-model="form.confirmationType"
                  :options="dictData.confirmation_type"
                  placeholder="请选择"
                  dictName="接收函证类型"
                  dictCode="confirmation_type"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="Other">
          <base-title title="其他"></base-title>
          <el-row :gutter="20">
            <el-col :span="24">
              <sw-form-item label="备注" prop="remark">
                <el-input
                  type="textarea"
                  maxlength="1000"
                  show-word-limit
                  rows="4"
                  v-model="form.remark"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </menu-form-content>
    <div slot="footer" class="page-content-footer">
      <el-button
        class="oper-btn cancel-btn mgr10"
        @click="onCloseClick"
        :loading="loading"
        >{{ $t("common.cancel") }}</el-button
      >
      <el-button
        type="primary"
        class="oper-btn"
        :loading="loading"
        @click="onSubmitClick"
        >{{ $t("common.submit") }}</el-button
      >
    </div>
  </content-detail>
</template>
<script>
import {
  getContactDetail,
  addContact,
  editContact,
} from "@/api/business/contact";
import { getCustomerList } from "@/api/business/customer";
let defaultInfo = {
  customerId: "",
  customerName: "",
  name: "",
  department: "",
  position: "",
  sex: "",
  mobile: "",
  tel: "",
  wechat: "",
  superiorLeader: "",
  primaryContactFlag: "",
  confirmationReceiverFlag: "",
  confirmationType: "",
  remark: "",
};
export default {
  name: "businessContactAdd",
  components: {},
  data() {
    return {
      isEdit: false,
      loading: false,
      dictData: {
        confirmation_type: [],
      },
      typeList: [
        { label: "基本信息", value: "Base" },
        { label: "公司文件接收信息", value: "Company" },
        { label: "其他", value: "Other" },
      ],
      form: {},
      rules: {
        name: [{ required: true, message: "请输入", trigger: "blur" }],
      },
      customerList: [],
      loading1: false,
    };
  },
  computed: {
    queryData() {
      return this.$route.query;
    },
  },
  created() {
    this.isEdit = !!this.$route.query.id;
    this.initData();
    this.getDictList();
  },

  methods: {
    onChangeCustomer(val) {
      let currentObj = this.customerList.find((item) => item.id == val);
      this.form.customerName = currentObj.customerName;
    },
    getCustomerList(customerName = "") {
      if (!customerName && this.form.customerId) {
        getCustomerList({
          id: this.form.customerId,
          pageNum: 1,
          pageSize: 100,
        })
          .then((res) => {
            this.customerList = res.data.list || res.data || [];
          })
          .finally(() => {
            this.loading1 = false;
          });
      } else {
        this.loading1 = true;
        getCustomerList({
          customerName: customerName,
          pageNum: 1,
          pageSize: 100,
        })
          .then((res) => {
            this.customerList = res.data.list || res.data || [];
          })
          .finally(() => {
            this.loading1 = false;
          });
      }
    },
    queryDictList() {
      console.log("重新获取字典");
      this.getDictList();
    },
    initData() {
      this.form = this.cloneDeep(defaultInfo);
      if (this.isEdit) {
        getContactDetail({ id: this.queryData.id }).then((res) => {
          this.form = {
            ...this.cloneDeep(defaultInfo),
            ...res.data,
          };
          if (this.form.customerId) {
            this.getCustomerList();
          }
        });
      } else {
        this.getCustomerList();
      }
    },

    onCloseClick() {
      this.$refs.formRef?.resetFields();
      this.$router.go(-1);
    },
    onSubmitClick() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          let request = this.isEdit ? editContact : addContact;
          this.loading = true;
          request(this.form)
            .then((res) => {
              this.successMsg(this.isEdit ? "编辑成功" : "新增成功");
              this.onCloseClick();
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
