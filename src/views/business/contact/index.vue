<template>
  <div class="full-height full-width page-container">
    <smart-search-table-content
      class="full-height full-width"
      ref="searchTableRef"
      :searchForm="searchForm"
      :advanceSearchForm="advanceSearchForm"
      :columns="columns"
      :tableData="tableData"
      :table-loading="loading"
      :tablePage="tablePage"
      @reset="reset"
      @query="queryBase"
      @pagination="pagination"
      :isSelected="true"
      :isAdvance="isAdvance"
      @queryAdvance="queryAdvance"
      @selectionChange="selectionChange"
      :selectable="selectable"
    >
      <template #pageTitle>
        <page-header title="联系人">
          <!-- <template #titleWarning>{{ $t("pageTitle.apiKey.desc") }}</template> -->
        </page-header>
      </template>
      <template #search>
        <search-item label="联系人编号" prop="contactCode">
          <el-input
            v-model="searchForm.contactCode"
            :placeholder="$t('placeholder.place')"
            clearable
          />
        </search-item>
        <search-item label="联系人姓名" prop="name">
          <el-input
            v-model="searchForm.name"
            :placeholder="$t('placeholder.place')"
            clearable
          />
        </search-item>
      </template>
      <template v-slot:actions>
        <batch-action-btns
          :btns="actionBtns"
          :batchBtns="batchBtns"
          :limit="batchLimit"
          :showBatch="selectList.length > 0"
          :getSelectList="() => selectList"
          :isSelectTotal="isSelectTotal"
          :total="tablePage.total"
          @onSelectTotalClick="onSelectTotalClick"
          @clearSelectList="clearSelectList"
        />
      </template>
      <template #operation="{ data }">
        <operatebtns
          :row="data"
          :btns="operateBtns"
          :limit="operbtnsLimit"
        ></operatebtns>
      </template>
    </smart-search-table-content>
  </div>
</template>

<script>
import Page from "@/components/smartSearchTableContent/mixins/page";
import { getContactList, deleteContact } from "@/api/business/contact";
export default {
  mixins: [Page],
  components: {},
  data() {
    return {
      addDialog: {
        dialogVisible: false,
        rowObj: {},
        isEdit: false,
      },
      detailDialog: {
        dialogVisible: false,
        rowObj: {},
      },
      actionBtns: [
        {
          name: "新建",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "查重",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onCheckClick,
          permi: ["none:none:none"],
        },
        {
          name: "导入",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-shangchuan1-line",
          func: this.onImportClick,
          permi: ["none:none:none"],
        },
        {
          name: "导出",
          id: "exportBtnId",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-daochu",
          func: this.onExportClick,
          permi: ["none:none:none"],
        },
      ],
      batchBtns: [
        {
          name: "分配",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
          rule: () => {
            return true;
          },
        },
        {
          name: "删除",
          class: "danger-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "导出",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "共享",
          class: "oper-text-btn",
          icon: "iconfont icon-shangchuan1-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "批量编辑",
          class: "oper-text-btn",
          icon: "iconfont icon-daochu",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "提交审批",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "下载全部附件",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
          rule: () => {
            return true;
          },
        },
      ],
      columns: [
        {
          label: "联系人编号",
          prop: "contactCode",
        },
        {
          label: "客户名称",
          prop: "customerName",
        },
        {
          label: "联系人姓名",
          prop: "name",
        },
        {
          label: "所在部门",
          prop: "department",
        },
        {
          label: "职务",
          prop: "position",
        },
        {
          label: "性别",
          prop: "sex",
        },
        {
          label: "手机号",
          prop: "mobile",
        },
        {
          label: "电话",
          prop: "tel",
        },
        {
          label: "微信",
          prop: "wechat",
        },
        {
          label: "上级领导",
          prop: "superiorLeader",
        },
        {
          label: "是否首要联系人",
          prop: "primaryContactFlag",
        },
        {
          label: "是否函证接收人",
          prop: "confirmationReceiverFlag",
        },
        {
          label: "接收函证类型",
          prop: "confirmationType",
        },
        {
          label: "备注",
          prop: "remark",
        },
        // {
        //   label: "扩展字段1",
        //   prop: "extend1",
        // },
        // {
        //   label: "扩展字段2",
        //   prop: "extend2",
        // },
        // {
        //   label: "扩展字段3",
        //   prop: "extend3",
        // },
        // {
        //   label: "创建人",
        //   prop: "createBy",
        // },
        {
          label: "创建时间",
          prop: "createTime",
        },
        // {
        //   label: "更新人",
        //   prop: "updateBy",
        // },
        {
          label: "更新时间",
          prop: "updateTime",
        },

        {
          label: "操作",
          prop: "operation",
          render: "operation",
          width: "120px",
          align: "center",
          fixed: "right",
        },
      ],
      operateBtns: [
        {
          name: this.$t("common.detail"),
          class: "oper-text-btn",
          permi: ["none:none:none"],
          func: this.onDetailClick,
        },
        {
          name: this.$t("common.edit"),
          class: "oper-text-btn",
          permi: ["none:none:none"],
          func: this.onEditClick,
        },
        {
          name: this.$t("common.delete"),
          class: "delete-text-btn",
          permi: ["none:none:none"],
          func: (row) => {
            this.confirmInfoOperFun(
              this.$t("tips.deleteTip"),
              deleteContact,
              [row.id],
              this.$t("msg.delete"),
            );
          },
        },
      ],
      // 查询参数
      searchForm: {
        contactCode: "",
        name: "",
      },
      dictData: {},
    };
  },
  created() {
    this.getDictList();
  },
  methods: {
    request: getContactList,

    onAddClick() {
      this.$router.push({
        name: "businessContactAdd",
        query: {},
      });
    },
    onCheckClick() {},
    onImportClick() {},
    onExportClick() {},
    onDetailClick(row) {},
    onEditClick(row) {
      this.$router.push({
        name: "businessContactAdd",
        query: {
          id: row.id,
          name: row.name,
        },
      });
    },
  },
  beforeDestroy() {},
};
</script>
