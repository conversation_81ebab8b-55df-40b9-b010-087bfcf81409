<template>
  <content-detail :content="isEdit ? `编辑-${queryData.name}` : '新增'">
    <menu-form-content :typeList="typeList">
      <el-form
        slot="rightContent"
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="auto"
        size="small"
        label-position="right"
      >
        <div class="right-content-item" id="Project">
          <base-title title="项目信息"></base-title>

          <el-row :gutter="20">
            <el-col :span="12">
              <sw-form-item label="销售负责人" prop="baseData.salesPersonId">
                <el-input
                  v-model="form.baseData.salesPersonId"
                  placeholder="请输入"
                  clearable
                ></el-input>
                TODO 组织架构写完再回来加
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="销售部门" prop="baseData.salesPersonDept">
                <el-input
                  v-model="form.baseData.salesPersonDept"
                  placeholder=""
                  disabled
                ></el-input>
                TODO 组织架构写完再回来加
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="项目省份" prop="baseData.projectProvince">
                <dict-select
                  v-model="form.baseData.projectProvince"
                  :options="dictData.province"
                  placeholder="请选择"
                  dictName="省份"
                  dictCode="province"
                  clearable
                  filterable
                  @queryDictList="queryDictList"
                  @change="onChangeProvince"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="项目地市" prop="baseData.projectCity">
                <dict-select
                  v-model="form.baseData.projectCity"
                  :options="dictData.city"
                  placeholder="请选择"
                  :dictName="`${getCurrentProvince().value}地市`"
                  dictCode="city"
                  :hasParent="true"
                  :parentData="getCurrentProvince()"
                  clearable
                  filterable
                  @queryDictList="queryCityDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="主体名称" prop="baseData.subjectSelectName">
                <dict-select
                  v-model="form.baseData.subjectSelectName"
                  :options="dictData.subject_select"
                  placeholder="请选择"
                  dictName="主体名称"
                  dictCode="subject_select"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="项目阶段" prop="baseData.projectStage">
                <dict-select
                  v-model="form.baseData.projectStage"
                  :options="dictData.project_stage"
                  placeholder="请选择"
                  dictName="项目阶段"
                  dictCode="project_stage"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="最终用户名称"
                prop="baseData.subjectUserName"
              >
                <el-input
                  v-model="form.baseData.subjectUserName"
                  placeholder="请输入"
                  clearable
                  maxlength="20"
                ></el-input>
                <div class="text-info text-mini">
                  为便于项目冲突排查，请写用户组织全称
                </div>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="需求/客户立项名称"
                prop="baseData.requireClientProjectName"
              >
                <el-input
                  v-model="form.baseData.requireClientProjectName"
                  placeholder="请输入"
                  clearable
                  minlength="20"
                  maxlength="50"
                ></el-input>
                <div class="text-info text-mini">
                  如客户侧尚未立项，填写客户需求；如后续确定最终项目名称，可联系销售管理部更新
                </div>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="项目名称" prop="baseData.projectName">
                <el-input
                  v-model="projectName"
                  placeholder=""
                  disabled
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="项目编号" prop="baseData.projectNum">
                <el-input
                  v-model="form.baseData.projectNum"
                  placeholder=""
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>

            <el-col :span="12">
              <sw-form-item
                label="预计签约金额(元)"
                prop="baseData.expectedSignValue"
              >
                <el-input
                  v-model="form.baseData.expectedSignValue"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="预计签约日期"
                prop="baseData.expectedSignDate"
              >
                <el-date-picker
                  v-model="form.baseData.expectedSignDate"
                  type="date"
                  placeholder="请选择日期"
                  value-format="yyyy-MM-dd"
                  format="yyyy-MM-dd"
                  clearable
                >
                </el-date-picker>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="该项目已签约金额(元)"
                prop="baseData.signedContractAmount"
              >
                <el-input
                  v-model="form.baseData.signedContractAmount"
                  placeholder="请输入"
                  clearable
                ></el-input>
                <!-- TODO 根据关联本项目的合同订单总额自动取值计算合计 前端or后端？ -->
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <!-- =预计-已签约 -->
              <sw-form-item
                label="后续预计签约金额(元)"
                prop="baseData.afterSignedContractAmount"
              >
                <el-input
                  v-model="afterSignedContractAmount"
                  placeholder=""
                  disabled
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="风险类型" prop="baseData.riskType">
                <template slot="label">
                  风险类型
                  <el-tooltip
                    class="mgl5"
                    effect="dark"
                    content="xxx"
                    placement="top-end"
                  >
                    <span class="el-icon-question"></span>
                  </el-tooltip>
                </template>
                <dict-select
                  v-model="form.baseData.riskType"
                  :options="dictData.risk_type"
                  placeholder="请选择"
                  dictName="风险类型"
                  dictCode="risk_type"
                  clearable
                  @queryDictList="queryDictList"
                />
                TODO 风险类型说明信息占位。。。
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="项目团队成员"
                prop="baseData.projectPersonDatas"
              >
                <el-select
                  v-model="form.baseData.projectPersonDatas"
                  placeholder="请输入"
                  clearable
                  multiple
                ></el-select>
                TODO 组织架构写完再回来加 多选
              </sw-form-item>
            </el-col>
            <el-col :span="24">
              <sw-form-item
                label="项目失败原因"
                prop="baseData.projectFailureReason"
              >
                <el-input
                  type="textarea"
                  maxlength="1000"
                  show-word-limit
                  rows="4"
                  v-model="form.baseData.projectFailureReason"
                  placeholder="请输入"
                  clearable
                ></el-input>
                TODO 项目状态失败时必填
              </sw-form-item>
            </el-col>
            <el-col :span="24">
              <sw-form-item label="备注" prop="baseData.remark">
                <el-input
                  type="textarea"
                  maxlength="1000"
                  show-word-limit
                  rows="4"
                  v-model="form.baseData.remark"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="Channel">
          <base-title title="合作渠道"></base-title>

          <el-row :gutter="20">
            <el-col :span="22" :offset="2">
              <div>
                <search-table-content
                  class="full-width"
                  ref="searchTableRef"
                  :columns="columns"
                  :tableData="form.cooperationChannels || []"
                  :table-loading="loading"
                  :tablePage="tablePage"
                  @reset="reset"
                  @query="query"
                  @pagination="pagination"
                  :isFooter="false"
                  :inner="true"
                >
                  <template v-slot:actions>
                    <action-btns :btns="actionBtns" />
                  </template>
                  <template #confirmSignContract="{ data }">
                    <el-switch
                      v-model="data.confirmSignContract"
                      active-value="是"
                      inactive-value="否"
                      @change="(val) => changeDefault(val, data)"
                    >
                    </el-switch>
                  </template>
                  <template #remark="{ data, index }">
                    <sw-form-item
                      label=""
                      :prop="`cooperationChannels.${index}.remark`"
                      label-width="0"
                      style="margin-top: 16px"
                    >
                      <el-input
                        v-model="data.remark"
                        placeholder="请输入"
                        clearable
                      ></el-input>
                    </sw-form-item>
                  </template>
                  <template #operation="{ data }">
                    <operatebtns
                      :row="data"
                      :btns="operateBtns"
                      :limit="operbtnsLimit"
                    ></operatebtns>
                  </template>
                </search-table-content>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="Plan">
          <base-title title="风险/计划"></base-title>
          <el-row :gutter="20">
            <el-col :span="24">
              <div v-if="form.followProjectPlans.length > 0">
                <el-row
                  v-for="(item, index) in form.followProjectPlans"
                  :key="index"
                  :gutter="10"
                >
                  <el-col :span="21">
                    <sw-form-item
                      :prop="`followProjectPlans.${index}.planContent`"
                      :key="`followProjectPlans.${item.id}.planContent`"
                      :rules="rules.planContent"
                      :label="index == 0 ? '风险/计划' : ''"
                    >
                      <el-input
                        v-model="item.planContent"
                        placeholder="请输入"
                        clearable
                        minlength="100"
                        maxlength="150"
                      >
                      </el-input>
                    </sw-form-item>
                  </el-col>
                  <el-col :span="2">
                    <span class="oper-icon-btn-box">
                      <i
                        class="iconfont icon-tianjia-line pending-color"
                        v-if="
                          index == form.followProjectPlans.length - 1 &&
                          form.followProjectPlans.length < 10
                        "
                        @click="onAddClick"
                        :title="$t('common.add')"
                      ></i>
                      <i
                        class="iconfont icon-shanchu fail-color"
                        @click="onDeleteClick(index)"
                        v-if="form.followProjectPlans.length > 0"
                        :title="$t('common.delete')"
                      ></i>
                    </span>
                  </el-col>
                </el-row>
              </div>

              <div class="mgb16" v-else>
                <sw-form-item label="">
                  <el-button type="text" @click="onAddClick">
                    <i
                      class="iconfont icon-add-tag"
                      style="font-size: 12px"
                    ></i>
                    新增风险/计划</el-button
                  >
                </sw-form-item>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="Business">
          <base-title title="商务信息"></base-title>
          <el-row :gutter="20">
            <el-col :span="12">
              <sw-form-item
                label="所属行业大类"
                prop="baseData.industryBigCategory"
              >
                <dict-select
                  v-model="form.baseData.industryBigCategory"
                  :options="dictData.major_big_categories"
                  placeholder="请选择"
                  dictName="所属行业大类"
                  dictCode="major_big_categories"
                  clearable
                  @queryDictList="queryDictList"
                  @change="onChangeBigCate"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="所属行业小类"
                prop="baseData.industryLittleCategory"
              >
                <dict-select
                  v-model="form.baseData.industryLittleCategory"
                  :options="dictData[smallCateDictCode] || []"
                  placeholder="请选择"
                  dictName="所属行业小类"
                  :dictCode="smallCateDictCode"
                  clearable
                  @queryDictList="querySmallDictList"
                />
              </sw-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="right-content-item" id="Docking">
          <base-title title="对接信息"></base-title>
          <el-row :gutter="20">
            <el-col :span="12">
              <sw-form-item label="客户编号" prop="baseData.customerNum">
                <el-input
                  v-model="form.baseData.customerNum"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="是否已传U9"
                prop="baseData.confirmTransferU9"
              >
                <el-input
                  v-model="form.baseData.confirmTransferU9"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="传U9时间" prop="baseData.transferU9Date">
                <el-input
                  v-model="form.baseData.transferU9Date"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="对应U9单据编号"
                prop="baseData.transferU9Num"
              >
                <el-input
                  v-model="form.baseData.transferU9Num"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="对接错误信息" prop="baseData.transferU9Msg">
                <el-input
                  v-model="form.baseData.transferU9Msg"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="已传分贝通"
                prop="baseData.confirmTransferFenbeitong"
              >
                <el-input
                  v-model="form.baseData.confirmTransferFenbeitong"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="分贝通同步时间"
                prop="baseData.transferFenbeitongDate"
              >
                <el-input
                  v-model="form.baseData.transferFenbeitongDate"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="项目三方ID"
                prop="baseData.transferFenbeitongThirdId"
              >
                <el-input
                  v-model="form.baseData.transferFenbeitongThirdId"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="分贝通对接信息"
                prop="baseData.transferFenbeitongMsg"
              >
                <el-input
                  v-model="form.baseData.transferFenbeitongMsg"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </menu-form-content>
    <div slot="footer" class="page-content-footer">
      <el-button
        class="oper-btn cancel-btn mgr10"
        @click="onCloseClick"
        :loading="loading"
        >{{ $t("common.cancel") }}</el-button
      >
      <el-button
        type="primary"
        class="oper-btn"
        :loading="loading"
        @click="onSubmitClick"
        >{{ $t("common.submit") }}</el-button
      >
    </div>
    <batchAddCustomer
      :dialogVisible="batchAddDialog.dialogVisible"
      :rowObj="batchAddDialog.rowObj"
      :selectedList="(() => this.form.cooperationChannels)()"
      @eventClose="batchAddDialog.dialogVisible = false"
      @eventSucc="eventSuccAddCustomer"
    ></batchAddCustomer>
  </content-detail>
</template>
<script>
import {
  getOpportunityDetail,
  addOpportunity,
  editOpportunity,
} from "@/api/business/projectOpportunity";
import { getUuid } from "@/utils/util";
import Page from "@/components/searchTableContent/mixins/page";
import batchAddCustomer from "@/views/components/batchAddCustomer/index.vue";
import { cloneDeep } from "lodash";
import { getSysDict } from "@/api/dict/index.js";

let defaultInfo = {
  baseData: {
    subjectSelectName: "",
    salesPerson: "test负责人",
    salesPersonId: "test负责人id",
    salesPersonDept: "test部门",
    salesPersonDeptId: "test部门id",
    subjectUserName: "",
    requireClientProjectName: "",
    projectName: "",
    projectNum: "",
    projectStage: "",
    projectProvince: "",
    projectCity: "",
    industryBigCategory: "",
    industryLittleCategory: "",
    projectFailureReason: "",
    remark: "",
    expectedSignValue: "",
    expectedSignDate: "",
    signedContractAmount: "",
    afterSignedContractAmount: "",
    riskType: "",
    projectPersonDatas: [],
    customerNum: "",
    confirmTransferU9: "",
    transferU9Date: "",
    transferU9Num: "",
    transferU9Msg: "",
    confirmTransferFenbeitong: "",
    transferFenbeitongDate: "",
    transferFenbeitongThirdId: "",
    transferFenbeitongMsg: "",
  },
  cooperationChannels: [],
  followProjectPlans: [],
};

let channelOption = {
  //   projectId: "",
  //   planNo: "",
  customerId: "",
  customerName: "",
  customerSubjectSelectName: "",
  confirmSignContract: "",
  remark: "",
};
let planOption = {
  //   projectId: "",
  //   planNo: "",
  planContent: "",
};
export default {
  name: "businessOpportunityAdd",
  components: { batchAddCustomer },
  mixins: [Page],
  data() {
    return {
      isEdit: false,
      loading: false,
      dictData: {
        subject_select: [],
        internal_project_level: [],
        province: [],
        // city: [],
        project_stage: [],
        risk_type: [],
        major_big_categories: [],
      },
      typeList: [
        { label: "项目信息", value: "Project" },
        { label: "合作渠道", value: "Channel" },
        { label: "风险/计划", value: "Plan" },
        { label: "商务信息", value: "Business" },
        { label: "对接信息", value: "Docking" },
      ],
      form: this.cloneDeep(defaultInfo),
      rules: {
        "baseData.subjectSelectName": [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        "baseData.salesPersonId": [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        "baseData.subjectUserName": [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        "baseData.projectStage": [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        "baseData.projectProvince": [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        "baseData.projectCity": [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        "baseData.salesPerson": [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        "baseData.salesPersonDept": [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        "baseData.industryBigCategory": [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        "baseData.industryLittleCategory": [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        "baseData.riskType": [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        planContent: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
      },
      customerList: [],
      loading1: false,
      actionBtns: [
        {
          name: "新增客户档案",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddCustomerClick,
          permi: ["none:none:none"],
        },
      ],
      columns: [
        {
          label: "客户名称",
          prop: "customerName",
          minWidth: "120px",
        },
        {
          label: "客户主体",
          prop: "customerSubjectSelectName",
          minWidth: "120px",
        },
        {
          label: "客户级别",
          prop: "customerLevel",
          minWidth: "120px",
        },
        {
          label: "是否签约客户",
          prop: "confirmSignContract",
          render: "confirmSignContract",
          width: "120px",
        },
        {
          label: "备注",
          prop: "remark",
          render: "remark",
          minWidth: "160px",
        },
        {
          label: "操作",
          prop: "operation",
          render: "operation",
          width: "120px",
          align: "center",
          fixed: "right",
        },
      ],
      operateBtns: [
        {
          name: "移除",
          class: "delete-text-btn",
          permi: ["none:none:none"],
          func: this.onDeleteCustomerClick,
        },
      ],
      initQuery: false,
      batchAddDialog: {
        dialogVisible: false,
        rowObj: {},
      },
    };
  },
  computed: {
    queryData() {
      return this.$route.query;
    },
    // 项目名称
    projectName() {
      this.form.baseData.projectName =
        this.form.baseData.subjectUserName +
        this.form.baseData.requireClientProjectName;
      return this.form.baseData.projectName;
    },
    // 自动计算金额
    afterSignedContractAmount() {
      const expectedSignValue =
        parseFloat(this.form.baseData.expectedSignValue) || 0;
      const signedContractAmount =
        parseFloat(this.form.baseData.signedContractAmount) || 0;
      this.form.baseData.afterSignedContractAmount =
        expectedSignValue - signedContractAmount;
      return this.form.baseData.afterSignedContractAmount;
    },
    // 小类 字典code
    smallCateDictCode() {
      if (this.form.baseData.industryBigCategory) {
        let currentObj = this.dictData.major_big_categories.find(
          (item) => item.value == this.form.baseData.industryBigCategory,
        );
        if (currentObj && currentObj.extendData) {
          return (
            currentObj.extendData.find((item) => item.name == "level_2")?.val ||
            ""
          );
        }
      }
      return "";
    },
    // 签约客户档案
    currentCustomer() {
      let obj = {};
      if (this.form?.cooperationChannels?.length) {
        obj =
          this.form.cooperationChannels.find(
            (item) => item.confirmSignContract == "是",
          ) || {};
      }
      return obj;
    },
  },
  created() {
    this.isEdit = !!this.$route.query.id;
    this.initData();
    this.getDictList();
  },

  methods: {
    getCurrentProvince() {
      let currentObj = this.dictData.province.find(
        (item) => item.value == this.form.baseData.projectProvince,
      );
      return currentObj || {};
    },
    onChangeBigCate(val) {
      this.form.baseData.industryLittleCategory = "";
      // 二级下拉
      this.querySmallDictList();
    },
    onChangeProvince(val) {
      this.form.baseData.projectCity = "";
      this.queryCityDictList();
    },
    queryDictList() {
      console.log("重新获取字典");
      this.getDictList();
    },
    querySmallDictList() {
      this.getSysDictData([this.smallCateDictCode]).then((res) => {
        this.$set(
          this.dictData,
          [this.smallCateDictCode],
          res.data[this.smallCateDictCode] || [],
        );
      });
    },
    queryCityDictList() {
      let currentObj = this.dictData.province.find(
        (item) => item.value == this.form.baseData.projectProvince,
      );
      // 地市下拉
      if (currentObj) {
        getSysDict({
          dictCode: "city",
          parentCode: currentObj.dictCode,
          parentValue: currentObj.dictValue,
        }).then((res) => {
          this.$set(
            this.dictData,
            "city",
            (res.data || []).map((i) => {
              return {
                ...i,
                value: i.dictNameCn,
                label: i.dictNameCn,
                typeCode: i.typeCode,
                extendData: i.extendData,
              };
            }),
          );
        });
      } else {
        this.$set(this.dictData, "city", []);
      }
    },
    initData() {
      this.form = this.cloneDeep(defaultInfo);
      if (this.isEdit) {
        getOpportunityDetail({ id: this.queryData.id }).then((res) => {
          this.form = {
            ...this.cloneDeep(defaultInfo),
            ...res.data,
            cooperationChannels: res.data.cooperationChannels || [],
            followProjectPlans: res.data.followProjectPlans || [],
          };
          if (this.form.baseData.industryBigCategory) {
            this.querySmallDictList();
          }
        });
      }
    },
    onDeleteClick(index) {
      this.form.followProjectPlans.splice(index, 1);
    },
    onAddClick() {
      this.form.followProjectPlans.push({
        ...this.cloneDeep(planOption),
        id: getUuid(),
      });
    },
    changeDefault(val, row) {
      row.confirmSignContract = val == "是" ? "否" : "是";
      if (val == "是") {
        this.form.cooperationChannels.forEach((i) => {
          i.confirmSignContract = "否";
        });
      }
      row.confirmSignContract = val;
    },
    onAddCustomerClick() {
      this.batchAddDialog.dialogVisible = true;
      this.batchAddDialog.rowObj = {};
    },
    eventSuccAddCustomer(list) {
      list.forEach((row) => {
        this.form.cooperationChannels.push({
          ...this.cloneDeep(channelOption),
          customerId: row.id,
          customerName: row.customerName,
          customerSubjectSelectName: row.corporation,
          customerLevel: row.customerLevel,
        });
      });
    },
    onDeleteCustomerClick(row) {
      this.form.cooperationChannels = this.form.cooperationChannels.filter(
        (item) => item.customerId !== row.customerId,
      );
    },
    onCloseClick() {
      this.$refs.formRef?.resetFields();
      this.$router.go(-1);
    },
    onSubmitClick() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          if (this.form.cooperationChannels.length === 0) {
            this.errorMsg("请至少选择一个客户档案");
            return false;
          }
          let request = this.isEdit ? editOpportunity : addOpportunity;
          this.loading = true;
          request(this.form)
            .then((res) => {
              this.successMsg(this.isEdit ? "编辑成功" : "新增成功");
              this.onCloseClick();
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.oper-icon-btn-box {
  line-height: 32px;
  display: inline-block;
  i {
    cursor: pointer;
  }
  i + i {
    margin-left: 8px;
  }
}
</style>
