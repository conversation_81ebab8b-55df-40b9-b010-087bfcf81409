<template>
  <div class="full-height full-width page-container">
    <smart-search-table-content
      class="full-height full-width"
      ref="searchTableRef"
      :searchForm="searchForm"
      :advanceSearchForm="advanceSearchForm"
      :columns="columns"
      :tableData="tableData"
      :table-loading="loading"
      :tablePage="tablePage"
      @reset="reset"
      @query="queryBase"
      @pagination="pagination"
      :isSelected="true"
      :isAdvance="isAdvance"
      @queryAdvance="queryAdvance"
      @selectionChange="selectionChange"
      :selectable="selectable"
    >
      <template #pageTitle>
        <page-header title="项目机会跟进">
          <!-- <template #titleWarning>{{ $t("pageTitle.apiKey.desc") }}</template> -->
        </page-header>
      </template>
      <template #search>
        <search-item label="主体名称" prop="subjectSelectName">
          <el-select
            v-model="searchForm.subjectSelectName"
            :placeholder="$t('placeholder.select')"
            clearable
          >
            <el-option
              v-for="item in dictData.subject_select"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            ></el-option>
          </el-select>
        </search-item>
      </template>
      <template v-slot:actions>
        <batch-action-btns
          :btns="actionBtns"
          :batchBtns="batchBtns"
          :limit="batchLimit"
          :showBatch="selectList.length > 0"
          :getSelectList="() => selectList"
          :isSelectTotal="isSelectTotal"
          :total="tablePage.total"
          @onSelectTotalClick="onSelectTotalClick"
          @clearSelectList="clearSelectList"
        />
      </template>
      <template #operation="{ data }">
        <operatebtns
          :row="data"
          :btns="operateBtns"
          :limit="operbtnsLimit"
        ></operatebtns>
      </template>
    </smart-search-table-content>
  </div>
</template>

<script>
import Page from "@/components/smartSearchTableContent/mixins/page";
import {
  getOpportunityList,
  deleteOpportunity,
} from "@/api/business/projectOpportunity";
import { getMock } from "@/utils/util";

export default {
  mixins: [Page],
  components: {},
  data() {
    return {
      addDialog: {
        dialogVisible: false,
        rowObj: {},
        isEdit: false,
      },
      detailDialog: {
        dialogVisible: false,
        rowObj: {},
      },
      actionBtns: [
        {
          name: "新建",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "查重",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onCheckClick,
          permi: ["none:none:none"],
        },
        {
          name: "导入",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-shangchuan1-line",
          func: this.onImportClick,
          permi: ["none:none:none"],
        },
        {
          name: "导出",
          id: "exportBtnId",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-daochu",
          func: this.onExportClick,
          permi: ["none:none:none"],
        },
      ],
      batchBtns: [
        {
          name: "分配",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
          rule: () => {
            return true;
          },
        },
        {
          name: "删除",
          class: "danger-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "导出",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "共享",
          class: "oper-text-btn",
          icon: "iconfont icon-shangchuan1-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "批量编辑",
          class: "oper-text-btn",
          icon: "iconfont icon-daochu",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "提交审批",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "下载全部附件",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
          rule: () => {
            return true;
          },
        },
      ],
      columns: [
        {
          label: "主体名称",
          prop: "subjectSelectName",
        },
        {
          label: "项目地市",
          prop: "projectCity",
        },
        {
          label: "项目省份",
          prop: "projectProvince",
        },
        {
          label: "项目阶段",
          prop: "projectStage",
        },
        {
          label: "项目编号",
          prop: "projectNum",
        },
        {
          label: "项目名称",
          prop: "projectName",
        },
        {
          label: "客户级别",
          prop: "customerLevel",
        },
        {
          label: "所属行业小类",
          prop: "industryLittleCategory",
        },
        {
          label: "所属行业大类",
          prop: "industryBigCategory",
        },
        {
          label: "销售部门",
          prop: "salesPersonDept",
        },
        {
          label: "销售负责人",
          prop: "salesPerson",
        },
        {
          label: "风险类型",
          prop: "riskType",
        },
        // {
        //   label: "主键id",
        //   prop: "id",
        // },
        {
          label: "最终用户名称",
          prop: "subjectUserName",
        },
        {
          label: "需求/客户立项名称",
          prop: "requireClientProjectName",
        },
        {
          label: "项目失败原因",
          prop: "projectFailureReason",
        },
        {
          label: "备注",
          prop: "remark",
        },
        {
          label: "预计签约金额(元)",
          prop: "expectedSignValue",
        },
        {
          label: "预计签约日期",
          prop: "expectedSignDate",
        },
        {
          label: "该项目已签约金额(元)",
          prop: "signedContractAmount",
        },
        {
          label: "后续预计签约金额(元)",
          prop: "afterSignedContractAmount",
        },
        {
          label: "项目团队成员",
          prop: "projectPersons",
        },
        {
          label: "客户编号",
          prop: "customerNum",
        },
        {
          label: "是否已传U9",
          prop: "confirmTransferU9",
        },
        {
          label: "传U9时间",
          prop: "transferU9Date",
        },
        {
          label: "对应U9单据编号",
          prop: "transferU9Num",
        },
        {
          label: "对接错误信息",
          prop: "transferU9Msg",
        },
        {
          label: "已传分贝通",
          prop: "confirmTransferFenbeitong",
        },
        {
          label: "分贝通同步时间",
          prop: "transferFenbeitongDate",
        },
        // {
        //   label: "项目三方ID",
        //   prop: "transferFenbeitongThirdId",
        // },
        {
          label: "分贝通对接信息",
          prop: "transferFenbeitongMsg",
        },
        {
          label: "更新时间",
          prop: "updateTime",
        },
        {
          label: "创建时间",
          prop: "createTime",
        },
        {
          label: "操作",
          prop: "operation",
          render: "operation",
          width: "120px",
          align: "center",
          fixed: "right",
        },
      ],
      operateBtns: [
        {
          name: this.$t("common.detail"),
          class: "oper-text-btn",
          permi: ["none:none:none"],
          func: this.onDetailClick,
        },
        {
          name: this.$t("common.edit"),
          class: "oper-text-btn",
          permi: ["none:none:none"],
          func: this.onEditClick,
        },
        {
          name: this.$t("common.delete"),
          class: "delete-text-btn",
          permi: ["none:none:none"],
          func: (row) => {
            this.confirmInfoOperFun(
              this.$t("tips.deleteTip"),
              deleteOpportunity,
              { id: row.id },
              this.$t("msg.delete"),
            );
          },
        },
      ],
      // 查询参数
      searchForm: {
        subjectSelectName: "",
        name: "",
      },
      dictData: {
        subject_select: [],
      },
    };
  },
  created() {
    this.getDictList();
    console.log(
      getMock(
        {
          id: { type: "string" },
          contractId: {
            type: "string",
            description: " 合同id字符串 必选  长度64",
          },
          productCode: {
            type: "string",
            description: ", U9编码 必选  长度64",
          },
          contractProductName: {
            type: "string",
            description: " 合同产品名称  必选  字符串  长度200",
          },
          contractProductType: {
            type: "string",
            description: " 合同规格型号  必选  字符串  长度200",
          },
          productNum: {
            type: "integer",
            description: "数量  必选  int  长度10",
          },
          productUnit: {
            type: "string",
            description: "单位  必选  字符串  长度20",
          },
          productTax: {
            type: "number",
            description: "税  必选  double 长度10",
          },
          paymentCount: {
            type: "number",
            description: "应付金额   ,必选",
          },
          unitTaxPrice: {
            type: "number",
            description: ", 含税单价  必选  decimal(16，6)",
          },
          noUnitTaxPrice: {
            type: "number",
            description: " ,不含税单价  必选  decimal(16，6)",
          },
          totalAmountPrice: {
            type: "number",
            description: ",价税合计  必选  decimal(16，6)",
          },
          noTotalAmountPrice: {
            type: "number",
            description: ",不含税金额  必选  decimal(16，6)",
          },
          taxPrice: {
            type: "number",
            description: " ,税额  必选  decimal(16，6)",
          },
          softwareTaxRefund: {
            type: "string",
            description: "软件是否可退税   必选  下拉是/否  字符串 长度20",
          },
          productNo: { type: "string", description: "产品序列号" },
          produceNo: { type: "string", description: "生产批号" },
          deliverNum: { type: "integer", description: "合同已发货数量" },
          noDeliverNum: {
            type: "integer",
            description: "合同未发货数量",
          },
          supplyNum: { type: "integer", description: "实际供货数量" },
          bussComplexIndex: {
            type: "number",
            description: "业务难度系数",
          },
          convertContractAmount: {
            type: "number",
            description: "折算后合同金额",
          },
          remark: { type: "string", description: "备注" },
        },
        {
          id: { type: "string" },
          contractId: {
            type: "string",
            description: " 合同id字符串 必选  长度64",
          },
          productCode: {
            type: "string",
            description: ", U9编码 必选  长度64",
          },
          contractProductName: {
            type: "string",
            description: " 合同产品名称  必选  字符串  长度200",
          },
          contractProductType: {
            type: "string",
            description: " 合同规格型号  必选  字符串  长度200",
          },
          productNum: {
            type: "integer",
            description: "数量  必选  int  长度10",
          },
          productUnit: {
            type: "string",
            description: "单位  必选  字符串  长度20",
          },
          productTax: {
            type: "number",
            description: "税  必选  double 长度10",
          },
          paymentCount: {
            type: "number",
            description: "应付金额   ,必选",
          },
          unitTaxPrice: {
            type: "number",
            description: ", 含税单价  必选  decimal(16，6)",
          },
          noUnitTaxPrice: {
            type: "number",
            description: " ,不含税单价  必选  decimal(16，6)",
          },
          totalAmountPrice: {
            type: "number",
            description: ",价税合计  必选  decimal(16，6)",
          },
          noTotalAmountPrice: {
            type: "number",
            description: ",不含税金额  必选  decimal(16，6)",
          },
          taxPrice: {
            type: "number",
            description: " ,税额  必选  decimal(16，6)",
          },
          softwareTaxRefund: {
            type: "string",
            description: "软件是否可退税   必选  下拉是/否  字符串 长度20",
          },
          productNo: { type: "string", description: "产品序列号" },
          produceNo: { type: "string", description: "生产批号" },
          deliverNum: { type: "integer", description: "合同已发货数量" },
          noDeliverNum: {
            type: "integer",
            description: "合同未发货数量",
          },
          supplyNum: { type: "integer", description: "实际供货数量" },
          bussComplexIndex: {
            type: "number",
            description: "业务难度系数",
          },
          convertContractAmount: {
            type: "number",
            description: "折算后合同金额",
          },
          remark: { type: "string", description: "备注" },
        },
      ),
    );
  },
  methods: {
    request: getOpportunityList,

    onAddClick() {
      this.$router.push({
        name: "businessOpportunityAdd",
        query: {},
      });
    },
    onCheckClick() {},
    onImportClick() {},
    onExportClick() {},
    onDetailClick(row) {},
    onEditClick(row) {
      this.$router.push({
        name: "businessOpportunityAdd",
        query: {
          id: row.id,
          name: row.projectName,
        },
      });
    },
  },
  beforeDestroy() {},
};
</script>
