<template>
  <div class="full-height full-width page-container">
    <smart-search-table-content
      class="full-height full-width"
      ref="searchTableRef"
      :searchForm="searchForm"
      :advanceSearchForm="advanceSearchForm"
      :columns="columns"
      :tableData="tableData"
      :table-loading="loading"
      :tablePage="tablePage"
      @reset="reset"
      @query="queryBase"
      @pagination="pagination"
      :isSelected="true"
      :isAdvance="isAdvance"
      @queryAdvance="queryAdvance"
      @selectionChange="selectionChange"
      :selectable="selectable"
    >
      <template #pageTitle>
        <page-header title="客户档案">
          <!-- <template #titleWarning>{{ $t("pageTitle.apiKey.desc") }}</template> -->
        </page-header>
      </template>
      <template #search>
        <search-item label="客户编号" prop="customerCode">
          <el-input
            v-model="searchForm.customerCode"
            :placeholder="$t('placeholder.place')"
            clearable
          />
        </search-item>
        <search-item label="客户名称" prop="customerName">
          <el-input
            v-model="searchForm.customerName"
            :placeholder="$t('placeholder.place')"
            clearable
          />
        </search-item>
      </template>
      <template v-slot:actions>
        <batch-action-btns
          :btns="actionBtns"
          :batchBtns="batchBtns"
          :limit="batchLimit"
          :showBatch="selectList.length > 0"
          :getSelectList="() => selectList"
          :isSelectTotal="isSelectTotal"
          :total="tablePage.total"
          @onSelectTotalClick="onSelectTotalClick"
          @clearSelectList="clearSelectList"
        />
      </template>
      <template #operation="{ data }">
        <operatebtns
          :row="data"
          :btns="operateBtns"
          :limit="operbtnsLimit"
        ></operatebtns>
      </template>
    </smart-search-table-content>
  </div>
</template>

<script>
import Page from "@/components/smartSearchTableContent/mixins/page";
import { getCustomerList, deleteCustomer } from "@/api/business/customer";
export default {
  mixins: [Page],
  components: {},
  data() {
    return {
      addDialog: {
        dialogVisible: false,
        rowObj: {},
        isEdit: false,
      },
      detailDialog: {
        dialogVisible: false,
        rowObj: {},
      },
      actionBtns: [
        {
          name: "新建",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "查重",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onCheckClick,
          permi: ["none:none:none"],
        },
        {
          name: "导入",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-shangchuan1-line",
          func: this.onImportClick,
          permi: ["none:none:none"],
        },
        {
          name: "导出",
          id: "exportBtnId",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-daochu",
          func: this.onExportClick,
          permi: ["none:none:none"],
        },
      ],
      batchBtns: [
        {
          name: "分配",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
          rule: () => {
            return true;
          },
        },
        {
          name: "删除",
          class: "danger-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "导出",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "共享",
          class: "oper-text-btn",
          icon: "iconfont icon-shangchuan1-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "批量编辑",
          class: "oper-text-btn",
          icon: "iconfont icon-daochu",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "提交审批",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "下载全部附件",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
          rule: () => {
            return true;
          },
        },
      ],
      columns: [
        {
          label: "客户编号",
          prop: "customerCode",
        },
        {
          label: "主体名称",
          prop: "corporation",
        },
        {
          label: "客户集",
          prop: "customerGroup",
        },
        {
          label: "客户名称",
          prop: "customerName",
        },
        {
          label: "销售部门",
          prop: "salesDepartment",
        },
        {
          label: "销售负责人",
          prop: "salesman",
        },
        {
          label: "客户来源",
          prop: "customerSource",
        },
        {
          label: "是否为战略客户",
          prop: "strategicCustomerFlag",
        },
        {
          label: "资源描述",
          prop: "resourceDescription",
        },
        {
          label: "团队成员",
          prop: "teamMembers",
        },
        {
          label: "业务说明",
          prop: "businessDescription",
        },
        {
          label: "曾用名",
          prop: "formerName",
        },
        {
          label: "成交状态",
          prop: "transactionState",
        },
        {
          label: "客户级别",
          prop: "customerLevel",
        },
        {
          label: "合作领域/行业",
          prop: "industry",
        },
        {
          label: "渠道类型",
          prop: "channelType",
        },
        {
          label: "合作价格折扣-硬件",
          prop: "discountHardware",
        },
        {
          label: "合作价格折扣-软件",
          prop: "discountSoftware",
        },
        {
          label: "渠道协议",
          prop: "channelAgreement",
        },
        {
          label: "协议起始日期",
          prop: "agreementStart",
        },
        {
          label: "协议截止日期",
          prop: "agreementEnd",
        },
        {
          label: "是否申请延迟协议周期",
          prop: "agreementExtendFlag",
        },
        {
          label: "证书编号",
          prop: "certificateNumber",
        },
        {
          label: "考核任务额",
          prop: "taskAmount",
        },
        {
          label: "考核期内合同签约额",
          prop: "contractSigningAmount",
        },
        {
          label: "跟进情况",
          prop: "followUp",
        },
        {
          label: "注册时间",
          prop: "registeredDate",
        },
        {
          label: "注册资金",
          prop: "registeredCapital",
        },
        {
          label: "注册行业",
          prop: "registeredIndustry",
        },
        {
          label: "企业性质",
          prop: "enterpriseNature",
        },
        {
          label: "法定代表人",
          prop: "legalRepresentative",
        },
        {
          label: "官网网址",
          prop: "officialWebsite",
        },
        {
          label: "人员规模",
          prop: "staffSize",
        },
        {
          label: "社保人数",
          prop: "socialSecurityNumber",
        },
        {
          label: "经营范围",
          prop: "businessScope",
        },
        {
          label: "公司Email",
          prop: "companyEmail",
        },
        {
          label: "公司电话",
          prop: "companyTel",
        },
        {
          label: "国家",
          prop: "country",
        },
        {
          label: "省份",
          prop: "province",
        },
        {
          label: "城市",
          prop: "city",
        },
        {
          label: "区域",
          prop: "region",
        },
        {
          label: "详细地址",
          prop: "address",
        },
        {
          label: "是否已传U9",
          prop: "u9Flag",
        },
        {
          label: "传U9时间",
          prop: "u9Time",
        },
        {
          label: "U9单据号",
          prop: "u9Code",
        },
        {
          label: "U9错误信息",
          prop: "u9Error",
        },
        {
          label: "已传分贝通",
          prop: "fbtFlag",
        },
        {
          label: "传分贝通时间",
          prop: "fbtTime",
        },
        {
          label: "分贝通对接信息",
          prop: "fbtError",
        },
        {
          label: "客户三方ID",
          prop: "fbtCode",
        },
        {
          label: "重传开票信息",
          prop: "u9Arq",
        },
        {
          label: "重传U9时间",
          prop: "u9ArqTime",
        },
        // {
        //   label: "扩展字段1",
        //   prop: "extend1",
        // },
        // {
        //   label: "扩展字段2",
        //   prop: "extend2",
        // },
        // {
        //   label: "扩展字段3",
        //   prop: "extend3",
        // },
        // {
        //   label: "创建人",
        //   prop: "createBy",
        // },
        {
          label: "创建时间",
          prop: "createTime",
        },
        // {
        //   label: "更新人",
        //   prop: "updateBy",
        // },
        {
          label: "更新时间",
          prop: "updateTime",
        },
        {
          label: "联系人姓名",
          prop: "name",
        },
        {
          label: "联系人职务",
          prop: "position",
        },
        {
          label: "联系人手机号",
          prop: "mobile",
        },
        {
          label: "联系人电话",
          prop: "tel",
        },
        {
          label: "联系人微信",
          prop: "wechat",
        },
        {
          label: "联系人部门",
          prop: "department",
        },
        {
          label: "公司名称",
          prop: "companyName",
        },
        {
          label: "开户行",
          prop: "bankName",
        },
        {
          label: "电话",
          prop: "phoneNumber",
        },
        {
          label: "纳税人资质",
          prop: "taxpayerType",
        },
        {
          label: "纳税人识别号",
          prop: "taxpayerId",
        },
        {
          label: "银行账户",
          prop: "bankAccount",
        },
        {
          label: "开票地址",
          prop: "invoiceAddress",
        },
        {
          label: "操作",
          prop: "operation",
          render: "operation",
          width: "120px",
          align: "center",
          fixed: "right",
        },
      ],
      operateBtns: [
        {
          name: this.$t("common.detail"),
          class: "oper-text-btn",
          permi: ["none:none:none"],
          func: this.onDetailClick,
        },
        {
          name: this.$t("common.edit"),
          class: "oper-text-btn",
          permi: ["none:none:none"],
          func: this.onEditClick,
        },
        {
          name: this.$t("common.delete"),
          class: "delete-text-btn",
          permi: ["none:none:none"],
          func: (row) => {
            this.confirmInfoOperFun(
              this.$t("tips.deleteTip"),
              deleteCustomer,
              { ids: [row.id] },
              this.$t("msg.delete"),
            );
          },
        },
      ],
      // 查询参数
      searchForm: {
        customerCode: "",
        customerName: "",
      },
      dictData: {},
    };
  },
  created() {
    this.getDictList();
  },
  methods: {
    request: getCustomerList,

    onAddClick() {
      this.$router.push({
        name: "businessCustomerAdd",
        query: {},
      });
    },
    onCheckClick() {},
    onImportClick() {},
    onExportClick() {},
    onDetailClick(row) {},
    onEditClick(row) {
      this.$router.push({
        name: "businessCustomerAdd",
        query: {
          id: row.id,
          name: row.customerName,
        },
      });
    },
  },
  beforeDestroy() {},
};
</script>
