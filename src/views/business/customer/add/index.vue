<template>
  <content-detail :content="isEdit ? `编辑-${queryData.name}` : '新增'">
    <menu-form-content :typeList="typeList">
      <el-form
        slot="rightContent"
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="auto"
        size="small"
        label-position="right"
      >
        <div class="right-content-item" id="basicInfo">
          <base-title title="基本信息"></base-title>
          <el-row :gutter="20">
            <el-col :span="12" v-if="form.customerCode">
              <sw-form-item label="客户编号" prop="customerCode">
                {{ form.customerCode }}
                <!-- <el-input
                v-model="form.customerCode"  
                placeholder="请输入"
                clearable
              ></el-input> -->
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="主体名称" prop="corporation">
                <dict-select
                  v-model="form.corporation"
                  :options="dictData.subject_select"
                  placeholder="请选择"
                  dictName="主体名称"
                  dictCode="subject_select"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <!-- TODO 默认等于客户名称，如有涉及通过创建、审批中编辑修改 -->
              <sw-form-item label="客户集" prop="customerGroup">
                <el-input
                  v-model="form.customerGroup"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="客户名称" prop="customerName">
                <el-input
                  v-model="form.customerName"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <!-- TODO 根据销售负责人自动填入 -->
              <sw-form-item label="销售部门" prop="salesDepartment">
                <el-input
                  disabled
                  v-model="form.salesDepartment"
                  placeholder=""
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <!-- TODO 默认当前创建人 可选 -->
              <sw-form-item label="销售负责人" prop="salesman">
                <el-input
                  v-model="form.salesman"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="客户来源" prop="customerSource">
                <dict-select
                  v-model="form.customerSource"
                  :options="dictData.customer_source"
                  placeholder="请选择"
                  dictName="客户来源"
                  dictCode="customer_source"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="是否战略客户" prop="strategicCustomerFlag">
                <dict-select
                  v-model="form.strategicCustomerFlag"
                  :options="dictData.strategic_customer"
                  placeholder="请选择"
                  dictName="是否战略客户"
                  dictCode="strategic_customer"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="资源描述" prop="resourceDescription">
                <el-input
                  v-model="form.resourceDescription"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <!-- TODO 多选成员 -->
              <sw-form-item label="团队成员" prop="teamMembers">
                <el-input
                  v-model="form.teamMembers"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="24">
              <sw-form-item label="业务说明" prop="businessDescription">
                <el-input
                  type="textarea"
                  maxlength="1000"
                  show-word-limit
                  rows="4"
                  v-model="form.businessDescription"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="曾用名" prop="formerName">
                <el-input
                  v-model="form.formerName"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="customerProperty">
          <base-title title="客户属性"></base-title>
          <el-row :gutter="20">
            <el-col :span="12">
              <!-- TODO 自动生成不可编辑？ -->
              <sw-form-item label="成交状态" prop="transactionState">
                <dict-select
                  v-model="form.transactionState"
                  :options="dictData.deal_status"
                  placeholder="请选择"
                  dictName="成交状态"
                  dictCode="deal_status"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="客户级别" prop="customerLevel">
                <dict-select
                  v-model="form.customerLevel"
                  :options="dictData.customer_level"
                  placeholder="请选择"
                  dictName="客户级别"
                  dictCode="customer_level"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="合作领域/行业" prop="industry">
                <el-input
                  v-model="form.industry"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="right-content-item" id="channelInfo">
          <base-title title="渠道管理信息"></base-title>
          <el-row :gutter="20">
            <el-col :span="12">
              <sw-form-item label="渠道类型" prop="channelType">
                <dict-select
                  v-model="form.channelType"
                  :options="dictData.channel_type"
                  placeholder="请选择"
                  dictName="渠道类型"
                  dictCode="channel_type"
                  clearable
                  @queryDictList="queryDictList"
                  @change="onChangeChannelType"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <!-- TODO 关联合同号（或触发的审批通过后反写合同号） -->
              <sw-form-item label="渠道协议" prop="channelAgreement">
                <el-input
                  v-model="form.channelAgreement"
                  placeholder=""
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12" v-if="form.discountHardware">
              <!-- 根据渠道类型带出 extendData-->
              <sw-form-item label="合作价格折扣-硬件" prop="discountHardware">
                <el-input
                  v-model="form.discountHardware"
                  placeholder=""
                  disabled
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12" v-if="form.discountSoftware">
              <!-- 根据渠道类型带出 -->
              <sw-form-item label="合作价格折扣-软件" prop="discountSoftware">
                <el-input
                  v-model="form.discountSoftware"
                  placeholder=""
                  disabled
                ></el-input>
              </sw-form-item>
            </el-col>

            <el-col :span="12">
              <sw-form-item label="协议起始日期" prop="agreementStart">
                <el-date-picker
                  v-model="form.agreementStart"
                  type="date"
                  placeholder="请选择日期"
                  value-format="yyyy-MM-dd"
                  format="yyyy-MM-dd"
                  clearable
                >
                </el-date-picker>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="协议截止日期" prop="agreementEnd">
                <el-date-picker
                  v-model="form.agreementEnd"
                  type="date"
                  placeholder="请选择日期"
                  value-format="yyyy-MM-dd"
                  format="yyyy-MM-dd"
                  clearable
                >
                </el-date-picker>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="是否申请延迟协议周期"
                prop="agreementExtendFlag"
              >
                <dict-select
                  v-model="form.agreementExtendFlag"
                  :options="dictData.extend_agreement_period"
                  placeholder="请选择"
                  dictName="是否申请延迟协议周期"
                  dictCode="extend_agreement_period"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="证书编号" prop="certificateNumber">
                <el-input
                  v-model="form.certificateNumber"
                  placeholder="审批通过后，销管手工填写"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="考核任务额" prop="taskAmount">
                <el-input
                  v-model="form.taskAmount"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <!-- 根据起止日期，关联累计合同金额 -->
              <sw-form-item
                label="考核期内合同签约额"
                prop="contractSigningAmount"
              >
                <el-input
                  v-model="form.contractSigningAmount"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="跟进情况" prop="followUp">
                <el-input
                  v-model="form.followUp"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="businessInfo">
          <!-- 工商信息中的内容均由企查查反写 -->
          <base-title title="工商信息"></base-title>
          <el-row :gutter="20">
            <el-col :span="12">
              <sw-form-item label="注册时间" prop="registeredDate">
                <el-date-picker
                  v-model="form.registeredDate"
                  type="date"
                  placeholder="请选择日期"
                  value-format="yyyy-MM-dd"
                  format="yyyy-MM-dd"
                  clearable
                >
                </el-date-picker>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="注册资金" prop="registeredCapital">
                <el-input
                  v-model="form.registeredCapital"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="注册行业" prop="registeredIndustry">
                <el-input
                  v-model="form.registeredIndustry"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="企业性质" prop="enterpriseNature">
                <el-input
                  v-model="form.enterpriseNature"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="法定代表人" prop="legalRepresentative">
                <el-input
                  v-model="form.legalRepresentative"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="官网网址" prop="officialWebsite">
                <el-input
                  v-model="form.officialWebsite"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="人员规模" prop="staffSize">
                <el-input
                  v-model="form.staffSize"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="设备人数" prop="socialSecurityNumber">
                <el-input
                  v-model="form.socialSecurityNumber"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="24">
              <sw-form-item label="经营范围" prop="businessScope">
                <el-input
                  type="textarea"
                  maxlength="1000"
                  show-word-limit
                  rows="4"
                  v-model="form.businessScope"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="公司Email" prop="companyEmail">
                <el-input
                  v-model="form.companyEmail"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="公司电话" prop="companyTel">
                <el-input
                  v-model="form.companyTel"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="contacts">
          <base-title title="联系人"></base-title>
          <el-row :gutter="20" v-if="!isEdit">
            <el-col :span="12">
              <sw-form-item label="姓名" prop="name">
                <el-input
                  v-model="form.name"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="职务" prop="position">
                <el-input
                  v-model="form.position"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="手机号" prop="mobile">
                <el-input
                  v-model="form.mobile"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="电话" prop="tel">
                <el-input
                  v-model="form.tel"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="微信" prop="wechat">
                <el-input
                  v-model="form.wechat"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="部门" prop="department">
                <el-input
                  v-model="form.department"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" v-else>
            <el-col :span="22" :offset="2">
              <div>
                <search-table-content
                  class="full-width"
                  ref="searchTableRef"
                  :columns="columns"
                  :tableData="form.bussContactInfoList || []"
                  :table-loading="loading"
                  :tablePage="tablePage"
                  @reset="reset"
                  @query="query"
                  @pagination="pagination"
                  :isFooter="false"
                  :inner="true"
                >
                </search-table-content>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="addressInfo">
          <!-- 企查查反写 -->
          <base-title title="地址信息"></base-title>
          <el-row :gutter="20">
            <el-col :span="12">
              <sw-form-item label="国家" prop="country">
                <el-input
                  v-model="form.country"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="省份" prop="province">
                <el-input
                  v-model="form.province"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="城市" prop="city">
                <el-input
                  v-model="form.city"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="区域" prop="region">
                <el-input
                  v-model="form.region"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="24">
              <sw-form-item label="详细地址" prop="address">
                <el-input
                  type="textarea"
                  maxlength="1000"
                  show-word-limit
                  rows="4"
                  v-model="form.address"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="invoiceInfo">
          <!-- TODO 开票信息所有字段：可新增，编辑/删除需要触发审批 审批后可编辑 -->
          <base-title title="开票信息"></base-title>
          <el-row :gutter="20">
            <el-col :span="12">
              <sw-form-item label="公司名称" prop="companyName">
                <el-input
                  v-model="form.companyName"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="开户行" prop="bankName">
                <el-input
                  v-model="form.bankName"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="电话" prop="phoneNumber">
                <el-input
                  v-model="form.phoneNumber"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="纳税人资质" prop="taxpayerType">
                <el-input
                  v-model="form.taxpayerType"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="纳税人识别号" prop="taxpayerId">
                <el-input
                  v-model="form.taxpayerId"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="银行账户" prop="bankAccount">
                <el-input
                  v-model="form.bankAccount"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="开票地址" prop="invoiceAddress">
                <el-input
                  v-model="form.invoiceAddress"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </menu-form-content>
    <div slot="footer" class="page-content-footer">
      <el-button
        class="oper-btn cancel-btn mgr10"
        @click="onCloseClick"
        :loading="loading"
        >{{ $t("common.cancel") }}</el-button
      >
      <el-button
        type="primary"
        class="oper-btn"
        :loading="loading"
        @click="onSubmitClick"
        >{{ $t("common.submit") }}</el-button
      >
    </div>
  </content-detail>
</template>
<script>
import {
  getCustomerDetail,
  addCustomer,
  editCustomer,
} from "@/api/business/customer";
import Page from "@/components/searchTableContent/mixins/page";

let defaultInfo = {
  id: "",
  bussContactInfoList: [], // 联系人列表
  customerCode: "",
  corporation: "",
  customerGroup: "",
  customerName: "",
  salesDepartment: "",
  salesman: "",
  customerSource: "",
  strategicCustomerFlag: "",
  resourceDescription: "",
  teamMembers: "",
  businessDescription: "",
  formerName: "",
  transactionState: "",
  customerLevel: "",
  industry: "",
  channelType: "",
  discountHardware: "",
  discountSoftware: "",
  channelAgreement: "",
  agreementStart: "",
  agreementEnd: "",
  agreementExtendFlag: "",
  certificateNumber: "",
  taskAmount: "",
  contractSigningAmount: "",
  followUp: "",
  registeredDate: "",
  registeredCapital: "",
  registeredIndustry: "",
  enterpriseNature: "",
  legalRepresentative: "",
  officialWebsite: "",
  staffSize: "",
  socialSecurityNumber: "",
  businessScope: "",
  companyEmail: "",
  companyTel: "",
  country: "",
  province: "",
  city: "",
  region: "",
  address: "",
  u9Flag: "",
  u9Time: "",
  u9Code: "",
  u9Error: "",
  fbtFlag: "",
  fbtTime: "",
  fbtError: "",
  fbtCode: "",
  u9Arq: "",
  u9ArqTime: "",
  extend1: "",
  extend2: "",
  extend3: "",
  createBy: "",
  createTime: "",
  updateBy: "",
  updateTime: "",
  name: "",
  position: "",
  mobile: "",
  tel: "",
  wechat: "",
  department: "",
  companyName: "",
  bankName: "",
  phoneNumber: "",
  taxpayerType: "",
  taxpayerId: "",
  bankAccount: "",
  invoiceAddress: "",
};
export default {
  name: "businessCustomerAdd",
  components: {},
  mixins: [Page],
  data() {
    return {
      isEdit: false,
      loading: false,
      dictData: {
        subject_select: [],
        customer_source: [],
        strategic_customer: [],
        deal_status: [],
        customer_level: [],
        channel_type: [],
        extend_agreement_period: [],
      },
      typeList: [
        { label: "基本信息", value: "basicInfo" },
        { label: "客户属性", value: "customerProperty" },
        { label: "渠道管理信息", value: "channelInfo" },
        { label: "工商信息", value: "businessInfo" },
        { label: "联系人", value: "contacts" },
        { label: "地址信息", value: "addressInfo" },
        { label: "开票信息", value: "invoiceInfo" },
      ],
      form: {},
      rules: {
        customerName: [{ required: true, message: "请输入", trigger: "blur" }],
        corporation: [{ required: true, message: "请输入", trigger: "blur" }],
      },
      columns: [
        // {
        //   label: "联系人编号",
        //   prop: "contactCode",
        // },
        // {
        //   label: "客户名称",
        //   prop: "customerName",
        // },
        {
          label: "姓名",
          prop: "name",
        },
        {
          label: "职务",
          prop: "position",
        },
        {
          label: "手机号",
          prop: "mobile",
        },
        {
          label: "电话",
          prop: "tel",
        },
        {
          label: "微信",
          prop: "wechat",
        },
        {
          label: "部门",
          prop: "department",
        },
      ],
      initQuery: false,
    };
  },
  computed: {
    queryData() {
      return this.$route.query;
    },
  },
  created() {
    this.isEdit = !!this.$route.query.id;
    this.initData();
    this.getDictList();
  },

  methods: {
    queryDictList() {
      console.log("重新获取字典");
      this.getDictList();
    },
    initData() {
      this.form = this.cloneDeep(defaultInfo);
      if (this.isEdit) {
        getCustomerDetail({ id: this.queryData.id }).then((res) => {
          this.form = {
            ...this.cloneDeep(defaultInfo),
            ...res.data,
          };
        });
      }
    },
    // 渠道类型->硬件折扣、软件折扣
    onChangeChannelType(val) {
      console.log(val);
      let currentObj = this.dictData.channel_type.find(
        (item) => item.value == val,
      );
      if (currentObj && currentObj.extendData) {
        this.form.discountHardware =
          currentObj.extendData.find((item) => item.name == "硬件折扣")?.val ||
          "";
        this.form.discountSoftware =
          currentObj.extendData.find((item) => item.name == "软件折扣")?.val ||
          "";
      } else {
        this.form.discountHardware = "";
        this.form.discountSoftware = "";
      }
    },
    onCloseClick() {
      this.$refs.formRef?.resetFields();
      this.$router.go(-1);
    },
    onSubmitClick() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          let request = this.isEdit ? editCustomer : addCustomer;
          this.loading = true;
          request(this.form)
            .then((res) => {
              this.successMsg(this.isEdit ? "编辑成功" : "新增成功");
              this.onCloseClick();
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
