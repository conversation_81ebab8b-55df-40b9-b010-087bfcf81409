<template>
  <div class="full-height full-width page-container">
    <smart-search-table-content
      class="full-height full-width"
      ref="searchTableRef"
      :searchForm="searchForm"
      :advanceSearchForm="advanceSearchForm"
      :columns="columns"
      :tableData="tableData"
      :table-loading="loading"
      :tablePage="tablePage"
      @reset="reset"
      @query="queryBase"
      @pagination="pagination"
      :isSelected="true"
      :isAdvance="isAdvance"
      @queryAdvance="queryAdvance"
      @selectionChange="selectionChange"
      :selectable="selectable"
    >
      <template #pageTitle>
        <page-header title="特殊供货申请">
          <!-- <template #titleWarning>{{ $t("pageTitle.apiKey.desc") }}</template> -->
        </page-header>
      </template>
      <template #search>
        <search-item label="单据编号" prop="documentNum">
          <el-input
            v-model="searchForm.documentNum"
            :placeholder="$t('placeholder.place')"
            clearable
          />
        </search-item>
        <search-item label="项目机会跟进编码" prop="projectFollowNum">
          <el-input
            v-model="searchForm.projectFollowNum"
            :placeholder="$t('placeholder.place')"
            clearable
          />
        </search-item>
      </template>
      <template v-slot:actions>
        <batch-action-btns
          :btns="actionBtns"
          :batchBtns="batchBtns"
          :limit="batchLimit"
          :showBatch="selectList.length > 0"
          :getSelectList="() => selectList"
          :isSelectTotal="isSelectTotal"
          :total="tablePage.total"
          @onSelectTotalClick="onSelectTotalClick"
          @clearSelectList="clearSelectList"
        />
      </template>
      <template #operation="{ data }">
        <operatebtns
          :row="data"
          :btns="operateBtns"
          :limit="operbtnsLimit"
        ></operatebtns>
      </template>
    </smart-search-table-content>
  </div>
</template>

<script>
import Page from "@/components/smartSearchTableContent/mixins/page";
import { getSupplyApplyList, deleteSupplyApply } from "@/api/supply/special";
import { getMock } from "@/utils/util";

export default {
  mixins: [Page],
  components: {},
  data() {
    return {
      addDialog: {
        dialogVisible: false,
        rowObj: {},
        isEdit: false,
      },
      detailDialog: {
        dialogVisible: false,
        rowObj: {},
      },
      actionBtns: [
        {
          name: "新建",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "查重",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onCheckClick,
          permi: ["none:none:none"],
        },
        {
          name: "导入",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-shangchuan1-line",
          func: this.onImportClick,
          permi: ["none:none:none"],
        },
        {
          name: "导出",
          id: "exportBtnId",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-daochu",
          func: this.onExportClick,
          permi: ["none:none:none"],
        },
      ],
      batchBtns: [
        {
          name: "分配",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
          rule: () => {
            return true;
          },
        },
        {
          name: "删除",
          class: "danger-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "导出",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "共享",
          class: "oper-text-btn",
          icon: "iconfont icon-shangchuan1-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "批量编辑",
          class: "oper-text-btn",
          icon: "iconfont icon-daochu",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "提交审批",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "下载全部附件",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
          rule: () => {
            return true;
          },
        },
      ],
      columns: [
        {
          label: "单据编号",
          prop: "documentNum",
        },
        {
          label: "项目机会跟进编码",
          prop: "projectFollowNum",
        },
        {
          label: "主体选择名称",
          prop: "subjectSelectName",
        },
        {
          label: "供货类型",
          prop: "supplyType",
        },
        {
          label: "负责人",
          prop: "responsiblePerson",
        },
        {
          label: "所属部门",
          prop: "department",
        },
        {
          label: "年份",
          prop: "year",
        },
        {
          label: "客户名称",
          prop: "customerName",
        },
        {
          label: "客户级别",
          prop: "customerLevel",
        },
        {
          label: "原因说明",
          prop: "reason",
        },
        {
          label: "项目级别",
          prop: "projectLevel",
        },
        {
          label: "实际考核部门",
          prop: "actualAssessDepartment",
        },
        {
          label: "跟进说明",
          prop: "followInstruction",
        },
        {
          label: "license授权描述",
          prop: "licenseAuthDesc",
        },
        {
          label: "合同号",
          prop: "contractNum",
        },
        {
          label: "申请发货日期",
          prop: "requestShipmentDate",
        },
        {
          label: "客户是否逾期",
          prop: "customerOverdue",
        },
        {
          label: "借用凭据",
          prop: "borrowCredentials",
        },
        {
          label: "时长",
          prop: "duration",
        },
        {
          label: "押金缴纳",
          prop: "depositPayment",
        },
        {
          label: "应归还/转销售日期",
          prop: "dueReturnTransferDate",
        },
        {
          label: "部门是否超借用额度",
          prop: "departmentExceedBorrowAmount",
        },
        {
          label: "营销中心是否整体超借用额度",
          prop: "marketCenterExceedBorrowAmount",
        },
        {
          label: "个人借用额度",
          prop: "personalBorrowAmount",
        },
        {
          label: "部门借用额度",
          prop: "departmentBorrowAmount",
        },
        {
          label: "个人当前借用占用费",
          prop: "personalCurrentBorrowFee",
        },
        {
          label: "部门当前借用占用费",
          prop: "departmentCurrentBorrowFee",
        },
        {
          label: "借用占比",
          prop: "borrowPercentage",
        },
        {
          label: "部门是否超提前供货额度",
          prop: "departmentExceedAdvanceSupplyAmount",
        },
        {
          label: "营销中心是否整体超提前供货额度",
          prop: "marketCenterExceedAdvanceSupplyAmount",
        },
        {
          label: "合同预计签回时间",
          prop: "expectContractSignoffDate",
        },
        {
          label: "个人提前供货额度",
          prop: "personalAdvanceSupplyAmount",
        },
        {
          label: "部门提前供货额度",
          prop: "departmentAdvanceSupplyAmount",
        },
        {
          label: "个人当前提前供货占用费",
          prop: "personalCurrentAdvanceSupplyFee",
        },
        {
          label: "部门当前提前供货占用费",
          prop: "departmentCurrentAdvanceSupplyFee",
        },
        {
          label: "提前供货占比",
          prop: "advanceSupplyPercentage",
        },
        // {
        //   label: "创建人",
        //   prop: "createPerson",
        // },
        {
          label: "创建时间",
          prop: "createTime",
        },
        // {
        //   label: "更新人",
        //   prop: "updatePerson",
        // },
        {
          label: "更新时间",
          prop: "updateTime",
        },
        {
          label: "操作",
          prop: "operation",
          render: "operation",
          width: "120px",
          align: "center",
          fixed: "right",
        },
      ],
      operateBtns: [
        // {
        //   name: this.$t("common.detail"),
        //   class: "oper-text-btn",
        //   permi: ["none:none:none"],
        //   func: this.onDetailClick,
        // },
        {
          name: this.$t("common.edit"),
          class: "oper-text-btn",
          permi: ["none:none:none"],
          func: this.onEditClick,
        },
        {
          name: this.$t("common.delete"),
          class: "delete-text-btn",
          permi: ["none:none:none"],
          func: (row) => {
            this.confirmInfoOperFun(
              this.$t("tips.deleteTip"),
              deleteSupplyApply,
              { ids: [row.id] },
              this.$t("msg.delete"),
            );
          },
        },
      ],
      // 查询参数
      searchForm: {
        documentNum: "",
        projectFollowNum: "",
      },
      dictData: {
        subject_select: [],
      },
    };
  },
  created() {
    this.getDictList();
    console.log(
      getMock(
        {
          id: { type: "string", description: "主键" },
          documentNum: { type: "string", description: "单据编号" },
          projectFollowNum: {
            type: "string",
            description: "项目机会跟进编码",
          },
          subjectSelectName: {
            type: "string",
            description: "主体选择名称",
          },
          subjectSelectId: {
            type: "string",
            description: "主体选择id",
          },
          supplyType: { type: "string", description: "供货类型" },
          responsiblePerson: {
            type: "string",
            description: "负责人",
          },
          department: { type: "string", description: "所属部门" },
          year: { type: "string", description: "年份" },
          customerName: { type: "string", description: "客户名称" },
          customerLevel: { type: "string", description: "客户级别" },
          reason: { type: "string", description: "原因说明" },
          projectLevel: { type: "string", description: "项目级别" },
          actualAssessDepartment: {
            type: "string",
            description: "实际考核部门",
          },
          followInstruction: {
            type: "string",
            description: "跟进说明",
          },
          licenseAuthDesc: {
            type: "string",
            description: "license授权描述",
          },
          contractNum: { type: "string", description: "合同号" },
          requestShipmentDate: {
            type: "string",
            description: "申请发货日期",
          },
          customerOverdue: {
            type: "string",
            description: "客户是否逾期，是/否",
          },
          borrowCredentials: {
            type: "string",
            description: "借用凭据",
          },
          duration: { type: "string", description: "时长" },
          depositPayment: { type: "number", description: "押金缴纳" },
          dueReturnTransferDate: {
            type: "string",
            description: "应归还/转销售日期",
          },
          departmentExceedBorrowAmount: {
            type: "number",
            description: "部门是否超借用额度",
          },
          marketCenterExceedBorrowAmount: {
            type: "number",
            description: "营销中心是否整体超借用额度",
          },
          personalBorrowAmount: {
            type: "number",
            description: "个人借用额度",
          },
          departmentBorrowAmount: {
            type: "number",
            description: "部门借用额度",
          },
          personalCurrentBorrowFee: {
            type: "number",
            description: "个人当前借用占用费",
          },
          departmentCurrentBorrowFee: {
            type: "number",
            description: "部门当前借用占用费",
          },
          borrowPercentage: {
            type: "number",
            description: "借用占比",
          },
          departmentExceedAdvanceSupplyAmount: {
            type: "number",
            description: "部门是否超提前供货额度",
          },
          marketCenterExceedAdvanceSupplyAmount: {
            type: "number",
            description: "营销中心是否整体超提前供货额度",
          },
          expectContractSignoffDate: {
            type: "string",
            description: "合同预计签回时间",
          },
          personalAdvanceSupplyAmount: {
            type: "number",
            description: "个人提前供货额度",
          },
          departmentAdvanceSupplyAmount: {
            type: "number",
            description: "部门提前供货额度",
          },
          personalCurrentAdvanceSupplyFee: {
            type: "number",
            description: "个人当前提前供货占用费",
          },
          departmentCurrentAdvanceSupplyFee: {
            type: "number",
            description: "部门当前提前供货占用费",
          },
          advanceSupplyPercentage: {
            type: "number",
            description: "提前供货占比",
          },
          createPerson: { type: "string", description: "创建人" },
          createTime: { type: "string", description: "创建时间" },
          updatePerson: { type: "string", description: "更新人" },
          updateTime: { type: "string", description: "更新时间" },
          specialSupplyProductPOS: {
            type: "array",
            items: {
              type: "object",
              properties: {
                id: { type: "string", description: "主键" },
                specialSupplyApplyId: {
                  type: "string",
                  description: "特殊供货申请表id",
                },
                productManageId: {
                  type: "string",
                  description: "产品管理表id",
                },
                unineCode: { type: "string", description: "U9编码" },
                productName: {
                  type: "string",
                  description: "产品名称",
                },
                productModel: {
                  type: "string",
                  description: "产品型号",
                },
                applyNum: { type: "number", description: "申请数量" },
                shipmentNum: {
                  type: "number",
                  description: "已发货数量",
                },
                transferSalesNum: {
                  type: "number",
                  description: "转销售数量",
                },
                inventoryNum: {
                  type: "number",
                  description: "结存数量",
                },
                usedQuota: {
                  type: "number",
                  description: "占用额度",
                },
                createPerson: {
                  type: "string",
                  description: "创建人",
                },
                createTime: {
                  type: "string",
                  description: "创建时间",
                },
                updatePerson: { type: "null", description: "更新人" },
                updateTime: { type: "null", description: "更新时间" },
              },
              required: [
                "id",
                "specialSupplyApplyId",
                "productManageId",
                "unineCode",
                "productName",
                "productModel",
                "applyNum",
                "shipmentNum",
                "transferSalesNum",
                "inventoryNum",
                "usedQuota",
                "createPerson",
                "createTime",
                "updatePerson",
                "updateTime",
              ],
            },
            description: "产品信息列表",
          },
          attachmentInfo: {
            type: "array",
            items: {
              type: "object",
              properties: {
                specialSupplyId: {
                  type: "string",
                  description: "特殊供货申请表id",
                },
                id: { type: "string", description: "附件id" },
                fileName: { type: "string", description: "原文件名" },
                fileHash: { type: "string", description: "文件hash" },
                name: {
                  type: "string",
                  description: "上传后附件显示名",
                },
                info: { type: "string", description: "附件简介" },
                fileLevel: {
                  type: "string",
                  description: "附件分类",
                },
                pid: { type: "number", description: "所属物料id" },
                fileSize: { type: "number", description: "附件大小" },
                fileType: {
                  type: "string",
                  description: "附件类型&文件后缀名",
                },
                creatorId: {
                  type: "number",
                  description: "创建者id",
                },
                lastModifierId: {
                  type: "number",
                  description: "最后一个修改者的id",
                },
                createTime: {
                  type: "string",
                  description: "创建时间",
                },
                path: { type: "string", description: "文件路径" },
              },
              required: [],
            },
          },
        },
        {
          documentNum: {
            type: "string",
            description: "单据编号，由后端接口根据规则生成，接口暂无",
          },
          projectFollowNum: {
            type: "string",
            description:
              "项目名称，项目机会跟进编码，调用项目机会跟进接口/crm/follow/projectopportunity/queryToPage，当客户名称不为空时，将客户名称作为入参；选中数据后将项目机会跟进中的项目编号赋值给该字段",
          },
          subjectSelectName: {
            type: "string",
            description: "主体选择名称，下拉框",
          },
          subjectSelectId: {
            type: "string",
            description: "主体选择id，下拉带出",
          },
          supplyType: { type: "string", description: "供货类型，下拉框" },
          responsiblePerson: {
            type: "string",
            description:
              "负责人，先显示当前登录人员名称，选中项目后显示项目的销售负责人",
          },
          department: {
            type: "string",
            description: "所属部门，显示项目负责人的部门",
          },
          year: {
            type: "string",
            description: "年份，默认当前年，下拉框，可选",
          },
          customerName: {
            type: "string",
            description:
              "客户名称，由项目名称列表中的数据反写；可修改，调用客户档案接口/crm/customer/page：选中数据后将客户名称赋值给该字段\n",
          },
          customerLevel: {
            type: "string",
            description:
              "客户级别，由项目机会跟进接口或客户档案接口中的数据反写",
          },
          reason: { type: "string", description: "原因说明" },
          projectLevel: { type: "string", description: "项目级别" },
          actualAssessDepartment: {
            type: "string",
            description: "实际考核部门",
          },
          followInstruction: { type: "string", description: "跟进说明" },
          licenseAuthDesc: { type: "string", description: "license授权描述" },
          contractNum: { type: "string", description: "合同号" },
          requestShipmentDate: { type: "string", description: "申请发货日期" },
          customerOverdue: {
            type: "string",
            description: "客户是否逾期，是/否",
          },
          borrowCredentials: { type: "string", description: "借用凭据" },
          duration: { type: "string", description: "时长" },
          depositPayment: { type: "number", description: "押金缴纳" },
          dueReturnTransferDate: {
            type: "string",
            description: "应归还/转销售日期",
          },
          departmentExceedBorrowAmount: {
            type: "string",
            description:
              "部门是否超借用额度，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算",
          },
          marketCenterExceedBorrowAmount: {
            type: "string",
            description:
              "营销中心是否整体超借用额度，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算",
          },
          personalBorrowAmount: {
            type: "string",
            description:
              "个人借用额度，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算",
          },
          departmentBorrowAmount: {
            type: "string",
            description:
              "部门借用额度，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算",
          },
          personalCurrentBorrowFee: {
            type: "string",
            description:
              "个人当前借用占用费，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算",
          },
          departmentCurrentBorrowFee: {
            type: "string",
            description:
              "部门当前借用占用费，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算",
          },
          borrowPercentage: {
            type: "string",
            description:
              "借用占比，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算",
          },
          departmentExceedAdvanceSupplyAmount: {
            type: "string",
            description:
              "部门是否超提前供货额度，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算",
          },
          marketCenterExceedAdvanceSupplyAmount: {
            type: "string",
            description:
              "营销中心是否整体超提前供货额度，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算",
          },
          expectContractSignoffDate: {
            type: "string",
            description: "合同预计签回时间",
          },
          personalAdvanceSupplyAmount: {
            type: "string",
            description:
              "个人提前供货额度，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算",
          },
          departmentAdvanceSupplyAmount: {
            type: "string",
            description:
              "部门提前供货额度，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算",
          },
          personalCurrentAdvanceSupplyFee: {
            type: "string",
            description:
              "个人当前提前供货占用费，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算",
          },
          departmentCurrentAdvanceSupplyFee: {
            type: "string",
            description:
              "部门当前提前供货占用费，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算",
          },
          advanceSupplyPercentage: {
            type: "string",
            description:
              "提前供货占比，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算",
          },
          addSpecialSupplyProductDTOS: {
            type: "array",
            items: {
              type: "object",
              properties: {
                productManageId: { type: "string", description: "产品管理id" },
                unineCode: { type: "string", description: "U9编码" },
                productName: { type: "string", description: "产品名称" },
                productModel: { type: "string", description: "产品型号" },
                applyNum: { type: "string", description: "申请数量" },
              },
              required: ["applyNum", "unineCode", "productManageId"],
            },
            description: "产品管理信息",
          },
          attachmentIds: {
            type: "array",
            items: { type: "string" },
            description: "附件id",
          },
        },
      ),
    );
  },
  methods: {
    request: getSupplyApplyList,

    onAddClick() {
      this.$router.push({
        name: "SupplySpecialAdd",
        query: {},
      });
    },
    onCheckClick() {},
    onImportClick() {},
    onExportClick() {},
    onDetailClick(row) {},
    onEditClick(row) {
      this.$router.push({
        name: "SupplySpecialAdd",
        query: {
          id: row.id,
        },
      });
    },
  },
  beforeDestroy() {},
};
</script>
