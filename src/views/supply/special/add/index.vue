<template>
  <content-detail :content="isEdit ? `编辑` : '新增'">
    <menu-form-content :typeList="typeList">
      <el-form
        slot="rightContent"
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="auto"
        size="small"
        label-position="right"
      >
        <div class="right-content-item" id="Base">
          <base-title title="基本信息"></base-title>
          <el-row :gutter="16">
            <el-col :span="12">
              <sw-form-item label="单据编号" prop="documentNum">
                <el-input
                  v-model="form.documentNum"
                  placeholder="请输入"
                  :disabled="isEdit"
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <!-- 当主体为包含科友时，项目名称非必填 -->
              <sw-form-item
                label="项目机会跟进（项目名称）"
                prop="projectFollowNum"
                :class="
                  form.subjectSelectName.includes('科友') ? '' : 'is-required'
                "
              >
                <add-btn-input
                  v-model="form.projectFollowNum"
                  clearable
                  @open="openProjectDialog"
                  @clear="clearProject"
                ></add-btn-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="主体选择" prop="subjectSelectName">
                <dict-select
                  v-model="form.subjectSelectName"
                  :options="dictData.subject_select"
                  placeholder="请选择"
                  dictName="主体名称"
                  dictCode="subject_select"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="供货类型" prop="supplyType">
                <dict-select
                  v-model="form.supplyType"
                  :options="dictData.supply_type"
                  placeholder="请选择"
                  dictName="供货类型"
                  dictCode="supply_type"
                  clearable
                  @queryDictList="queryDictList"
                  @change="changeSupplyType"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="负责人" prop="responsiblePerson">
                <el-input
                  v-model="form.responsiblePerson"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="所属部门" prop="department">
                <el-input
                  v-model="form.department"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="年份" prop="year">
                <dict-select
                  v-model="form.year"
                  :options="dictData.year"
                  placeholder="请选择"
                  dictName="年份"
                  dictCode="year"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="客户名称" prop="customerName">
                <add-btn-input
                  v-model="form.customerName"
                  clearable
                  @open="openCustomerDialog"
                  @clear="clearCustomer"
                ></add-btn-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="客户级别" prop="customerLevel">
                <el-input
                  v-model="form.customerLevel"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="24">
              <sw-form-item label="原因说明" prop="reason">
                <el-input
                  type="textarea"
                  maxlength="200"
                  show-word-limit
                  rows="4"
                  v-model="form.reason"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="项目级别" prop="projectLevel">
                <el-input
                  v-model="form.projectLevel"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="实际考核部门" prop="actualAssessDepartment">
                <el-input
                  v-model="form.actualAssessDepartment"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="24">
              <sw-form-item label="跟进说明" prop="followInstruction">
                <el-input
                  type="textarea"
                  maxlength="200"
                  show-word-limit
                  rows="4"
                  v-model="form.followInstruction"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="24">
              <sw-form-item label="license授权描述" prop="licenseAuthDesc">
                <el-input
                  type="textarea"
                  maxlength="200"
                  show-word-limit
                  rows="4"
                  v-model="form.licenseAuthDesc"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="申请发货日期" prop="requestShipmentDate">
                <el-date-picker
                  v-model="form.requestShipmentDate"
                  type="date"
                  placeholder="请选择日期"
                  value-format="yyyy-MM-dd"
                  format="yyyy-MM-dd"
                  clearable
                >
                </el-date-picker>
              </sw-form-item>
            </el-col>
            <el-col :span="12" v-if="form.supplyType == '返回升级'">
              <sw-form-item label="合同号" prop="contractNum">
                <el-input
                  v-model="form.contractNum"
                  placeholder=""
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="客户是否逾期" prop="customerOverdue">
                <dict-select
                  v-model="form.customerOverdue"
                  :options="dictData.customer_overdue"
                  placeholder="请选择"
                  dictName="客户是否逾期"
                  dictCode="customer_overdue"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
        <div
          class="right-content-item"
          id="Borrow"
          v-if="form.supplyType == '借用' || form.supplyType == '提前供货'"
        >
          <base-title title="借用信息"></base-title>
          <!-- 供货类型=借用 -->
          <el-row :gutter="16" v-if="form.supplyType == '借用'">
            <el-col :span="12">
              <sw-form-item label="借用凭据" prop="borrowCredentials">
                <dict-select
                  v-model="form.borrowCredentials"
                  :options="dictData.borrow_credentials"
                  placeholder="请选择"
                  dictName="借用凭据"
                  dictCode="borrow_credentials"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="时长" prop="duration">
                <el-input
                  v-model="form.duration"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="押金缴纳" prop="depositPayment">
                <el-input
                  v-model="form.depositPayment"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="应归还/转销售日期"
                prop="dueReturnTransferDate"
              >
                <el-date-picker
                  v-model="form.dueReturnTransferDate"
                  type="date"
                  placeholder="请选择日期"
                  value-format="yyyy-MM-dd"
                  format="yyyy-MM-dd"
                  clearable
                >
                </el-date-picker>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="部门是否超借用额度"
                prop="departmentExceedBorrowAmount"
              >
                <dict-select
                  v-model="form.departmentExceedBorrowAmount"
                  :options="dictData.department_exceed_borrow_limit"
                  placeholder=""
                  dictName="部门是否超借用额度"
                  dictCode="department_exceed_borrow_limit"
                  disabled
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="营销中心是否整体超借用额度"
                prop="marketCenterExceedBorrowAmount"
              >
                <dict-select
                  v-model="form.marketCenterExceedBorrowAmount"
                  :options="dictData.market_center_exceed_borrow_limit"
                  placeholder=""
                  dictName="营销中心是否整体超借用额度"
                  dictCode="market_center_exceed_borrow_limit"
                  disabled
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="个人借用额度" prop="personalBorrowAmount">
                <el-input
                  v-model="form.personalBorrowAmount"
                  placeholder=""
                  disabled
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="部门借用额度" prop="departmentBorrowAmount">
                <el-input
                  v-model="form.departmentBorrowAmount"
                  placeholder=""
                  disabled
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="个人当前借用占用费"
                prop="personalCurrentBorrowFee"
              >
                <el-input
                  v-model="form.personalCurrentBorrowFee"
                  placeholder=""
                  disabled
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="部门当前借用占用费"
                prop="departmentCurrentBorrowFee"
              >
                <el-input
                  v-model="form.departmentCurrentBorrowFee"
                  placeholder=""
                  disabled
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="借用占比" prop="borrowPercentage">
                <el-input
                  v-model="form.borrowPercentage"
                  placeholder=""
                  disabled
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
          <!-- 供货类型=提前供货 -->
          <el-row :gutter="16" v-if="form.supplyType == '提前供货'">
            <el-col :span="12">
              <sw-form-item
                label="部门是否超提前供货额度"
                prop="departmentExceedAdvanceSupplyAmount"
              >
                <dict-select
                  v-model="form.departmentExceedAdvanceSupplyAmount"
                  :options="dictData.department_exceed_advance_supply_limit"
                  placeholder=""
                  dictName="部门是否超提前供货额度"
                  dictCode="department_exceed_advance_supply_limit"
                  disabled
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="营销中心是否整体超提前供货额度"
                prop="marketCenterExceedAdvanceSupplyAmount"
              >
                <dict-select
                  v-model="form.marketCenterExceedAdvanceSupplyAmount"
                  :options="dictData.market_center_exceed_advance_supply_limit"
                  placeholder=""
                  dictName="营销中心是否整体超提前供货额度"
                  dictCode="market_center_exceed_advance_supply_limit"
                  disabled
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
        <div
          class="right-content-item"
          id="Supply"
          v-if="form.supplyType == '提前供货' || form.supplyType == '补发/赠送'"
        >
          <base-title title="供货信息"></base-title>
          <!-- 供货类型=提前供货 -->
          <el-row :gutter="16" v-if="form.supplyType == '提前供货'">
            <el-col :span="12">
              <sw-form-item
                label="合同预计签回时间"
                prop="expectContractSignoffDate"
              >
                <el-date-picker
                  v-model="form.expectContractSignoffDate"
                  type="date"
                  placeholder=""
                  value-format="yyyy-MM-dd"
                  format="yyyy-MM-dd"
                  disabled
                >
                </el-date-picker>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="个人提前供货额度"
                prop="personalAdvanceSupplyAmount"
              >
                <el-input
                  v-model="form.personalAdvanceSupplyAmount"
                  placeholder=""
                  disabled
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="部门提前供货额度"
                prop="departmentAdvanceSupplyAmount"
              >
                <el-input
                  v-model="form.departmentAdvanceSupplyAmount"
                  placeholder=""
                  disabled
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="个人当前提前供货占用费"
                prop="personalCurrentAdvanceSupplyFee"
              >
                <el-input
                  v-model="form.personalCurrentAdvanceSupplyFee"
                  placeholder=""
                  disabled
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="部门当前提前供货占用费"
                prop="departmentCurrentAdvanceSupplyFee"
              >
                <el-input
                  v-model="form.departmentCurrentAdvanceSupplyFee"
                  placeholder=""
                  disabled
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="提前供货占比" prop="advanceSupplyPercentage">
                <el-input
                  v-model="form.advanceSupplyPercentage"
                  placeholder=""
                  disabled
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
          <!-- 供货类型=补发/赠送 -->
          <el-row :gutter="16" v-if="form.supplyType == '补发/赠送'">
            <el-col :span="12">
              <sw-form-item label="合同号" prop="contractNum">
                <el-input
                  v-model="form.contractNum"
                  placeholder=""
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="Attach">
          <base-title title="附件"></base-title>
          <el-row :gutter="16">
            <el-col :span="23" :offset="1">
              <AttachCom
                :data="form.attachmentIds"
                ref="AttachCom"
                :attachs="form.attachmentInfo"
              ></AttachCom>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="Product">
          <base-title title="产品"></base-title>
          <el-row :gutter="16">
            <el-col :span="23" :offset="1">
              <ProductCom
                :data="form.specialSupplyProductPOS"
                ref="ProductCom"
                @getSupplyAmountInfo="getSupplyAmountInfo"
              ></ProductCom>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </menu-form-content>
    <div slot="footer" class="page-content-footer">
      <el-button
        class="oper-btn cancel-btn mgr10"
        @click="onCloseClick"
        :loading="loading"
        >{{ $t("common.cancel") }}</el-button
      >
      <el-button
        type="primary"
        class="oper-btn"
        :loading="loading"
        @click="onSubmitClick"
        >{{ $t("common.submit") }}</el-button
      >
    </div>

    <selectProject
      :dialogVisible="projectDialog.dialogVisible"
      :rowObj="projectDialog.rowObj"
      :selectedList="[]"
      @eventClose="projectDialog.dialogVisible = false"
      @eventSucc="eventSuccAddProject"
    ></selectProject>
    <selectCustomer
      :dialogVisible="customerDialog.dialogVisible"
      :rowObj="customerDialog.rowObj"
      :selectedList="[]"
      @eventClose="customerDialog.dialogVisible = false"
      @eventSucc="eventSuccAddCustomer"
    ></selectCustomer>
  </content-detail>
</template>
<script>
import {
  getSupplyApplyDetail,
  addSupplyApply,
  editSupplyApply,
  getSupplyAmountInfo,
} from "@/api/supply/special";
import ProductCom from "./components/ProductCom/index.vue";
import AttachCom from "./components/AttachCom/index.vue";
import selectProject from "./components/selectProject.vue";
import selectCustomer from "@/views/components/selectCustomer/index.vue";

let defaultInfo = {
  documentNum: "",
  projectFollowNum: "",
  subjectSelectName: "",
  subjectSelectId: "",
  supplyType: "",
  responsiblePerson: "",
  department: "",
  year: "",
  customerName: "",
  customerLevel: "",
  reason: "",
  projectLevel: "",
  actualAssessDepartment: "",
  followInstruction: "",
  licenseAuthDesc: "",
  contractNum: "",
  requestShipmentDate: "",
  customerOverdue: "",
  borrowCredentials: "",
  duration: "",
  depositPayment: "",
  dueReturnTransferDate: "",
  departmentExceedBorrowAmount: "",
  marketCenterExceedBorrowAmount: "",
  personalBorrowAmount: "",
  departmentBorrowAmount: "",
  personalCurrentBorrowFee: "",
  departmentCurrentBorrowFee: "",
  borrowPercentage: "",
  departmentExceedAdvanceSupplyAmount: "",
  marketCenterExceedAdvanceSupplyAmount: "",
  expectContractSignoffDate: "",
  personalAdvanceSupplyAmount: "",
  departmentAdvanceSupplyAmount: "",
  personalCurrentAdvanceSupplyFee: "",
  departmentCurrentAdvanceSupplyFee: "",
  advanceSupplyPercentage: "",
  addSpecialSupplyProductDTOS: [],
  attachmentIds: [],
  attachmentInfo: [],
};
export default {
  name: "specialSupplyAdd",
  components: { ProductCom, AttachCom, selectProject, selectCustomer },
  data() {
    return {
      projectDialog: {
        dialogVisible: false,
        rowObj: {},
      },
      customerDialog: {
        dialogVisible: false,
        rowObj: {},
      },
      isEdit: false,
      loading: false,
      dictData: {
        year: [],
        subject_select: [],
        supply_type: [],
        customer_overdue: [],
        borrow_credentials: [],
        department_exceed_borrow_limit: [],
        market_center_exceed_borrow_limit: [],
        department_exceed_advance_supply_limit: [],
        market_center_exceed_advance_supply_limit: [],
      },
      typeList: [
        { label: "基本信息", value: "Base" },
        {
          label: "借用信息",
          value: "Borrow",
          hidden: () => {
            return !(
              this.form.supplyType == "借用" ||
              this.form.supplyType == "提前供货"
            );
          },
        },
        {
          label: "供货信息",
          value: "Supply",
          hidden: () => {
            return !(
              this.form.supplyType == "提前供货" ||
              this.form.supplyType == "补发/赠送"
            );
          },
        },
        { label: "附件", value: "Attach" },
        { label: "产品管理", value: "Product" },
      ],
      form: {},
      rules: {
        documentNum: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        subjectSelectName: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        supplyType: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        reason: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
      },
      refList: [
        { refName: "ProductCom", labelName: "addSpecialSupplyProductDTOS" },
        { refName: "AttachCom", labelName: "attachmentIds" },
      ],
    };
  },
  computed: {
    queryData() {
      return this.$route.query;
    },
  },
  created() {
    this.isEdit = !!this.$route.query.id;
    this.initData();
    this.getDictList();
  },

  methods: {
    queryDictList() {
      console.log("重新获取字典");
      this.getDictList();
    },
    initData() {
      this.form = this.cloneDeep(defaultInfo);
      if (this.isEdit) {
        getSupplyApplyDetail({ id: this.queryData.id }).then((res) => {
          this.form = {
            ...this.cloneDeep(defaultInfo),
            ...res.data,
            addSpecialSupplyProductDTOS:
              res.data.addSpecialSupplyProductDTOS || [],
            attachmentIds: res.data.attachmentIds || [],
            attachmentInfo: res.data.attachmentInfo || [],
          };
        });
      } else {
        // 年份默认今年
        const today = new Date();
        const year = today.getFullYear();
        this.form.year = year;
      }
    },
    // 选择项目机会跟进（项目名称） 如果选择了客户,则查询项目需传入客户名称
    openProjectDialog() {
      this.projectDialog.dialogVisible = true;
      this.projectDialog.rowObj = { customerName: this.form.customerName };
    },
    eventSuccAddProject(row) {
      this.form.projectFollowNum = row.projectNum;
      // 获取客户信息
      this.form.customerName = row.customerName;
      this.form.customerLevel = row.customerLevel;
      // 负责人
      this.form.responsiblePerson = row.salesPerson;
      this.form.department = row.salesPersonDept;
    },
    // 清空项目编号，但不要清空客户信息
    clearProject() {
      this.form.projectFollowNum = "";
    },
    // 选择客户
    openCustomerDialog() {
      this.customerDialog.dialogVisible = true;
      this.customerDialog.rowObj = {
        customerName: this.form.customerName,
      };
    },
    eventSuccAddCustomer(row) {
      this.form.customerName = row.customerName;
      this.form.customerLevel = row.customerLevel;
    },
    clearCustomer() {
      this.form.customerName = "";
    },
    changeSupplyType() {
      // 合同号
      this.form.contractNum = "";
      this.getSupplyAmountInfo();
    },
    // 计算额度
    getSupplyAmountInfo() {
      this.$nextTick(() => {
        let productList = this.$refs.ProductCom.getParams();
        if (
          productList.length > 0 &&
          (this.form.supplyType == "借用" || this.form.supplyType == "提前供货")
        ) {
          getSupplyAmountInfo({
            supplyType: this.form.supplyType,
            applyNumDTOS: productList.map((item) => {
              return {
                unineCode: item.unineCode,
                applyNum: !isNaN(Number(item.applyNum))
                  ? Number(item.applyNum)
                  : 0,
              };
            }),
          }).then((res) => {
            this.form.departmentExceedBorrowAmount =
              res.data.departmentExceedBorrowAmount || "";
            this.form.marketCenterExceedBorrowAmount =
              res.data.marketCenterExceedBorrowAmount || "";
            this.form.personalCurrentBorrowAvailableAmount =
              res.data.personalCurrentBorrowAvailableAmount || "";
            this.form.departmentCurrentBorrowAvailableAmount =
              res.data.departmentCurrentBorrowAvailableAmount || "";
            this.form.personalCurrentBorrowFee =
              res.data.personalCurrentBorrowFee || "";
            this.form.departmentCurrentBorrowFee =
              res.data.departmentCurrentBorrowFee || "";
            this.form.borrowPercentage = res.data.borrowPercentage || "";
            this.form.departmentExceedAdvanceSupplyAmount =
              res.data.departmentExceedAdvanceSupplyAmount || "";
            this.form.marketCenterExceedAdvanceSupplyAmount =
              res.data.marketCenterExceedAdvanceSupplyAmount || "";
            this.form.personalCurrentAdvanceSupplyAvailableAmount =
              res.data.personalCurrentAdvanceSupplyAvailableAmount || "";
            this.form.departmentCurrentAdvanceSupplyAvailableAmount =
              res.data.departmentCurrentAdvanceSupplyAvailableAmount || "";
            this.form.personalCurrentAdvanceSupplyFee =
              res.data.personalCurrentAdvanceSupplyFee || "";
            this.form.departmentCurrentAdvanceSupplyFee =
              res.data.departmentCurrentAdvanceSupplyFee || "";
            this.form.advanceSupplyPercentage =
              res.data.advanceSupplyPercentage || "";
          });
        } else {
          // 清空
          this.form.departmentExceedBorrowAmount = "";
          this.form.marketCenterExceedBorrowAmount = "";
          this.form.personalCurrentBorrowAvailableAmount = "";
          this.form.departmentCurrentBorrowAvailableAmount = "";
          this.form.personalCurrentBorrowFee = "";
          this.form.departmentCurrentBorrowFee = "";
          this.form.borrowPercentage = "";
          this.form.departmentExceedAdvanceSupplyAmount = "";
          this.form.marketCenterExceedAdvanceSupplyAmount = "";
          this.form.personalCurrentAdvanceSupplyAvailableAmount = "";
          this.form.departmentCurrentAdvanceSupplyAvailableAmount = "";
          this.form.personalCurrentAdvanceSupplyFee = "";
          this.form.departmentCurrentAdvanceSupplyFee = "";
          this.form.advanceSupplyPercentage = "";
        }
      });
    },
    onCloseClick() {
      this.$refs.formRef?.resetFields();
      this.$router.go(-1);
    },
    onSubmitClick() {
      const mainFormValidate = new Promise((resolve) => {
        this.$refs.formRef.validate((valid) => {
          resolve(valid);
        });
      });
      const componentValidates = this.refList.map((item) => {
        const component = this.$refs[item.refName];
        if (!component || !component.validForm) {
          return Promise.resolve(true);
        }
        return new Promise((resolve) => {
          const result = component.validForm();
          if (typeof result === "boolean") {
            resolve(result);
          } else if (result instanceof Promise) {
            resolve(result);
          } else {
            resolve(true);
          }
        });
      });
      Promise.all([mainFormValidate, ...componentValidates]).then((results) => {
        const isAllValid = results.every((result) => result === true);
        if (isAllValid) {
          this.handleAsyncFormSubmission();
        }
      });
    },
    async handleAsyncFormSubmission() {
      try {
        let params = this.cloneDeep(this.form);
        for (const item of this.refList) {
          const component = this.$refs[item.refName];
          if (component && component.getParams) {
            try {
              const result = await Promise.resolve(component.getParams());
              params[item.labelName] = result;
            } catch (paramError) {
              // 如果某个组件的getParams抛出错误，则停止执行
              return;
            }
          }
        }
        this.loading = true;
        if (this.isEdit) {
          const res = await editSupplyApply({
            updateSpecialSupplyDTOS: [params],
          });
        } else {
          const res = await addSupplyApply(params);
        }
        this.successMsg(this.isEdit ? "编辑成功" : "新增成功");
        this.onCloseClick();
      } catch (error) {
        console.error("提交失败:", error);
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>
<style lang="scss" scoped></style>
