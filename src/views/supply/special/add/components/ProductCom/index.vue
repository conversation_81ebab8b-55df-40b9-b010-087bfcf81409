<template>
  <div>
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="auto"
      size="small"
      label-position="right"
    >
      <search-table-content
        class="full-width"
        ref="searchTableRef"
        :columns="columns"
        :tableData="form.tableData"
        :table-loading="loading"
        :tablePage="tablePage"
        @reset="reset"
        @query="query"
        @pagination="pagination"
        :summary-method="getSummaries"
        show-summary
        :isFooter="false"
        :inner="true"
      >
        <template v-slot:actions>
          <action-btns :btns="actionBtns" />
        </template>
        <template #index="{ data, index }">
          {{ index + 1 }}
        </template>

        <template #applyNum="{ data, index }">
          <sw-form-item
            label=""
            :prop="`tableData.${index}.applyNum`"
            label-width="0"
            style="margin-top: 16px"
            :rules="rules.applyNum || {}"
          >
            <el-input
              v-model="data.applyNum"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>
        </template>
        <template #operation="{ data }">
          <operatebtns
            :row="data"
            :btns="operateBtns"
            :limit="operbtnsLimit"
          ></operatebtns>
        </template>
      </search-table-content>
    </el-form>
    <batchAddProduct
      :dialogVisible="batchAddDialog.dialogVisible"
      :rowObj="batchAddDialog.rowObj"
      :selectedList="(() => this.form.tableData)()"
      @eventClose="batchAddDialog.dialogVisible = false"
      @eventSucc="eventSuccAdd"
    ></batchAddProduct>
  </div>
</template>
<script>
import Page from "@/components/searchTableContent/mixins/page";
import { getUuid } from "@/utils/util";
import batchAddProduct from "@/views/components/batchAddProduct/index";
import { getProductDetailByU9 } from "@/api/infoSearch/product";
let defaultData = {
  tableData: [],
};
let itemOption = {
  unineCode: "",
  productName: "",
  productModel: "",
  applyNum: "1",
  shipmentNum: "",
  transferSalesNum: "",
  inventoryNum: "",
  usedQuota: "",
};

export default {
  mixins: [Page],
  components: {
    batchAddProduct,
  },
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      initQuery: false,
      loading: false,
      form: {
        tableData: [],
      },
      dictData: {},
      rules: {
        applyNum: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
      },
      actionBtns: [
        {
          name: "新增",
          class: "action-btn blue-color-btn",
          //   icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "清空",
          class: "action-btn white-color-btn",
          //   icon: "iconfont icon-tianjia-line",
          func: this.resetField,
          permi: ["none:none:none"],
        },
      ],
      columns: [
        {
          label: "序号",
          prop: "index",
          render: "index",
          width: "60px",
          align: "center",
          fixed: "left",
        },
        {
          label: "U9编码",
          prop: "unineCode",
          minWidth: "120px",
          fixed: "left",
        },
        {
          label: "产品名称",
          prop: "productName",
          minWidth: "120px",
          fixed: "left",
        },
        {
          label: "产品型号",
          prop: "productModel",
          minWidth: "120px",
        },
        {
          label: "申请数量",
          prop: "applyNum",
          render: "applyNum",
          minWidth: "120px",
        },
        {
          label: "已发货数量",
          prop: "shipmentNum",
          minWidth: "120px",
        },
        {
          label: "转销售数量",
          prop: "transferSalesNum",
          minWidth: "120px",
        },
        {
          label: "结存数量",
          prop: "inventoryNum",
          minWidth: "120px",
        },
        {
          label: "占用额度",
          prop: "usedQuota",
          minWidth: "120px",
        },
        {
          label: "操作",
          prop: "operation",
          render: "operation",
          width: "120px",
          align: "center",
          fixed: "right",
        },
      ],
      operateBtns: [
        // {
        //   name: "复制",
        //   class: "oper-text-btn",
        //   permi: ["none:none:none"],
        //   func: this.onCopyClick,
        // },
        {
          name: "移除",
          class: "delete-text-btn",
          permi: ["none:none:none"],
          func: this.onDeleteClick,
        },
      ],
      batchAddDialog: {
        dialogVisible: false,
        rowObj: {},
      },
      addDialog: {
        dialogVisible: false,
        rowObj: {},
      },
    };
  },
  computed: {
    totalPrice() {
      if (
        !this.form ||
        !this.form.tableData ||
        !Array.isArray(this.form.tableData)
      ) {
        return 0;
      }
      return this.form.tableData.reduce((sum, item) => {
        const price = parseFloat(item.totalAmountPrice) || 0;
        return sum + price;
      }, 0);
    },
  },
  beforeMount() {},
  watch: {
    data: {
      handler(newVal) {
        this.form.tableData = newVal || [];
      },
      immediate: true,
      deep: true,
    },
    "form.tableData": {
      handler(newVal) {
        // 需要获取父组件供货/借用信息
        this.$emit("getSupplyAmountInfo");
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    // 合计
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];

      columns.forEach((column, index) => {
        if (index === 0) {
          // 第一列显示
          sums[index] = "合计";
          return;
        }
        // 需要合计的字段
        const sumFields = [
          "applyNum",
          "shipmentNum",
          "transferSalesNum",
          "inventoryNum",
          "usedQuota",
        ];
        if (sumFields.includes(column.property)) {
          const values = data.map((item) => Number(item[column.property]));
          const sum = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums[index] = sum;
        } else {
          sums[index] = "";
        }
      });

      return sums;
    },
    onAddClick() {
      this.batchAddDialog.dialogVisible = true;
      this.batchAddDialog.rowObj = {};
    },
    onCopyClick(row) {
      this.form.tableData.push({
        ...this.cloneDeep(row),
      });
    },
    onDeleteClick(row) {
      this.form.tableData = this.form.tableData.filter(
        (item) => item.id !== row.id,
      );
    },
    async eventSuccAdd(list) {
      for (let i = 0; i < list.length; i++) {
        try {
          const row = list[i];
          const res = await getProductDetailByU9({ unineCode: row.unineCode });
          this.form.tableData.push({
            ...this.cloneDeep(itemOption),
            ...this.cloneDeep(res.data || {}),
          });
        } catch (error) {}
      }
    },
    changePrice(field, val, data) {
      const toNumber = (value) => {
        const num = parseFloat(value);
        return isNaN(num) ? 0 : num;
      };
      const formatDecimal = (value) => {
        return parseFloat(value.toFixed(6));
      };
      data.taxPrice = toNumber(data.taxPrice);
      data.productNum = toNumber(data.productNum);
      data.unitTaxPrice = toNumber(data.unitTaxPrice);
      data.noUnitTaxPrice = toNumber(data.noUnitTaxPrice);
      data.totalAmountPrice = toNumber(data.totalAmountPrice);
      data.productTax = toNumber(data.productTax);

      switch (field) {
        // 改变税率
        case "productTax":
          data.noUnitTaxPrice = formatDecimal(
            data.unitTaxPrice / (1 + data.productTax * 0.01),
          );
          data.totalAmountPrice = formatDecimal(
            data.unitTaxPrice * data.productNum,
          );
          data.noTotalAmountPrice = formatDecimal(
            data.noUnitTaxPrice * data.productNum,
          );
          data.taxPrice = formatDecimal(
            data.totalAmountPrice - data.noTotalAmountPrice,
          );

          break;
        // 改变数量
        case "productNum":
          data.totalAmountPrice = formatDecimal(
            data.unitTaxPrice * data.productNum,
          );
          data.noTotalAmountPrice = formatDecimal(
            data.noUnitTaxPrice * data.productNum,
          );
          data.taxPrice = formatDecimal(
            data.totalAmountPrice - data.noTotalAmountPrice,
          );
          break;
        // 改变含税单价
        case "unitTaxPrice":
          data.totalAmountPrice = formatDecimal(
            data.unitTaxPrice * data.productNum,
          );
          data.noUnitTaxPrice = formatDecimal(
            data.unitTaxPrice / (1 + data.productTax * 0.01),
          );
          data.noTotalAmountPrice = formatDecimal(
            data.noUnitTaxPrice * data.productNum,
          );
          data.taxPrice = formatDecimal(
            data.totalAmountPrice - data.noTotalAmountPrice,
          );
          break;
        // 改变不含税单价
        case "noUnitTaxPrice":
          data.noTotalAmountPrice = formatDecimal(
            data.noUnitTaxPrice * data.productNum,
          );
          data.unitTaxPrice = formatDecimal(
            data.noUnitTaxPrice * (1 + data.productTax * 0.01),
          );
          data.totalAmountPrice = formatDecimal(
            data.unitTaxPrice * data.productNum,
          );
          data.taxPrice = formatDecimal(
            data.totalAmountPrice - data.noTotalAmountPrice,
          );
          break;
        // 改变价税合计
        case "totalAmountPrice":
          if (data.productNum !== 0) {
            data.unitTaxPrice = formatDecimal(
              data.totalAmountPrice / data.productNum,
            );
            data.noUnitTaxPrice = formatDecimal(
              data.unitTaxPrice / (1 + data.productTax * 0.01),
            );
            data.noTotalAmountPrice = formatDecimal(
              data.noUnitTaxPrice * data.productNum,
            );
            data.taxPrice = formatDecimal(
              data.totalAmountPrice - data.noTotalAmountPrice,
            );
          } else {
            data.unitTaxPrice = 0;
            data.noUnitTaxPrice = 0;
            data.noTotalAmountPrice = 0;
            data.taxPrice = 0;
          }
          break;
      }
    },
    resetField() {
      this.$refs.formRef.resetFields();
      this.form.tableData = [];
    },
    getParams() {
      return this.form.tableData;
    },
    validForm() {
      return new Promise((resolve) => {
        if (this.$refs.formRef) {
          this.$refs.formRef.validate((valid) => {
            resolve(valid);
          });
        } else {
          resolve(true);
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.total-container {
  font-size: 14px;
  .total-content {
    color: rgba(0, 0, 0, 0.65);
  }
  .total-name {
    font-weight: 700;
  }
}
</style>
