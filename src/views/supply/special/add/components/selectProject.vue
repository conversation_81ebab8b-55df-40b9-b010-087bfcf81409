<template>
  <el-dialog
    title="项目机会跟进"
    :visible="dialogVisible"
    width="1000px"
    append-to-body
    :close-on-click-modal="false"
    :before-close="cancelClick"
    @open="onOpenDig"
  >
    <search-table-content
      style="height: 500px"
      :table-loading="loading"
      :ref="searchTableRef"
      :tableData="tableData"
      :tablePage="tablePage"
      :columns="columns"
      :isSelected="false"
      @query="query"
      @reset="reset"
      @pagination="pagination"
      :inner="true"
      :heightAuto="false"
    >
      <template #search>
        <search-item label="主体名称" prop="subjectSelectName">
          <el-select
            v-model="searchForm.subjectSelectName"
            :placeholder="$t('placeholder.select')"
            clearable
          >
            <el-option
              v-for="item in dictData.subject_select"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            ></el-option>
          </el-select>
        </search-item>
      </template>
      <template #radio="{ data }">
        <el-radio v-model="currentRow" :label="data.projectNum"
          ><span></span
        ></el-radio>
      </template>
    </search-table-content>
    <span slot="footer" class="dialog-footer">
      <el-button
        class="oper-btn cancel-btn"
        @click="cancelClick"
        :disabled="loading"
        >{{ $t("common.cancel") }}</el-button
      >
      <el-button
        type="primary"
        class="oper-btn"
        @click="submitClick"
        :disabled="loading"
        >{{ $t("common.submit") }}</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
import Page from "@/components/searchTableContent/mixins/page";
import { getOpportunityList } from "@/api/business/projectOpportunity";
export default {
  name: "selectCustomer",
  mixins: [Page],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    rowObj: {
      type: Object,
      default: () => {},
    },
    selectedList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loading: false,
      searchForm: {
        subjectSelectName: "",
        // name: "",
        customerName: "",
      },
      columns: [
        {
          label: "",
          render: "radio",
          align: "center",
          width: "50px",
        },
        {
          label: "项目编号",
          prop: "projectNum",
        },
        {
          label: "项目名称",
          prop: "projectName",
        },
        {
          label: "销售负责人",
          prop: "salesPerson",
        },
        // {
        //   label: "客户名称",
        //   prop: "customerName",
        // },
        {
          label: "项目阶段",
          prop: "projectStage",
        },
      ],
      selectList: [],
      initQuery: false,
      currentRow: "",
      dictData: {
        subject_select: [],
      },
    };
  },
  methods: {
    request: getOpportunityList,

    onOpenDig() {
      this.getDictList();
      if (this.rowObj.customerName) {
        this.searchForm.customerName = this.rowObj.customerName;
      }
      this.reset();
    },
    tableLoadData() {
      this.$nextTick(() => {
        const tableRef = this.$refs?.[this.searchTableRef]?.getTableRef();
        console.log(this.selectedList);
        this.selectedList.forEach((item) => {
          this.tableData.forEach((i) => {
            if (i.projectNum == item.projectNum) {
              //   tableRef.toggleRowSelection(i, true);
              this.currentRow = i.projectNum;
            }
          });
        });
      });
    },
    cancelClick() {
      this.$refs[this.searchTableRef].clearSelection();
      this.tableData = [];
      //   this.selectList = [];
      this.currentRow = "";
      this.$emit("eventClose");
    },
    submitClick() {
      if (!this.currentRow) {
        this.errorMsg("请选择客户档案");
        return false;
      }
      let select = this.tableData.find(
        (item) => item.projectNum == this.currentRow,
      );
      this.$emit("eventSucc", select);
      this.cancelClick();
    },
  },
};
</script>
