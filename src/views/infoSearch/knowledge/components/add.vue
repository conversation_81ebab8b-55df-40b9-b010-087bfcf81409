<template>
  <el-dialog
    class="form-dialog"
    :title="isEdit ? $t('common.edit') : $t('common.add')"
    :visible="dialogVisible"
    width="700px"
    :close-on-click-modal="false"
    :append-to-body="true"
    @open="onOpenDig"
    :before-close="onCloseClick"
  >
    <div v-loading="loading">
      <el-form
        :model="form"
        :rules="rules"
        ref="formRef"
        label-position="right"
        label-width="auto"
        class="mgr40"
      >
        <base-title title="基本信息" />
        <sw-form-item label="标题" prop="notesTitle">
          <el-input
            v-model="form.notesTitle"
            :placeholder="$t('placeholder.place')"
            clearable
            maxlength="50"
          ></el-input>
        </sw-form-item>
        <sw-form-item label="文件类型" prop="type">
          <dict-select
            v-model="form.fileType"
            filterable
            clearable
            placeholder="请选择"
            dictName="文件类型"
            dictCode="knowledge_file_type"
            :options="dictData.knowledge_file_type"
          ></dict-select>
        </sw-form-item>
        <sw-form-item label="负责人" prop="assignedUserId">
          <user-select
            v-if="!isEdit"
            v-model="form.assignedUserId"
          ></user-select>
          <span v-else>{{ form.assignedUserName }}</span>
        </sw-form-item>
        <sw-form-item label="说明" prop="remark">
          <el-input
            type="textarea"
            v-model="form.remark"
            :placeholder="$t('placeholder.place')"
            clearable
            maxlength="150"
            show-word-limit
          ></el-input>
        </sw-form-item>
        <base-title title="文档信息" />
        <sw-form-item label="文件" prop="fileList" ref="fileDomRef">
          <multiple-file-upload
            v-model="form.fileList"
            :size="500"
            unit="MB"
            :limit="100"
            :autoUpload="true"
            :showFileList="false"
            @change="onFileChange"
            @upload="submitFileClick"
          ></multiple-file-upload>
          <div class="file-box" v-if="form.fileInfo?.length > 0">
            <div class="file-item" v-for="item in form.fileInfo" :key="item.id">
              <span :title="`${item.fileName}(${item.fileSize})`"
                >{{ item.fileName }}<span>({{ item.fileSize }})</span></span
              >
              <operatebtns
                :row="item"
                :btns="operateBtns"
                :limit="operateBtns.length"
              ></operatebtns>
            </div>
          </div>
        </sw-form-item>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button
        @click="onCloseClick"
        :loading="loading"
        class="oper-btn cancel-btn"
        >{{ $t("common.cancel") }}</el-button
      >
      <el-button
        type="primary"
        class="oper-btn"
        @click="onSubmitClick"
        :loading="loading"
        >{{ $t("common.submit") }}</el-button
      >
    </div>
  </el-dialog>
</template>
<script>
import {
  addKnowledge,
  editKnowledge,
  uploadKnowledgeFile,
  deleteKnowledgeFile,
  getKnowledgeDetail,
} from "@/api/infoSearch/knowledge.js";
import userSelect from "@/components/userSelect/index";
import { cloneDeep } from "lodash";
let defaultOption = {
  notesTitle: "",
  assignedUserId: "",
  fileType: "",
  remark: "",
  fileList: [],
  fileInfo: [],
};
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    rowObj: {
      type: Object,
      default: () => {
        return {};
      },
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    dictData: {
      type: Object,
      default: () => {
        return {
          assetDiscoveryTaskType: [],
        };
      },
    },
  },
  components: { userSelect },
  data() {
    return {
      loading: false,
      form: {},
      rules: {
        notesTitle: [
          {
            required: true,
            message: this.$t("placeholder.place"),
            trigger: "blur",
          },
        ],
        assignedUserId: [
          {
            required: true,
            message: this.$t("placeholder.select"),
            trigger: "change",
          },
        ],
      },
      operateBtns: [
        {
          name: this.$t("common.delete"),
          class: "delete-text-btn",
          permi: ["none:none:none"],
          func: this.onDeleteFile,
        },
        {
          name: this.$t("common.preview"),
          class: "oper-text-btn",
          permi: ["none:none:none"],
          func: this.onPreviewClick,
        },
      ],
    };
  },
  created() {},
  methods: {
    onOpenDig() {
      this.form = { ...defaultOption };
      if (this.isEdit) {
        this.loading = true;
        getKnowledgeDetail({ id: this.rowObj.id })
          .then((res) => {
            this.form = {
              ...defaultOption,
              ...res.data,
              assignedUserId: Number(res.data.assignedUserId),
            };
            this.form.fileList = cloneDeep(res.data.files);
            this.form.fileInfo = cloneDeep(res.data.files);
          })
          .finally(() => {
            this.loading = false;
          });
      }
    },

    onFileChange() {
      this.$refs.fileDomRef.$children[0].clearValidate();
    },
    submitFileClick(fileInfo) {
      this.loading = true;
      uploadKnowledgeFile({ file: fileInfo.file })
        .then((res) => {
          this.form.fileInfo.push(res.data);
        })
        .finally(() => {
          this.loading = false;
        });
    },

    onDeleteFile(row) {
      this.confirmMessage(this.$t("tips.deleteTip")).then((res) => {
        this.loading = true;
        deleteKnowledgeFile({ id: row.id })
          .then((res) => {
            this.form.fileInfo.splice(this.form.fileInfo.indexOf(row), 1);
          })
          .finally(() => {
            this.loading = false;
          });
      });
    },

    onPreviewClick(row) {
      const routeData = this.$router.resolve({
        path: "/preview/online",
        query: { fileName: row.fileName, fileId: row.id, source: "knowledge" },
      });
      window.open(routeData.href, "_blank");
    },
    onCloseClick() {
      this.form.fileInfo = [];
      this.$refs.formRef?.resetFields();
      this.$emit("eventClose");
    },
    onSubmitClick() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.loading = true;
          let request = this.isEdit ? editKnowledge : addKnowledge;
          let params = cloneDeep({
            ...this.form,
            fileIds: this.form.fileInfo.map((i) => i.id),
          });
          delete params.fileList;
          delete params.fileInfo;
          delete params.files;
          request(params)
            .then((res) => {
              this.successMsg(
                this.isEdit ? this.$t("msg.edit") : this.$t("msg.add"),
              );
              this.$emit("eventSucc");
              this.onCloseClick();
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.file-box {
  margin-top: 16px;
  width: 100%;
  .file-item {
    display: flex;
    align-items: center;
    & > span {
      display: inline-block;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      line-height: 32px;
      span {
        margin-left: 4px;
        color: rgba(0, 0, 0, 0.45);
      }
    }
    .operate-btns {
      flex-shrink: 0;
    }
  }
}
</style>
