<template>
  <div class="preview-box full-width full-height">
    <div class="file-name">{{ query.fileName }}</div>
    <div class="file-content" v-loading="loading">
      <div
        class="show-doc full-width full-height"
        v-if="
          docUrl ||
          ['txt', 'c', 'py', 'java', 'xml', 'sh'].includes(form.fileType)
        "
        v-loading="loading"
      >
        <vue-office-pdf
          v-if="['pdf', 'doc', 'ppt', 'pptx'].includes(form.fileType)"
          :src="docUrl"
          :options="options"
          style="height: 100%"
          @rendered="rendered"
          @error="errorHandler"
        />
        <vue-office-docx
          v-if="form.fileType == 'docx'"
          :src="docUrl"
          :options="options"
          style="height: 100%"
          @rendered="rendered"
          @error="errorHandler"
        />
        <vue-office-excel
          v-if="['xlsx', 'xls', 'csv'].includes(form.fileType)"
          :src="docUrl"
          :options="options"
          style="height: 100%"
          @rendered="rendered"
          @error="errorHandler"
        />
        <el-image
          :src="docUrl"
          v-if="
            docUrl &&
            ['jpg', 'jpeg', 'gif', 'png', 'bmp'].indexOf(form.fileType) >= 0
          "
          :preview-src-list="[docUrl]"
          fit="contain"
          style="width: 100%; height: 100%"
          :lazy="true"
          @load="rendered"
          @error="errorHandler"
        ></el-image>
        <div
          v-if="['txt', 'c', 'py', 'java', 'xml', 'sh'].includes(form.fileType)"
          class="txt-content"
          style="white-space: pre-wrap; color: #222"
        >
          {{ content }}
        </div>
        <div v-else class="no-content">
          <sw-nodata-icon title="暂不支持预览当前格式的文件"> </sw-nodata-icon>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import VueOfficePdf from "@vue-office/pdf";
import VueOfficeDocx from "@vue-office/docx";
import VueOfficeExcel from "@vue-office/excel";
import "@vue-office/docx/lib/index.css";
import "@vue-office/excel/lib/index.css";
import {
  previewKnowledgeFile,
  getKnowledgeFileUrl,
} from "@/api/infoSearch/knowledge";
export default {
  name: "preview",
  components: {
    VueOfficePdf,
    VueOfficeDocx,
    VueOfficeExcel,
  },
  data() {
    return {
      loading: false,
      docUrl: "",
      options: {},
      content: "",
      form: {
        fileType: "",
      },
    };
  },
  computed: {
    query() {
      return this.$route.query;
    },
  },
  mounted() {
    this.getPreview();
  },
  methods: {
    getPreview() {
      let name = this.query.objName || this.query.fileName;
      let docNames = name.split(".");
      this.form.fileType = docNames[docNames.length - 1];

      if (
        ["txt", "c", "py", "java", "xml", "sh"].includes(this.form.fileType)
      ) {
        previewKnowledgeFile({ fileId: row.fileId })
          .then((res) => {
            const blob = new Blob([res.data]);
            const reader = new FileReader();
            reader.readAsText(blob);
            reader.onload = () => {
              this.content = reader.result; //获取的数据data
            };
          })
          .finally(() => {
            this.loading = false;
          });
      } else {
        this.docUrl = getKnowledgeFileUrl({ fileId: this.query.fileId });

        if (this.form.fileType == "docx") {
          this.options = {
            width: "900px", // 不生效
            className: "docx", //class name/prefix for default and document style classes
            inWrapper: true, //enables rendering of wrapper around document content
            ignoreWidth: true, //disables rendering width of page
            ignoreHeight: false, //disables rendering height of page
            ignoreFonts: false, //disables fonts rendering
            breakPages: true, //enables page breaking on page breaks
            ignoreLastRenderedPageBreak: false, //disables page breaking on lastRenderedPageBreak elements
            experimental: false, //enables experimental features (tab stops calculation)
            trimXmlDeclaration: true, //if true, xml declaration will be removed from xml documents before parsing
            useBase64URL: false, //if true, images, fonts, etc. will be converted to base 64 URL, otherwise URL.createObjectURL is used
            useMathMLPolyfill: false, //includes MathML polyfills for chrome, edge, etc.
            showChanges: false, //enables experimental rendering of document changes (inserions/deletions)
            debug: false, //enables additional logging
          };
        }
        if (this.form.fileType == "xlsx") {
          this.options = {
            xls: false, //预览xlsx文件设为false；预览xls文件设为true
            minColLength: 0, // excel最少渲染多少列，如果想实现xlsx文件内容有几列，就渲染几列，可以将此值设置为0.
            minRowLength: 0, // excel最少渲染多少行，如果想实现根据xlsx实际函数渲染，可以将此值设置为0.
            widthOffset: 10, //如果渲染出来的结果感觉单元格宽度不够，可以在默认渲染的列表宽度上再加 Npx宽
            heightOffset: 10, //在默认渲染的列表高度上再加 Npx高
          };
        }
        if (["xls", "csv"].includes(this.form.fileType)) {
          this.options = {
            xls: true, //预览xlsx文件设为false；预览xls文件设为true
            minColLength: 0, // excel最少渲染多少列，如果想实现xlsx文件内容有几列，就渲染几列，可以将此值设置为0.
            minRowLength: 0, // excel最少渲染多少行，如果想实现根据xlsx实际函数渲染，可以将此值设置为0.
            widthOffset: 100, //如果渲染出来的结果感觉单元格宽度不够，可以在默认渲染的列表宽度上再加 Npx宽
            heightOffset: 10, //在默认渲染的列表高度上再加 Npx高
          };
        }
        if (["pdf", "doc", "ppt", "pptx"].includes(this.form.fileType)) {
          this.options = {
            inWrapper: true,
            width: 800, //number，可不传，用来控制pdf预览的宽度，默认根据文档实际宽度计算
            httpHeaders: {}, //object, Basic authentication headers
            password: "", //string, 加密pdf的密码
          };
        }
      }
    },
    rendered(event) {
      this.loading = false;
      console.log(event, "---");
    },
    errorHandler(err) {
      this.loading = false;
      console.log(err, "---");
    },
  },
};
</script>
<style lang="scss" scoped>
.preview-box {
  padding: 16px;
  .file-name {
    font-size: 28px;
    font-weight: 600;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
  }
  .file-content {
    width: 100%;
    height: calc(100% - 51px);
    min-height: 500px;
    overflow: auto;
  }
  ::v-deep .docx {
    width: 900px;
  }
}
</style>
