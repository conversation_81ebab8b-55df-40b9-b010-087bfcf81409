<template>
  <div class="full-height full-width page-container">
    <smart-search-table-content
      class="full-height full-width"
      ref="searchTableRef"
      :searchForm="searchForm"
      :advanceSearchForm="advanceSearchForm"
      :columns="columns"
      :tableData="tableData"
      :table-loading="loading"
      :tablePage="tablePage"
      @reset="reset"
      @query="queryBase"
      @pagination="pagination"
      :isSelected="true"
      :isAdvance="isAdvance"
      @queryAdvance="queryAdvance"
      @selectionChange="selectionChange"
      :selectable="selectable"
    >
      <template #pageTitle>
        <page-header title="知识管理">
          <!-- <template #titleWarning>{{ $t("pageTitle.apiKey.desc") }}</template> -->
        </page-header>
      </template>
      <template #search>
        <search-item label="标题" prop="notesTitle">
          <el-input
            v-model="searchForm.notesTitle"
            :placeholder="$t('placeholder.place')"
            clearable
          />
        </search-item>
        <search-item label="文件类型" prop="fileType">
          <el-select
            v-model="searchForm.fileType"
            :placeholder="$t('placeholder.select')"
          >
            <el-option
              v-for="(item, index) in dictData.knowledge_file_type"
              :key="index"
              :name="item.name"
              :value="item.name"
            ></el-option
            >?
          </el-select>
        </search-item>
      </template>
      <template v-slot:actions>
        <batch-action-btns
          :btns="actionBtns"
          :batchBtns="batchBtns"
          :limit="batchLimit"
          :showBatch="selectList.length > 0"
          :getSelectList="() => selectList"
          :isSelectTotal="isSelectTotal"
          :total="tablePage.total"
          @onSelectTotalClick="onSelectTotalClick"
          @clearSelectList="clearSelectList"
        />
      </template>
      <template #operation="{ data }">
        <operatebtns
          :row="data"
          :btns="operateBtns"
          :limit="operbtnsLimit"
        ></operatebtns>
      </template>
    </smart-search-table-content>
    <knowledge-add
      :dialogVisible="addDialog.dialogVisible"
      :rowObj="addDialog.rowObj"
      :isEdit="addDialog.isEdit"
      :dictData="dictData"
      @eventClose="eventClose(addDialog)"
      @eventSucc="resetParamsQuery(!addDialog.isEdit)"
    ></knowledge-add>
  </div>
</template>

<script>
import Page from "@/components/smartSearchTableContent/mixins/page";
import { getKnowledgePage, deleteKnowledge } from "@/api/infoSearch/knowledge";
import knowledgeAdd from "./components/add.vue";
export default {
  mixins: [Page],
  components: {
    knowledgeAdd,
  },
  data() {
    return {
      addDialog: {
        dialogVisible: false,
        rowObj: {},
        isEdit: false,
      },
      detailDialog: {
        dialogVisible: false,
        rowObj: {},
      },
      actionBtns: [
        {
          name: "新建",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "添加模板",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-shangchuan1-line",
          func: this.onImportClick,
          permi: ["none:none:none"],
        },
        {
          name: "导出",
          id: "exportBtnId",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-daochu",
          func: this.onExportClick,
          permi: ["none:none:none"],
        },
      ],
      batchBtns: [
        {
          name: "分配",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
          rule: () => {
            return true;
          },
        },
        {
          name: "删除",
          class: "danger-text-btn",
          icon: "iconfont icon-tianjia-line",
          permi: ["none:none:none"],
          func: (row) => {
            this.confirmInfoOperFun(
              this.$t("tips.deleteTip"),
              deleteKnowledge,
              { ids: [row.id] },
              this.$t("msg.delete"),
            );
          },
        },
        {
          name: "导出",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "共享",
          class: "oper-text-btn",
          icon: "iconfont icon-shangchuan1-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "下载全部附件",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "更改创建人",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
      ],
      columns: [
        {
          label: "文档编号",
          prop: "noteNo",
        },
        {
          label: "标题",
          prop: "notesTitle",
        },
        {
          label: "负责人",
          prop: "assignedUserName",
        },
        {
          label: "文件类型",
          prop: "fileType",
        },
        {
          label: "更新人",
          prop: "modifiedByName",
        },
        {
          label: "更新时间",
          prop: "modifiedTime",
        },
        {
          label: "说明",
          prop: "remark",
        },
        {
          label: "操作",
          prop: "operation",
          render: "operation",
          width: "120px",
          align: "center",
          fixed: "right",
        },
      ],
      operateBtns: [
        {
          name: this.$t("common.detail"),
          class: "oper-text-btn",
          permi: ["none:none:none"],
          func: this.onDetailClick,
        },
        {
          name: this.$t("common.edit"),
          class: "oper-text-btn",
          permi: ["none:none:none"],
          func: this.onEditClick,
        },
        // {
        //   name: this.$t("common.downLoad"),
        //   class: "oper-text-btn",
        //   permi: ["none:none:none"],
        //   func: this.onDownloadClick,
        // },
        // {
        //   name: this.$t("common.preview"),
        //   class: "oper-text-btn",
        //   permi: ["none:none:none"],
        //   func: this.onPreviewClick,
        // },
        {
          name: this.$t("common.delete"),
          class: "delete-text-btn",
          permi: ["none:none:none"],
          func: (row) => {
            this.confirmInfoOperFun(
              this.$t("tips.deleteTip"),
              deleteKnowledge,
              { ids: [row.id] },
              this.$t("msg.delete"),
            );
          },
        },
      ],
      // 查询参数
      searchForm: {
        notesTitle: "",
        fileType: "",
      },
      dictData: {
        knowledge_file_type: [],
      },
    };
  },
  created() {
    this.getDictList();
  },
  methods: {
    request: getKnowledgePage,

    onAddClick() {
      this.addDialog = {
        dialogVisible: true,
        rowObj: {},
        isEdit: false,
      };
    },
    onCheckClick() {},
    onImportClick() {},
    onExportClick() {},
    onDetailClick(row) {},
    onEditClick(row) {
      this.addDialog = {
        dialogVisible: true,
        rowObj: {
          ...row,
        },
        isEdit: true,
      };
    },
    eventClose(dialog) {
      dialog.dialogVisible = false;
    },
  },
  beforeDestroy() {},
};
</script>
