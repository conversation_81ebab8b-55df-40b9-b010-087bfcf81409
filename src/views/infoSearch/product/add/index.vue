<template>
  <content-detail :content="isEdit ? `编辑-${queryData.name}` : '新增'">
    <menu-form-content :typeList="typeList">
      <el-form
        slot="rightContent"
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="auto"
        size="small"
        label-position="right"
      >
        <div class="right-content-item" id="base">
          <base-title title="基本信息"></base-title>
          <el-row :gutter="16">
            <el-col :span="12">
              <sw-form-item label="U9编码" prop="unineCode">
                <el-input
                  :disabled="isEdit"
                  v-model="form.unineCode"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="产品名称" prop="productName">
                <el-input
                  :disabled="isEdit"
                  v-model="form.productName"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="品牌" prop="brand">
                <!-- <el-select
                    v-model="form.brand"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="item in dictData.company_brand"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select> -->
                <dict-select
                  v-model="form.brand"
                  :options="dictData.company_brand"
                  placeholder="请选择"
                  dictName="品牌"
                  dictCode="company_brand"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="产品类型" prop="productType">
                <dict-select
                  v-model="form.productType"
                  :options="dictData.product_type"
                  placeholder="请选择"
                  dictName="产品类型"
                  dictCode="company_brand"
                  clearable
                  @queryDictList="queryDictList"
                  @change="onChangeProductType"
                />
              </sw-form-item>
            </el-col>

            <el-col :span="12">
              <sw-form-item label="产品经理" prop="productManager">
                <el-input
                  v-model="form.productManager"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>

            <el-col :span="12">
              <sw-form-item label="是否禁用" prop="disable">
                <!-- <el-radio-group v-model="form.disable" :disabled="isEdit">
                    <el-radio
                      :label="item.value"
                      v-for="item in dictData.is_disable"
                      :key="item.value"
                      >{{ item.label }}</el-radio
                    >
                  </el-radio-group> -->
                <!-- <el-select
                    v-model="form.disable"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="item in dictData.is_disable"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select> -->
                <dict-select
                  v-model="form.disable"
                  :options="dictData.is_disable"
                  placeholder="请选择"
                  dictName="是否禁用"
                  dictCode="is_disable"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <!-- // CRM产品编号？ -->

            <el-col :span="12">
              <sw-form-item label="禁用日期" prop="disableDate">
                <el-date-picker
                  v-model="form.disableDate"
                  type="date"
                  placeholder="请选择日期"
                  value-format="yyyy-MM-dd"
                  format="yyyy-MM-dd"
                  clearable
                >
                </el-date-picker>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="大类" prop="majorCategory">
                <el-input
                  :disabled="isEdit"
                  v-model="form.majorCategory"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="中类" prop="mediumCategory">
                <el-input
                  :disabled="isEdit"
                  v-model="form.mediumCategory"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="小类" prop="minorCategory">
                <el-input
                  :disabled="isEdit"
                  v-model="form.minorCategory"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>

            <!-- // 嵌入式软件名称？ -->
            <el-col :span="12">
              <sw-form-item label="国密批号" prop="gmBatchNumber">
                <el-input
                  :disabled="isEdit"
                  v-model="form.gmBatchNumber"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="产品型号" prop="productModel">
                <el-input
                  :disabled="isEdit"
                  v-model="form.productModel"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="研发部门" prop="rdDepartment">
                <el-input
                  :disabled="isEdit"
                  v-model="form.rdDepartment"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="Promotion">
          <base-title title="推广信息"></base-title>
          <el-row :gutter="20">
            <el-col :span="12">
              <sw-form-item label="市场策略" prop="marketStrategy">
                <el-input
                  v-model="form.marketStrategy"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>

            <el-col :span="12">
              <sw-form-item label="产品目录价" prop="productCatalogPrice">
                <el-input
                  v-model="form.productCatalogPrice"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="借用占用额" prop="borrowedAmount">
                <el-input
                  v-model="form.borrowedAmount"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <!-- 由产品类型直接带出 extendData-->
              <sw-form-item label="销售税率" prop="salesTaxRate">
                <el-input
                  v-model="form.salesTaxRate"
                  placeholder=""
                  disabled
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12" v-if="form.productType == '外采产品'">
              <sw-form-item label="供应商名称" prop="supplierName">
                <el-input
                  v-model="form.supplierName"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="内部结算价" prop="internalSettlePrice">
                <el-input
                  v-model="form.internalSettlePrice"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="24">
              <sw-form-item label="供货及报价说明" prop="supplyQuotationDesc">
                <el-input
                  type="textarea"
                  maxlength="1000"
                  show-word-limit
                  rows="4"
                  v-model="form.supplyQuotationDesc"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
        <div
          class="right-content-item"
          id="Product"
          v-if="
            form.majorCategory == '产成品' && form.mediumCategory != '板卡类'
          "
        >
          <base-title title="产品配置"></base-title>
          <el-row :gutter="20">
            <el-col :span="12">
              <sw-form-item label="配置方式" prop="configMethod">
                <el-input
                  :disabled="isEdit"
                  v-model="form.configMethod"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="机箱" prop="chassis">
                <el-input
                  :disabled="isEdit"
                  v-model="form.chassis"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="电源" prop="powerSupply">
                <el-input
                  :disabled="isEdit"
                  v-model="form.powerSupply"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="主板" prop="motherboard">
                <el-input
                  :disabled="isEdit"
                  v-model="form.motherboard"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="CPU" prop="cpu">
                <el-input
                  :disabled="isEdit"
                  v-model="form.cpu"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="内存" prop="memory">
                <el-input
                  :disabled="isEdit"
                  v-model="form.memory"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="存储设备+电子盘"
                prop="storageDeviceElectDisk"
              >
                <el-input
                  :disabled="isEdit"
                  v-model="form.storageDeviceElectDisk"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="网卡" prop="networkCard">
                <el-input
                  :disabled="isEdit"
                  v-model="form.networkCard"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="密码卡" prop="secretCard">
                <el-input
                  :disabled="isEdit"
                  v-model="form.secretCard"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="24">
              <sw-form-item label="备注" prop="remarks">
                <el-input
                  type="textarea"
                  maxlength="1000"
                  show-word-limit
                  rows="4"
                  :disabled="isEdit"
                  v-model="form.remarks"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>

            <el-col :span="12">
              <sw-form-item label="硬件平台" prop="hardwarePlatform">
                <el-input
                  :disabled="isEdit"
                  v-model="form.hardwarePlatform"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="Qualifications">
          <base-title title="产品资质"></base-title>
          <el-row :gutter="20">
            <el-col :span="24">
              <sw-form-item label="国密证书编号" prop="gmCertNumber">
                <el-input
                  :disabled="isEdit"
                  type="textarea"
                  maxlength="1000"
                  show-word-limit
                  rows="4"
                  v-model="form.gmCertNumber"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>

            <el-col :span="12">
              <sw-form-item label="软著登记号" prop="softwareRegNumber">
                <el-input
                  :disabled="isEdit"
                  v-model="form.softwareRegNumber"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item
                label="软著名称（嵌入式软件名称）"
                prop="softwareName"
              >
                <el-input
                  :disabled="isEdit"
                  v-model="form.softwareName"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="安全等级" prop="securityLevel">
                <el-input
                  :disabled="isEdit"
                  v-model="form.securityLevel"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="是否信创" prop="trustInnovation">
                <el-input
                  :disabled="isEdit"
                  v-model="form.trustInnovation"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="国密证书到期日期" prop="gmCertExpireDate">
                <el-input
                  :disabled="isEdit"
                  v-model="form.gmCertExpireDate"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
        <div
          class="right-content-item"
          id="enc"
          v-if="form.mediumCategory == '板卡类'"
        >
          <base-title title="密码卡"></base-title>
          <el-row :gutter="20">
            <!-- // 硬件版本？ -->
            <el-col :span="12">
              <sw-form-item label="FPGA版本" prop="fpgaVersion">
                <el-input
                  :disabled="isEdit"
                  v-model="form.fpgaVersion"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="卡内程序" prop="cardProgram">
                <el-input
                  :disabled="isEdit"
                  v-model="form.cardProgram"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>

            <el-col :span="12">
              <sw-form-item label="访问模式" prop="accessMode">
                <el-input
                  :disabled="isEdit"
                  v-model="form.accessMode"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
        <div
          class="right-content-item"
          id="software"
          v-if="
            form.majorCategory == '产成品' && form.mediumCategory != '板卡类'
          "
        >
          <base-title title="软件信息"></base-title>
          <el-row :gutter="20">
            <el-col :span="24">
              <sw-form-item
                label="对应软件版本"
                prop="correspondSoftwareVersion"
              >
                <el-input
                  :disabled="isEdit"
                  type="textarea"
                  maxlength="1000"
                  show-word-limit
                  rows="4"
                  v-model="form.correspondSoftwareVersion"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="操作系统" prop="operateSystem">
                <el-input
                  :disabled="isEdit"
                  v-model="form.operateSystem"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="UK COS版本" prop="ukcosVersion">
                <el-input
                  :disabled="isEdit"
                  v-model="form.ukcosVersion"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="访问控制参考" prop="accessControlReference">
                <el-input
                  :disabled="isEdit"
                  v-model="form.accessControlReference"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="right-content-item" id="ProCode">
          <base-title title="生产编码"></base-title>
          <el-row :gutter="20">
            <el-col :span="12">
              <sw-form-item label="生产部门" prop="productionDepartment">
                <el-input
                  :disabled="isEdit"
                  v-model="form.productionDepartment"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>

            <el-col :span="12">
              <sw-form-item label="物料编码（4位）" prop="materialCode">
                <el-input
                  :disabled="isEdit"
                  v-model="form.materialCode"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="产品编码" prop="productCode">
                <el-input
                  :disabled="isEdit"
                  v-model="form.productCode"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>

            <el-col :span="12">
              <sw-form-item label="生产编码" prop="productionCode">
                <el-input
                  :disabled="isEdit"
                  v-model="form.productionCode"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="Other">
          <base-title title="其他"></base-title>
          <el-row :gutter="20">
            <el-col :span="12">
              <sw-form-item label="其他" prop="others">
                <el-input
                  v-model="form.others"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="安全库存" prop="safetyStock">
                <el-input
                  v-model="form.safetyStock"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="是否外采" prop="outsource">
                <dict-select
                  v-model="form.outsource"
                  :options="dictData.third_party_product"
                  placeholder="请选择"
                  dictName="是否外采"
                  dictCode="third_party_product"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="是否选配" prop="optional">
                <dict-select
                  v-model="form.optional"
                  :options="dictData.select_config"
                  placeholder="请选择"
                  dictName="是否选配"
                  dictCode="select_config"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="是否已推送PDM" prop="pushPdm">
                <dict-select
                  v-model="form.pushPdm"
                  :options="dictData.push_pdm"
                  placeholder="请选择"
                  dictName="是否已推送PDM"
                  dictCode="push_pdm"
                  clearable
                  @queryDictList="queryDictList"
                />
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
        <div
          class="right-content-item"
          id="SoftParams"
          v-if="form.majorCategory == '软件组件'"
        >
          <base-title title="软件参数"></base-title>
          <el-row :gutter="20">
            <el-col :span="12">
              <sw-form-item label="属性1" prop="attributeOne">
                <el-input
                  v-model="form.attributeOne"
                  placeholder=""
                  :disabled="isEdit"
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="属性参数1" prop="attributeParamOne">
                <el-input
                  v-model="form.attributeParamOne"
                  placeholder=""
                  :disabled="isEdit"
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="属性2" prop="attributeTwo">
                <el-input
                  v-model="form.attributeTwo"
                  placeholder=""
                  :disabled="isEdit"
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="属性参数2" prop="attributeParamTwo">
                <el-input
                  v-model="form.attributeParamTwo"
                  placeholder=""
                  :disabled="isEdit"
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="指标3" prop="indicatorThree">
                <el-input
                  v-model="form.indicatorThree"
                  placeholder=""
                  :disabled="isEdit"
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="指标参数3" prop="indicatorParamThree">
                <el-input
                  v-model="form.indicatorParamThree"
                  placeholder=""
                  :disabled="isEdit"
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="指标4" prop="indicatorFour">
                <el-input
                  v-model="form.indicatorFour"
                  placeholder=""
                  :disabled="isEdit"
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="指标参数4" prop="indicatorParamFour">
                <el-input
                  v-model="form.indicatorParamFour"
                  placeholder=""
                  :disabled="isEdit"
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="指标5" prop="indicatorFive">
                <el-input
                  v-model="form.indicatorFive"
                  placeholder=""
                  :disabled="isEdit"
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="指标参数5" prop="indicatorParamFive">
                <el-input
                  v-model="form.indicatorParamFive"
                  placeholder=""
                  :disabled="isEdit"
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="指标6" prop="indicatorSix">
                <el-input
                  v-model="form.indicatorSix"
                  placeholder=""
                  :disabled="isEdit"
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="指标参数6" prop="indicatorParamSix">
                <el-input
                  v-model="form.indicatorParamSix"
                  placeholder=""
                  :disabled="isEdit"
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="指标7" prop="indicatorSeven">
                <el-input
                  v-model="form.indicatorSeven"
                  placeholder=""
                  :disabled="isEdit"
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="指标参数7" prop="indicatorParamSeven">
                <el-input
                  v-model="form.indicatorParamSeven"
                  placeholder=""
                  :disabled="isEdit"
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="指标8" prop="indicatorEight">
                <el-input
                  v-model="form.indicatorEight"
                  placeholder=""
                  :disabled="isEdit"
                ></el-input>
              </sw-form-item>
            </el-col>
            <el-col :span="12">
              <sw-form-item label="指标参数8" prop="indicatorParamEight">
                <el-input
                  v-model="form.indicatorParamEight"
                  placeholder=""
                  :disabled="isEdit"
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="right-content-item" id="attach">
          <base-title title="附表-配件"></base-title>
          <el-row :gutter="20">
            <el-col :span="12">
              <sw-form-item label="配置类型" prop="configType">
                <el-input
                  :disabled="isEdit"
                  v-model="form.configType"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </menu-form-content>
    <div slot="footer" class="page-content-footer">
      <el-button
        class="oper-btn cancel-btn mgr10"
        @click="onCloseClick"
        :loading="loading"
        >{{ $t("common.cancel") }}</el-button
      >
      <el-button
        type="primary"
        class="oper-btn"
        :loading="loading"
        @click="onSubmitClick"
        >{{ $t("common.submit") }}</el-button
      >
    </div>
  </content-detail>
</template>
<script>
import {
  getProductDetail,
  addProduct,
  editProduct,
} from "@/api/infoSearch/product";
let defaultInfo = {
  id: "",
  productType: "",
  unineCode: "",
  productName: "",
  productModel: "",
  gmBatchNumber: "",
  configType: "",
  marketStrategy: "",
  supplyQuotationDesc: "",
  productCatalogPrice: "",
  internalSettlePrice: "",
  majorCategory: "",
  mediumCategory: "",
  minorCategory: "",
  brand: "",
  disable: "",
  disableDate: "",
  rdDepartment: "",
  productManager: "",
  supplierName: "",
  borrowedAmount: "",
  salesTaxRate: "",
  configMethod: "",
  chassis: "",
  powerSupply: "",
  motherboard: "",
  cpu: "",
  memory: "",
  storageDeviceElectDisk: "",
  networkCard: "",
  secretCard: "",
  remarks: "",
  gmCertNumber: "",
  securityLevel: "",
  trustInnovation: "",
  gmCertExpireDate: "",
  softwareRegNumber: "",
  cardProgram: "",
  fpgaVersion: "",
  accessMode: "",
  operateSystem: "",
  correspondSoftwareVersion: "",
  ukcosVersion: "",
  accessControlReference: "",
  productionDepartment: "",
  productCode: "",
  materialCode: "",
  productionCode: "",
  others: "",
  safetyStock: "",
  createPerson: "",
  createTime: "",
  outsource: "",
  optional: "",
  pushPdm: "",
  attributeOne: "",
  attributeParamOne: "",
  attributeTwo: "",
  attributeParamTwo: "",
  indicatorThree: "",
  indicatorParamThree: "",
  indicatorFour: "",
  indicatorParamFour: "",
  indicatorFive: "",
  indicatorParamFive: "",
  indicatorSix: "",
  indicatorParamSix: "",
  indicatorSeven: "",
  indicatorParamSeven: "",
  indicatorEight: "",
  indicatorParamEight: "",
  softwareName: "",
  hardwarePlatform: "",
  updatePerson: "",
  updateTime: "",
};
export default {
  name: "productAdd",
  components: {},
  data() {
    return {
      isEdit: false,
      loading: false,
      dictData: {
        product_type: [],
        company_brand: [],
        is_disable: [],
        third_party_product: [],
        select_config: [],
        push_pdm: [],
      },
      typeList: [
        { label: "基本信息", value: "base" },
        { label: "推广信息", value: "Promotion" },
        {
          label: "产品配置",
          value: "Product",
          hidden: () => {
            return !(
              this.form.majorCategory == "产成品" &&
              this.form.mediumCategory != "板卡类"
            );
          },
        },
        { label: "产品资质", value: "Qualifications" },
        {
          label: "密码卡",
          value: "enc",
          hidden: () => {
            return this.form.mediumCategory != "板卡类";
          },
        },
        {
          label: "软件信息",
          value: "software",
          hidden: () => {
            return !(
              this.form.majorCategory == "产成品" &&
              this.form.mediumCategory != "板卡类"
            );
          },
        },
        { label: "生产编码", value: "ProCode" },
        { label: "其他", value: "Other" },
        {
          label: "软件参数",
          value: "SoftParams",
          hidden: () => {
            return this.form.majorCategory != "软件组件";
          },
        },
        { label: "附表-配件", value: "attach" },
      ],
      form: {},
      rules: {},
    };
  },
  computed: {
    queryData() {
      return this.$route.query;
    },
  },
  created() {
    this.isEdit = !!this.$route.query.id;
    this.initData();
    this.getDictList();
  },

  methods: {
    queryDictList() {
      console.log("重新获取字典");
      this.getDictList();
    },
    initData() {
      this.form = this.cloneDeep(defaultInfo);
      if (this.isEdit) {
        getProductDetail({ id: this.queryData.id }).then((res) => {
          this.form = {
            ...this.cloneDeep(defaultInfo),
            ...res.data,
          };
        });
      }
    },
    // 产品类型->销售税率
    onChangeProductType(val) {
      let currentObj = this.dictData.product_type.find(
        (item) => item.value == val,
      );
      if (currentObj && currentObj.extendData) {
        this.form.salesTaxRate =
          currentObj.extendData.find((item) => item.name == "销售税率")?.val ||
          "";
      } else {
        this.form.salesTaxRate = "";
      }
    },
    onCloseClick() {
      this.$refs.formRef?.resetFields();
      this.$router.go(-1);
    },
    onSubmitClick() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          let request = this.isEdit ? editProduct : addProduct;
          this.loading = true;
          request({ paramList: [this.form] })
            .then((res) => {
              this.successMsg(this.isEdit ? "编辑成功" : "新增成功");
              this.onCloseClick();
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
