<template>
  <div class="full-height full-width page-container">
    <smart-search-table-content
      class="full-height full-width"
      ref="searchTableRef"
      :searchForm="searchForm"
      :advanceSearchForm="advanceSearchForm"
      :columns="columns"
      :tableData="tableData"
      :table-loading="loading"
      :tablePage="tablePage"
      @reset="reset"
      @query="queryBase"
      @pagination="pagination"
      :isSelected="true"
      :isAdvance="isAdvance"
      @queryAdvance="queryAdvance"
      @selectionChange="selectionChange"
      :selectable="selectable"
    >
      <template #pageTitle>
        <page-header title="产品管理">
          <!-- <template #titleWarning>{{ $t("pageTitle.apiKey.desc") }}</template> -->
        </page-header>
      </template>
      <template #search>
        <search-item label="产品类型" prop="productType">
          <el-select
            v-model="searchForm.productType"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="item in dictData.product_type"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </search-item>
        <search-item label="U9编码" prop="unineCode">
          <el-input
            v-model="searchForm.unineCode"
            :placeholder="$t('placeholder.place')"
            clearable
          />
        </search-item>
      </template>
      <template v-slot:actions>
        <batch-action-btns
          :btns="actionBtns"
          :batchBtns="batchBtns"
          :limit="batchLimit"
          :showBatch="selectList.length > 0"
          :getSelectList="() => selectList"
          :isSelectTotal="isSelectTotal"
          :total="tablePage.total"
          @onSelectTotalClick="onSelectTotalClick"
          @clearSelectList="clearSelectList"
        />
      </template>
      <template #operation="{ data }">
        <operatebtns
          :row="data"
          :btns="operateBtns"
          :limit="operbtnsLimit"
        ></operatebtns>
      </template>
    </smart-search-table-content>
  </div>
</template>

<script>
import Page from "@/components/smartSearchTableContent/mixins/page";
import { getProductList, deleteProduct } from "@/api/infoSearch/product";
export default {
  mixins: [Page],
  components: {},
  data() {
    return {
      addDialog: {
        dialogVisible: false,
        rowObj: {},
        isEdit: false,
      },
      detailDialog: {
        dialogVisible: false,
        rowObj: {},
      },
      actionBtns: [
        {
          name: "新建",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "查重",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onCheckClick,
          permi: ["none:none:none"],
        },
        {
          name: "导入",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-shangchuan1-line",
          func: this.onImportClick,
          permi: ["none:none:none"],
        },
        {
          name: "导出",
          id: "exportBtnId",
          class: "action-btn blue-color-btn",
          icon: "iconfont icon-daochu",
          func: this.onExportClick,
          permi: ["none:none:none"],
        },
      ],
      batchBtns: [
        {
          name: "分配",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
          rule: () => {
            return true;
          },
        },
        {
          name: "删除",
          class: "danger-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "导出",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "共享",
          class: "oper-text-btn",
          icon: "iconfont icon-shangchuan1-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "批量编辑",
          class: "oper-text-btn",
          icon: "iconfont icon-daochu",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "提交审批",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
        },
        {
          name: "下载全部附件",
          class: "oper-text-btn",
          icon: "iconfont icon-tianjia-line",
          func: this.onAddClick,
          permi: ["none:none:none"],
          rule: () => {
            return true;
          },
        },
      ],
      columns: [
        {
          label: "产品类型",
          prop: "productType",
        },
        {
          label: "U9编码",
          prop: "unineCode",
        },
        {
          label: "产品名称",
          prop: "productName",
        },
        {
          label: "产品型号",
          prop: "productModel",
        },
        {
          label: "国密批号",
          prop: "gmBatchNumber",
        },
        {
          label: "配置类型",
          prop: "configType",
        },
        {
          label: "市场策略",
          prop: "marketStrategy",
        },
        {
          label: "供货及报价说明",
          prop: "supplyQuotationDesc",
        },
        {
          label: "产品目录价",
          prop: "productCatalogPrice",
        },
        {
          label: "内部结算价",
          prop: "internalSettlePrice",
        },
        {
          label: "大类",
          prop: "majorCategory",
        },
        {
          label: "中类",
          prop: "mediumCategory",
        },
        {
          label: "小类",
          prop: "minorCategory",
        },
        {
          label: "品牌",
          prop: "brand",
        },
        {
          label: "是否禁用",
          prop: "disable",
        },
        {
          label: "禁用日期",
          prop: "disableDate",
        },
        {
          label: "研发部门",
          prop: "rdDepartment",
        },
        {
          label: "产品经理",
          prop: "productManager",
        },
        {
          label: "供应商名称",
          prop: "supplierName",
        },
        {
          label: "借用占用额",
          prop: "borrowedAmount",
        },
        {
          label: "销售税率",
          prop: "salesTaxRate",
        },
        {
          label: "配置方式",
          prop: "configMethod",
        },
        {
          label: "机箱",
          prop: "chassis",
        },
        {
          label: "电源",
          prop: "powerSupply",
        },
        {
          label: "主板",
          prop: "motherboard",
        },
        {
          label: "cpu",
          prop: "cpu",
        },
        {
          label: "内存",
          prop: "memory",
        },
        {
          label: "存储设备+电子盘",
          prop: "storageDeviceElectDisk",
        },
        {
          label: "网卡",
          prop: "networkCard",
        },
        {
          label: "密码卡",
          prop: "secretCard",
        },
        {
          label: "备注",
          prop: "remarks",
        },
        {
          label: "国密证书编号",
          prop: "gmCertNumber",
        },
        {
          label: "安全等级",
          prop: "securityLevel",
        },
        {
          label: "是否信创",
          prop: "trustInnovation",
        },
        {
          label: "国密证书到期日期",
          prop: "gmCertExpireDate",
        },
        {
          label: "软著登记号",
          prop: "softwareRegNumber",
        },
        {
          label: "卡内程序",
          prop: "cardProgram",
        },
        {
          label: "fpga版本",
          prop: "fpgaVersion",
        },
        {
          label: "访问模式",
          prop: "accessMode",
        },
        {
          label: "操作系统",
          prop: "operateSystem",
        },
        {
          label: "对应软件版本",
          prop: "correspondSoftwareVersion",
        },
        {
          label: "UK COS版本",
          prop: "ukcosVersion",
        },
        {
          label: "访问控制参考",
          prop: "accessControlReference",
        },
        {
          label: "生产部门",
          prop: "productionDepartment",
        },
        {
          label: "产品编码",
          prop: "productCode",
        },
        {
          label: "物料编码（4位）",
          prop: "materialCode",
        },
        {
          label: "生产编码",
          prop: "productionCode",
        },
        {
          label: "其他",
          prop: "others",
        },
        {
          label: "安全库存",
          prop: "safetyStock",
        },

        {
          label: "是否外采",
          prop: "outsource",
        },
        {
          label: "是否选配",
          prop: "optional",
        },
        {
          label: "是否已推送PDM",
          prop: "pushPdm",
        },
        // {
        //   label: "属性1",
        //   prop: "attributeOne",
        // },
        // {
        //   label: "属性参数1",
        //   prop: "attributeParamOne",
        // },
        // {
        //   label: "属性2",
        //   prop: "attributeTwo",
        // },
        // {
        //   label: "属性参数2",
        //   prop: "attributeParamTwo",
        // },
        // {
        //   label: "指标3",
        //   prop: "indicatorThree",
        // },
        // {
        //   label: "指标参数3",
        //   prop: "indicatorParamThree",
        // },
        // {
        //   label: "指标4",
        //   prop: "indicatorFour",
        // },
        // {
        //   label: "指标参数4",
        //   prop: "indicatorParamFour",
        // },
        // {
        //   label: "指标5",
        //   prop: "indicatorFive",
        // },
        // {
        //   label: "指标参数5",
        //   prop: "indicatorParamFive",
        // },
        // {
        //   label: "指标6",
        //   prop: "indicatorSix",
        // },
        // {
        //   label: "指标参数6",
        //   prop: "indicatorParamSix",
        // },
        // {
        //   label: "指标7",
        //   prop: "indicatorSeven",
        // },
        // {
        //   label: "指标参数7",
        //   prop: "indicatorParamSeven",
        // },
        // {
        //   label: "指标8",
        //   prop: "indicatorEight",
        // },
        // {
        //   label: "指标参数8",
        //   prop: "indicatorParamEight",
        // },
        {
          label: "软著名称（嵌入式软件名称）",
          prop: "softwareName",
        },
        {
          label: "硬件平台",
          prop: "hardwarePlatform",
        },

        // {
        //   label: "创建人",
        //   prop: "createPerson",
        // },
        // {
        //   label: "更新人",
        //   prop: "updatePerson",
        // },
        {
          label: "创建时间",
          prop: "createTime",
        },
        {
          label: "更新时间",
          prop: "updateTime",
        },
        {
          label: "操作",
          prop: "operation",
          render: "operation",
          width: "120px",
          align: "center",
          fixed: "right",
        },
      ],
      operateBtns: [
        {
          name: this.$t("common.detail"),
          class: "oper-text-btn",
          permi: ["none:none:none"],
          func: this.onDetailClick,
        },
        {
          name: this.$t("common.edit"),
          class: "oper-text-btn",
          permi: ["none:none:none"],
          func: this.onEditClick,
        },
        {
          name: this.$t("common.delete"),
          class: "delete-text-btn",
          permi: ["none:none:none"],
          func: (row) => {
            this.confirmInfoOperFun(
              this.$t("tips.deleteTip"),
              deleteProduct,
              { paramList: [row.id] },
              this.$t("msg.delete"),
            );
          },
        },
      ],
      // 查询参数
      searchForm: {
        productType: "",
        unineCode: "",
      },
      dictData: {
        product_type: [],
        // company_brand: [],
        // is_disable: [],
        // third_party_product: [],
        // select_config: [],
        // push_pdm: [],
      },
    };
  },
  created() {
    this.getDictList();
  },
  methods: {
    request: getProductList,

    onAddClick() {
      this.$router.push({
        name: "ProductAdd",
        query: {},
      });
    },
    onCheckClick() {},
    onImportClick() {},
    onExportClick() {},
    onDetailClick(row) {},
    onEditClick(row) {
      this.$router.push({
        name: "ProductAdd",
        query: {
          id: row.id,
          name: row.productName,
        },
      });
    },
  },
  beforeDestroy() {},
};
</script>
