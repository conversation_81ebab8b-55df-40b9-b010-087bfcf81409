<template>
  <el-dialog
    title="选择客户档案"
    :visible="dialogVisible"
    width="1000px"
    append-to-body
    :close-on-click-modal="false"
    :before-close="cancelClick"
    @open="onOpenDig"
  >
    <search-table-content
      style="height: 500px"
      :table-loading="loading"
      :ref="searchTableRef"
      :tableData="tableData"
      :tablePage="tablePage"
      :columns="columns"
      :isSelected="true"
      @query="query"
      @reset="reset"
      @pagination="pagination"
      @selectionChange="selectionChange"
      :inner="true"
      :heightAuto="false"
      :selectable="selectable"
    >
      <template #search>
        <search-item label="客户编号" prop="customerCode">
          <el-input
            v-model="searchForm.customerCode"
            :placeholder="$t('placeholder.place')"
            clearable
          />
        </search-item>
        <search-item label="客户名称" prop="customerName">
          <el-input
            v-model="searchForm.customerName"
            :placeholder="$t('placeholder.place')"
            clearable
          />
        </search-item>
      </template>
    </search-table-content>
    <span slot="footer" class="dialog-footer">
      <el-button
        class="oper-btn cancel-btn"
        @click="cancelClick"
        :disabled="loading"
        >{{ $t("common.cancel") }}</el-button
      >
      <el-button
        type="primary"
        class="oper-btn"
        @click="submitClick"
        :disabled="loading"
        >{{ $t("common.submit") }}</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
import Page from "@/components/searchTableContent/mixins/page";
import { getCustomerList } from "@/api/business/customer";
export default {
  name: "batchAddCustomer",
  mixins: [Page],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    rowObj: {
      type: Object,
      default: () => {},
    },
    selectedList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loading: false,
      searchForm: {
        customerCode: "",
        customerName: "",
      },
      columns: [
        {
          label: "客户编号",
          prop: "customerCode",
        },
        {
          label: "主体名称",
          prop: "corporation",
        },
        {
          label: "客户集",
          prop: "customerGroup",
        },
        {
          label: "客户名称",
          prop: "customerName",
        },
        {
          label: "客户级别",
          prop: "customerLevel",
        },
      ],
      selectList: [],
      initQuery: false,
    };
  },
  methods: {
    request: getCustomerList,
    onOpenDig() {
      this.reset();
    },
    selectable(row) {
      let obj = this.selectedList.find((item) => item.customerId === row.id);
      return !obj;
    },
    tableLoadData() {
      this.$nextTick(() => {
        const tableRef = this.$refs?.[this.searchTableRef]?.getTableRef();
        this.selectedList.forEach((item) => {
          this.tableData.forEach((i) => {
            if (i.id == item.customerId) {
              tableRef.toggleRowSelection(i, true);
            }
          });
        });
      });
    },
    selectionChange(val) {
      this.selectList = val;
    },
    cancelClick() {
      this.$refs[this.searchTableRef].clearSelection();
      this.tableData = [];
      this.selectList = [];
      this.$emit("eventClose");
    },
    submitClick() {
      let filterList = this.selectList.filter(
        (item) => !this.selectedList.find((i) => i.customerId == item.id),
      );
      if (this.selectList.length == 0 && this.selectedList.length == 0) {
        this.errorMsg("请选择客户档案");
        return false;
      }
      this.$emit("eventSucc", filterList);
      this.cancelClick();
    },
  },
};
</script>
