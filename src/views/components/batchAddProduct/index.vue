<template>
  <el-dialog
    title="选择产品"
    :visible="dialogVisible"
    width="1000px"
    append-to-body
    :close-on-click-modal="false"
    :before-close="cancelClick"
    @open="onOpenDig"
  >
    <search-table-content
      style="height: 500px"
      :table-loading="loading"
      :ref="searchTableRef"
      :tableData="tableData"
      :tablePage="tablePage"
      :columns="columns"
      :isSelected="true"
      @query="query"
      @reset="reset"
      @pagination="pagination"
      @selectionChange="selectionChange"
      :inner="true"
      :heightAuto="false"
      :selectable="selectable"
    >
      <template #search>
        <search-item label="产品类型" prop="productType">
          <el-select
            v-model="searchForm.productType"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="item in dictData.product_type"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </search-item>
        <search-item label="U9编码" prop="unineCode">
          <el-input
            v-model="searchForm.unineCode"
            :placeholder="$t('placeholder.place')"
            clearable
          />
        </search-item>
      </template>
    </search-table-content>
    <span slot="footer" class="dialog-footer">
      <el-button
        class="oper-btn cancel-btn"
        @click="cancelClick"
        :disabled="loading"
        >{{ $t("common.cancel") }}</el-button
      >
      <el-button
        type="primary"
        class="oper-btn"
        @click="submitClick"
        :disabled="loading"
        >{{ $t("common.submit") }}</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
import Page from "@/components/searchTableContent/mixins/page";
import { getProductList } from "@/api/infoSearch/product";

export default {
  name: "batchAddCustomer",
  mixins: [Page],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    rowObj: {
      type: Object,
      default: () => {},
    },
    selectedList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loading: false,
      searchForm: {
        productType: "",
        unineCode: "",
      },
      dictData: {
        product_type: [],
        // company_brand: [],
        // is_disable: [],
        // third_party_product: [],
        // select_config: [],
        // push_pdm: [],
      },
      columns: [
        {
          label: "产品类型",
          prop: "productType",
        },
        {
          label: "U9编码",
          prop: "unineCode",
        },
        {
          label: "产品名称",
          prop: "productName",
        },
        {
          label: "产品型号",
          prop: "productModel",
        },
        {
          label: "国密批号",
          prop: "gmBatchNumber",
        },
        {
          label: "配置类型",
          prop: "configType",
        },
        {
          label: "市场策略",
          prop: "marketStrategy",
        },
        {
          label: "供货及报价说明",
          prop: "supplyQuotationDesc",
        },
        {
          label: "产品目录价",
          prop: "productCatalogPrice",
        },
        {
          label: "内部结算价",
          prop: "internalSettlePrice",
        },
        {
          label: "大类",
          prop: "majorCategory",
        },
        {
          label: "中类",
          prop: "mediumCategory",
        },
        {
          label: "小类",
          prop: "minorCategory",
        },
        {
          label: "品牌",
          prop: "brand",
        },
        {
          label: "是否禁用",
          prop: "disable",
        },
        {
          label: "禁用日期",
          prop: "disableDate",
        },
        {
          label: "研发部门",
          prop: "rdDepartment",
        },
        {
          label: "产品经理",
          prop: "productManager",
        },
        {
          label: "供应商名称",
          prop: "supplierName",
        },
        {
          label: "借用占用额",
          prop: "borrowedAmount",
        },
        {
          label: "销售税率",
          prop: "salesTaxRate",
        },
        {
          label: "配置方式",
          prop: "configMethod",
        },
        {
          label: "机箱",
          prop: "chassis",
        },
        {
          label: "电源",
          prop: "powerSupply",
        },
        {
          label: "主板",
          prop: "motherboard",
        },
        {
          label: "cpu",
          prop: "cpu",
        },
        {
          label: "内存",
          prop: "memory",
        },
        {
          label: "存储设备+电子盘",
          prop: "storageDeviceElectDisk",
        },
        {
          label: "网卡",
          prop: "networkCard",
        },
        {
          label: "密码卡",
          prop: "secretCard",
        },
        {
          label: "备注",
          prop: "remarks",
        },
        {
          label: "国密证书编号",
          prop: "gmCertNumber",
        },
        {
          label: "安全等级",
          prop: "securityLevel",
        },
        {
          label: "是否信创",
          prop: "trustInnovation",
        },
        {
          label: "国密证书到期日期",
          prop: "gmCertExpireDate",
        },
        {
          label: "软著登记号",
          prop: "softwareRegNumber",
        },
        {
          label: "卡内程序",
          prop: "cardProgram",
        },
        {
          label: "fpga版本",
          prop: "fpgaVersion",
        },
        {
          label: "访问模式",
          prop: "accessMode",
        },
        {
          label: "操作系统",
          prop: "operateSystem",
        },
        {
          label: "对应软件版本",
          prop: "correspondSoftwareVersion",
        },
        {
          label: "UK COS版本",
          prop: "ukcosVersion",
        },
        {
          label: "访问控制参考",
          prop: "accessControlReference",
        },
        {
          label: "生产部门",
          prop: "productionDepartment",
        },
        {
          label: "产品编码",
          prop: "productCode",
        },
        {
          label: "物料编码（4位）",
          prop: "materialCode",
        },
        {
          label: "生产编码",
          prop: "productionCode",
        },
        {
          label: "其他",
          prop: "others",
        },
        {
          label: "安全库存",
          prop: "safetyStock",
        },

        {
          label: "是否外采",
          prop: "outsource",
        },
        {
          label: "是否选配",
          prop: "optional",
        },
        {
          label: "是否已推送PDM",
          prop: "pushPdm",
        },
        // {
        //   label: "属性1",
        //   prop: "attributeOne",
        // },
        // {
        //   label: "属性参数1",
        //   prop: "attributeParamOne",
        // },
        // {
        //   label: "属性2",
        //   prop: "attributeTwo",
        // },
        // {
        //   label: "属性参数2",
        //   prop: "attributeParamTwo",
        // },
        // {
        //   label: "指标3",
        //   prop: "indicatorThree",
        // },
        // {
        //   label: "指标参数3",
        //   prop: "indicatorParamThree",
        // },
        // {
        //   label: "指标4",
        //   prop: "indicatorFour",
        // },
        // {
        //   label: "指标参数4",
        //   prop: "indicatorParamFour",
        // },
        // {
        //   label: "指标5",
        //   prop: "indicatorFive",
        // },
        // {
        //   label: "指标参数5",
        //   prop: "indicatorParamFive",
        // },
        // {
        //   label: "指标6",
        //   prop: "indicatorSix",
        // },
        // {
        //   label: "指标参数6",
        //   prop: "indicatorParamSix",
        // },
        // {
        //   label: "指标7",
        //   prop: "indicatorSeven",
        // },
        // {
        //   label: "指标参数7",
        //   prop: "indicatorParamSeven",
        // },
        // {
        //   label: "指标8",
        //   prop: "indicatorEight",
        // },
        // {
        //   label: "指标参数8",
        //   prop: "indicatorParamEight",
        // },
        {
          label: "软著名称",
          prop: "softwareName",
        },
        {
          label: "硬件平台",
          prop: "hardwarePlatform",
        },
        // {
        //   label: "创建人",
        //   prop: "createPerson",
        // },
        // {
        //   label: "更新人",
        //   prop: "updatePerson",
        // },
        {
          label: "创建时间",
          prop: "createTime",
        },
        {
          label: "更新时间",
          prop: "updateTime",
        },
      ],
      selectList: [],
      initQuery: false,
    };
  },
  created() {
    this.getDictList();
  },
  methods: {
    request: getProductList,
    onOpenDig() {
      this.reset();
    },
    selectable(row) {
      let obj = this.selectedList.find((item) => item.id === row.id);
      return !obj;
    },
    tableLoadData() {
      this.$nextTick(() => {
        const tableRef = this.$refs?.[this.searchTableRef]?.getTableRef();
        this.selectedList.forEach((item) => {
          this.tableData.forEach((i) => {
            if (i.id == item.id) {
              tableRef.toggleRowSelection(i, true);
            }
          });
        });
      });
    },
    selectionChange(val) {
      this.selectList = val;
    },
    cancelClick() {
      this.$refs[this.searchTableRef].clearSelection();
      this.tableData = [];
      this.selectList = [];
      this.$emit("eventClose");
    },
    submitClick() {
      let filterList = this.selectList.filter(
        (item) => !this.selectedList.find((i) => i.id == item.id),
      );
      if (this.selectList.length == 0 && this.selectedList.length == 0) {
        this.errorMsg("请选择产品");
        return false;
      }
      this.$emit("eventSucc", filterList);
      this.cancelClick();
    },
  },
};
</script>
