<template>
  <el-dialog
    title="选择客户档案"
    :visible="dialogVisible"
    width="1000px"
    append-to-body
    :close-on-click-modal="false"
    :before-close="cancelClick"
    @open="onOpenDig"
  >
    <search-table-content
      style="height: 500px"
      :table-loading="loading"
      :ref="searchTableRef"
      :tableData="tableData"
      :tablePage="tablePage"
      :columns="columns"
      :isSelected="false"
      @query="query"
      @reset="reset"
      @pagination="pagination"
      :inner="true"
      :heightAuto="false"
    >
      <template #search>
        <search-item label="客户编号" prop="customerCode">
          <el-input
            v-model="searchForm.customerCode"
            :placeholder="$t('placeholder.place')"
            clearable
          />
        </search-item>
        <search-item label="客户名称" prop="customerName">
          <el-input
            v-model="searchForm.customerName"
            :placeholder="$t('placeholder.place')"
            clearable
          />
        </search-item>
      </template>
      <template #radio="{ data }">
        <el-radio v-model="currentRow" :label="data.id"><span></span></el-radio>
      </template>
    </search-table-content>
    <span slot="footer" class="dialog-footer">
      <el-button
        class="oper-btn cancel-btn"
        @click="cancelClick"
        :disabled="loading"
        >{{ $t("common.cancel") }}</el-button
      >
      <el-button
        type="primary"
        class="oper-btn"
        @click="submitClick"
        :disabled="loading"
        >{{ $t("common.submit") }}</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
import Page from "@/components/searchTableContent/mixins/page";
import { getCustomerList } from "@/api/business/customer";
export default {
  name: "selectCustomer",
  mixins: [Page],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    rowObj: {
      type: Object,
      default: () => {},
    },
    selectedList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loading: false,
      searchForm: {
        customerCode: "",
        customerName: "",
      },
      columns: [
        {
          label: "",
          render: "radio",
          align: "center",
          width: "50px",
        },
        {
          label: "客户编号",
          prop: "customerCode",
        },
        {
          label: "主体名称",
          prop: "corporation",
        },
        {
          label: "客户集",
          prop: "customerGroup",
        },
        {
          label: "客户名称",
          prop: "customerName",
        },
        {
          label: "客户级别",
          prop: "customerLevel",
        },
      ],
      selectList: [],
      initQuery: false,
      currentRow: "",
    };
  },
  methods: {
    request: getCustomerList,

    onOpenDig() {
      this.reset();
    },
    tableLoadData() {
      this.$nextTick(() => {
        const tableRef = this.$refs?.[this.searchTableRef]?.getTableRef();
        console.log(this.selectedList);
        this.selectedList.forEach((item) => {
          this.tableData.forEach((i) => {
            if (i.id == item.id) {
              //   tableRef.toggleRowSelection(i, true);
              this.currentRow = i.id;
            }
          });
        });
      });
    },
    cancelClick() {
      this.$refs[this.searchTableRef].clearSelection();
      this.tableData = [];
      //   this.selectList = [];
      this.currentRow = "";
      this.$emit("eventClose");
    },
    submitClick() {
      if (!this.currentRow) {
        this.errorMsg("请选择客户档案");
        return false;
      }
      let select = this.tableData.find((item) => item.id == this.currentRow);
      this.$emit("eventSucc", select);
      this.cancelClick();
    },
  },
};
</script>
