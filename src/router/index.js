import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);
// 新增修改方法：获取原型对象上的push函数
const originalPush = Router.prototype.push;
// 新增修改方法：修改原型对象中的push方法
// Router.prototype.push = function push(location) {
//   let { route } = this.resolve(location);
//   window.top.postMessage({
//     type: "pushUrl",
//     query: {
//       url: swGlobal.routePrefix + route.fullPath.slice(1),
//     },
//     //给query.url传参
//     iframeQuery: {},
//   });
//   return originalPush.call(this, location).catch((err) => err);
// };

//通过portal跳转页面
Router.prototype.parentPush = function push(location) {
  let { route } = this.resolve(location);
  if (window.top === window) {
    return originalPush.call(this, location).catch((err) => err);
  } else {
    window.top.postMessage({
      type: "pushUrl",
      query: {
        url: swGlobal.routePrefix + route.fullPath.slice(1),
      },
      //给query.url传参
      iframeQuery: {},
    });
  }
  // return originalPush.call(this, location).catch((err) => err);
};

/* Layout */
import Layout from "@/layout";

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */
let baseRouters = [
  {
    path: "/redirect",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect"),
      },
    ],
  },
  {
    path: "/404",
    component: () => import("@/views/error/404"),
    hidden: true,
  },
  {
    path: "/401",
    component: () => import("@/views/error/401"),
    hidden: true,
  },
];
if (swGlobal.isInIframe != "true") {
  baseRouters = baseRouters.concat([
    {
      path: "/login",
      component: () => import("@/views/login/index"),
      hidden: true,
    },
    {
      path: "/register",
      component: () => import("@/views/register/index"),
      hidden: true,
    },
  ]);
}

// 公共路由
export const constantRoutes = baseRouters;

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [];

// 防止连续点击多次路由报错
let routerPush = Router.prototype.push;
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch((err) => err);
};

export default new Router({
  mode: "history", // 去掉url中的#
  base: process.env.VUE_APP_ROUTER_BASE,
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes,
});
