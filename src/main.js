import Vue from "vue";

import Cookies from "js-cookie";

import Element from "element-ui";
import "./assets/styles/element-variables.scss";

import "@/assets/styles/index.scss"; // global css
// 中间放置更改主题时插入的样式文件#chalk-style
import "@/assets/styles/common.scss"; // common css
import "./assets/icons/iconfont/iconfont.css";
import App from "./App";
import store from "./store";
import router from "./router";
import directive from "./directive"; // directive
import plugins from "./plugins"; // plugins
import { download } from "@/utils/request";
import Base64 from "js-base64";
import { sm3 } from "sm-crypto";
import { gm, sm } from "@/utils/crypto";
import VueClipBoard from "vue-clipboard2";
require("@Project/config/main.js");
import * as echarts from "echarts";
import "./assets/icons"; // icon
import "./permission"; // permission control
import {
  parseTime,
  resetForm,
  addDate<PERSON>ange,
  selectDictLabel,
  selectDictLabels,
  selectDictText,
  handleTree,
  getBasePrefix,
  getFullUrl,
} from "@/utils/util";
import "./plugins/components";
import { checkPermi } from "@/utils/permission";
import base from "@/utils/websocket.js";
import PortalBridge from "./utils/portalBridge";
Vue.prototype.$portalBridge = PortalBridge;
// 头部标签组件
import VueMeta from "vue-meta";

// 中英文切换
import i18n from "./i18n";

import SwKey from "@/utils/uKey/swKey.js";

import * as noticeMethods from "./utils/notice";
import * as language from "./utils/language";
import { cloneDeep } from "lodash";

Vue.prototype.$echarts = echarts;

Object.keys(noticeMethods).forEach((key) => {
  Vue.prototype[key] = noticeMethods[key];
});
Object.keys(language).forEach((key) => {
  Vue.prototype[key] = language[key];
});

Vue.use(VueClipBoard);
Vue.use(Base64);
Vue.use(base);
// Vue.use(SparkMD5)

import {
  getEnum,
  getEnumLabelByValue,
  getSysDictData,
  getDictList,
} from "@/utils/enum/index.js";
Vue.prototype.getEnum = getEnum;
Vue.prototype.getEnumLabelByValue = getEnumLabelByValue;
Vue.prototype.getSysDictData = getSysDictData;
Vue.prototype.getDictList = getDictList;

Vue.prototype.getFullUrl = getFullUrl;
// 全局方法挂载
Vue.prototype.parseTime = parseTime;
Vue.prototype.resetForm = resetForm;
Vue.prototype.addDateRange = addDateRange;
Vue.prototype.selectDictLabel = selectDictLabel;
Vue.prototype.selectDictText = selectDictText;
Vue.prototype.selectDictLabels = selectDictLabels;
Vue.prototype.download = download;
Vue.prototype.handleTree = handleTree;
Vue.prototype.$ukey = new SwKey();
Vue.prototype.checkPermi = checkPermi;
Vue.prototype.getBasePrefix = getBasePrefix;
Vue.prototype.$sm3 = sm3;
Vue.prototype.$sm = sm;
Vue.prototype.$gm = gm;
Vue.prototype.cloneDeep = cloneDeep;

Vue.use(directive);
Vue.use(plugins);
Vue.use(VueMeta);

window.str = function (num) {
  if (num == null || num == undefined) {
    return "";
  } else {
    return num + "";
  }
};

// mock
if (process.env.NODE_ENV === "development") {
  require("@/mock/index.js");
} else {
  //权限通了之后放开
  require("@/mock/index.js");
}

Vue.use(Element, {
  size: Cookies.get("size") || "default", // set element-ui default size
  i18n: (key, value) => i18n.t(key, value),
});

Vue.config.productionTip = false;

new Vue({
  el: "#app",
  router,
  store,
  i18n,
  render: (h) => h(App),
});
