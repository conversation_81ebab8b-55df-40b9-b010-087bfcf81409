import i18n from "@/i18n";
/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
  const valid_map = ["admin", "editor"];
  return valid_map.indexOf(str.trim()) >= 0;
}

/**
 * @param {string} url
 * @returns {Boolean}
 */
export function validURL(url) {
  const reg =
    /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/;
  return reg.test(url);
}

// 校验url
export function validRuleUrl(rule, value, callback) {
  if (value == "" || value == null || value == undefined) {
    return callback();
  }
  if (validURL(value)) {
    return callback();
  }
  return callback(new Error(i18n.t("validate.url")));
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validLowerCase(str) {
  const reg = /^[a-z]+$/;
  return reg.test(str);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUpperCase(str) {
  const reg = /^[A-Z]+$/;
  return reg.test(str);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validAlphabets(str) {
  const reg = /^[A-Za-z]+$/;
  return reg.test(str);
}

/**
 * @param {string} email
 * @returns {Boolean}
 */
export function validEmail(email) {
  const reg =
    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return reg.test(email);
}

export function validRuleEmail(rule, value, callback) {
  if (value == "") {
    callback();
  }
  if (validEmail(value)) {
    callback();
  }
  callback(new Error(i18n.t("validate.email")));
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function isString(str) {
  if (typeof str === "string" || str instanceof String) {
    return true;
  }
  return false;
}

/**
 * @param {Array} arg
 * @returns {Boolean}
 */
export function isArray(arg) {
  if (typeof Array.isArray === "undefined") {
    return Object.prototype.toString.call(arg) === "[object Array]";
  }
  return Array.isArray(arg);
}

/**
 * 校验手机号
 */
export const validateMobile = (value) => {
  let reg =
    /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/;
  return value.length == 11 && reg.test(value);
};
/**
 * 校验电话号码
 */
export const validatePhone = (value) => {
  // let reg = /^\d{3}-\d{8}|\d{4}-\d{7}$/
  let reg = /^\d{3,4}-\d{7,8}$/;
  return reg.test(value);
};

/**
 * 校验USA电话号码
 */
export const validateUSAPhone = (value) => {
  let reg = /^\(?(\d{3})\)?[- ]?(\d{3})[- ]?(\d{4})$/;
  return reg.test(value);
};
/**
 * 校验USA2电话号码
 */
export const validateUSA2Phone = (value) => {
  let reg = /^((((1|01|001)(\s|-|)|)|)[0-9]{3}(\s|-|)[0-9]{3}(\s|-|)[0-9]{4})$/;
  return reg.test(value);
};

export const validateAllPhone = (val) => {
  let res1 = false;
  let res2 = false;
  let res3 = false;
  let res4 = false;
  res1 = validateMobile(val);
  res2 = validatePhone(val);
  res3 = validateUSAPhone(val);
  res4 = validateUSA2Phone(val);
  return res1 || res2 || res3 || res4;
};

/**
 * 校验用户名
 */
export const validateUsername = (value) => {
  let reg = /^[a-zA-Z0-9_-]{3,15}$/;
  return reg.test(value);
};

/**
 * 校验角色名称
 */
export const validateRoleName = (value) => {
  let reg = /^[a-zA-Z0-9_-]{1,64}$/;
  return reg.test(value);
};

/**
 * 校验是否存在特殊字符(除"-、_"，中英文逗号，中英文句号，中英文分号)
 */
export function isContainsSpecialCharRemark(rule, value, callback) {
  const reg = new RegExp(
    /[(\`)(\~)(\!)(\@)(\#)(\$)(\%)(\^)(\&)(\*)(\()(\))(\+)(\=)(\|)(\{)(\})(\')(\:)(\')(\[)(\])(\<)(\>)(\/)(\?)(\~)(\！)(\@)(\#)(\￥)(\%)(\…)(\&)(\*)(\（)(\）)(\+)(\|)(\{)(\})(\【)(\】)(\‘)(\：)(\”)(\“)(\’)(\、)(\？)(\\)]+/,
  );
  if (value === undefined || value === null) {
    value = "";
  }
  if (!reg.test(value)) {
    callback();
  } else {
    callback(new Error(i18n.t("validate.specialCharRemark")));
  }
}
/**
 * 校验密码
 */
export const validatePassword_old = (value) => {
  let reg = /^[a-zA-Z0-9~!@#\$%^&*\{\};,.\?\/'"]{8,64}$/;
  if (!reg.test(value)) {
    return false;
  }
  var num = 0;
  var rule1 = /\d+/;
  var rule2 = /[a-z]+/;
  var rule3 = /[A-Z]+/;
  var rule4 = /[~!@#\$%^&*\{\};,.\?\/'"]/;
  var rule5 = /^.{8,64}$/;
  var flag1 = rule1.test(value);
  var flag2 = rule2.test(value);
  var flag3 = rule3.test(value);
  var flag4 = rule4.test(value);
  var flag5 = rule5.test(value);
  if (flag1) {
    num = num + 1;
  }
  if (flag2) {
    num = num + 1;
  }
  if (flag3) {
    num = num + 1;
  }
  if (flag4) {
    num = num + 1;
  }

  if (!(num > 2 && flag5)) {
    return false;
  }
  return true;
};

/**
 * 校验密码
 */
export const validatePassword = (rule, value, callback) => {
  let min = 8,
    max = 64;
  let reg =
    /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_!@#$%^&*`~()-+=]+$)(?![a-z0-9]+$)(?![a-z\W_!@#$%^&*`~()-+=]+$)(?![0-9\W_!@#$%^&*`~()-+=]+$)(?!.*\s)[a-zA-Z0-9\W_!@#$%^&*`~()-+=]*$/;
  const reg2 = new RegExp(`^[\\s\\S]{${min},${max}}$`);
  if (reg.test(value) && reg2.test(value)) {
    callback();
  }
  return callback(
    new Error(i18n.t("validate.password", { min: min, max: max })),
  );
};
export function validXssLen(min, max) {
  return (rule, value, callback) => {
    if (!value) {
      callback();
    }
    const reg = /[\<\>]/;
    const reg2 = new RegExp(`^[\\s\\S]{${min},${max}}$`);
    if (reg.test(value)) {
      callback(new Error(i18n.t("validate.XssLen", [min, max])));
    } else {
      if (reg2.test(value)) {
        callback();
      } else {
        callback(new Error(i18n.t("validate.XssLen", [min, max])));
      }
    }
  };
}

/**
 * 校验端口 1~65535
 */
export const validPort = (value) => {
  //   let reg = /^([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5])$/;
  if (value.length > 1 && value == 0) {
    return false;
  }
  //   let reg = /^[0-9]|[1-9]\d{1,4}$/;
  let reg = /^[0-9]$|^([1-9]\d{1,4})$/;
  let num = Number(value);
  if (reg.test(value) && num <= 65535 && num >= 1) {
    return true;
  } else {
    return false;
  }
};

/**
 * 校验端口 1~65535
 */
export const validRulePort = (rule, value, callback) => {
  if (value == "" || value == undefined || value == null) {
    return callback();
  }
  if (validPort(value)) {
    return callback();
  }
  return callback(new Error(i18n.t("validate.port")));
};

export const validRulePortRange = (min = 0, max = 65535) => {
  return (rule, value, callback) => {
    if (value == "" || value == undefined || value == null) {
      return callback();
    }
    if (validPort(value)) {
      if (value >= min && value <= max) {
        return callback();
      }
      return callback(new Error(i18n.t("validate.portRange", { min, max })));
    }

    return callback(new Error(i18n.t("validate.portRange", { min, max })));
  };
};

/**
 * 校验IP
 */
export const validIp = (value) => {
  let reg =
    /^((2[0-4]\d|25[0-5]|[01]?\d\d?)\.){3}(2[0-4]\d|25[0-5]|[01]?\d\d?)$/;
  return reg.test(value);
};

/**
 * 校验IP
 */
export const validRuleIp = (rule, value, callback) => {
  if (value == "" || value == undefined || value == null) {
    return callback();
  }
  if (validIp(value)) {
    return callback();
  }
  return callback(new Error(i18n.t("validate.ip")));
};

// 校验数字
export const validNum = (value) => {
  let reg = /^[0-9]+$/;
  return reg.test(value);
};

// 校验数字
export const validRuleNum = (rule, value, callback) => {
  if (value == "" || value == undefined || value == null) {
    return callback();
  }
  if (validNum(value)) {
    return callback();
  }
  return callback(new Error(i18n.t("validate.number")));
};

// 数字范围
export function validNumberRange(min, max) {
  return (rule, value, callback) => {
    const reg = /^(0|[1-9][0-9]*)$/;
    if (reg.test(value)) {
      let val = Number(value) || 0;
      if (val < min || val > max) {
        return callback(
          new Error(i18n.t("validate.numberRange", { min, max })),
        );
      }
      return callback();
    }
    return callback(new Error(i18n.t("validate.numberRange", { min, max })));
  };
}

// 校验是否重复
export const isRepeat = (arr) => {
  var hash = {};
  for (var i in arr) {
    if (hash[arr[i]]) {
      return true;
    }
    hash[arr[i]] = true;
  }
  return false;
};

// 校验ipv6
export const validIpv6 = (value) => {
  const reg =
    /^((\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*)(\/(([1-9])|([1-9][0-9])|(1[0-1][0-9]|12[0-8]))){0,1})*$/;
  return reg.test(value);
};

export function checkAllIP(rule, value, callback) {
  if (value == "") {
    callback();
    return;
  }
  const reg =
    /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
  const regv6 =
    /^((\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*)(\/(([1-9])|([1-9][0-9])|(1[0-1][0-9]|12[0-8]))){0,1})*$/;

  if (reg.test(value) || regv6.test(value)) {
    callback();
  } else {
    callback(new Error(i18n.t("tips.ipv4Andipv6")));
  }
}

// 校验ip/前缀
export const validIpv6OrPrefix = (value) => {
  if (value.includes("/")) {
    let arr = value.split("/");
    if (arr.length != 2) {
      return false;
    } else {
      const reg1 = /^[0-9]+$/;
      const reg0 =
        /^((\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*)(\/(([1-9])|([1-9][0-9])|(1[0-1][0-9]|12[0-8]))){0,1})*$/;
      let flag0 = reg0.test(arr[0]);
      let flag1 = reg1.test(arr[1]) && arr[1] <= 128 && arr[1] >= 1;
      return flag0 && flag1;
    }
  } else {
    return false;
  }
};

// 额外的备份名校验
export const validExtendBackUpName = (value) => {
  const reg = /^[a-zA-Z0-9_]{0,100}$/;
  return reg.test(value);
};

// 恢复路径校验
export const validRecoveryPath = (value) => {
  // 增加正则校验，除.\-外不能包含任何特殊字符
  const reg1 = /\W/;
  if (reg1.test(value.replace(/\/|\.|\-/g, ""))) {
    return false;
  }
  // if (value.includes('---') || value.includes('///') || value.includes('...')) {
  //   return false
  // }
  // 该正则容易导致回溯次数过多页面卡住
  const reg2 = /^\/?([\w+]|[\w.-]\/?){0,255}$/;
  return reg2.test(value);
};

// SMB地址
export const validSMBUrl = (value) => {
  let reg = /^smb:\/\/([A-Za-z0-9.]?)+([\w,-]+\/?)+$/;
  return reg.test(value);
};

export const validSMBUrl_ipv6 = (value) => {
  let reg =
    /^smb:\/\/\[((\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*)(\/(([1-9])|([1-9][0-9])|(1[0-1][0-9]|12[0-8]))){0,1})*\]([\w,-]*\/?)+$/;
  return reg.test(value);
};

// 路径
export const validPath = (min, max) => {
  return (rule, value, callback) => {
    if (value == "" || value == undefined || value == null) return callback();

    const reg = new RegExp(`^(?:[/?\\w+.\\-][/]?){${min},${max}}$`);
    if (reg.test(value)) return callback();
    return callback(
      new Error(
        i18n.t("validate.path1", {
          min,
          max,
        }),
      ),
    );
  };
};

// 数字、字母 -_
export const validEnCode = (min, max) => {
  return (rule, value, callback) => {
    if (value == "" || value == undefined || value == null) return callback();

    const reg = new RegExp(`^[A-Za-z0-9_-]{${min},${max}}$`);
    if (reg.test(value)) return callback();
    return callback(
      new Error(
        i18n.t("validate.enCode1", {
          min,
          max,
        }),
      ),
    );
  };
};

// 数字、字母 _
export const validEnCode2 = (min, max) => {
  return (rule, value, callback) => {
    if (value == "" || value == undefined || value == null) return callback();
    const reg = new RegExp(`^[A-Za-z0-9_]{${min},${max}}$`);
    if (reg.test(value)) return callback();
    return callback(
      new Error(
        i18n.t("validate.enCode2", {
          min,
          max,
        }),
      ),
    );
  };
};

// 数字、字母 _-.
export const validEnCode3 = (min, max) => {
  return (rule, value, callback) => {
    if (value == "" || value == undefined || value == null) return callback();
    const reg = new RegExp(`^[A-Za-z0-9_-.]{${min},${max}}$`);
    if (reg.test(value)) return callback();
    return callback(
      new Error(
        i18n.t("validate.enCode3", {
          min,
          max,
        }),
      ),
    );
  };
};

// 中文、字母、数字 _-
export const validZhCode = (min, max) => {
  return (rule, value, callback) => {
    if (value == "" || value == undefined || value == null) return callback();

    const reg = new RegExp(`^[\u4E00-\u9FA5A-Za-z0-9_-]{${min},${max}}$`);
    if (reg.test(value)) return callback();
    return callback(
      new Error(
        i18n.t("validate.zhCode1", {
          min,
          max,
        }),
      ),
    );
  };
};

// 中文、字母、数字 _
export const validZhCode2 = (min, max) => {
  return (rule, value, callback) => {
    if (value == "" || value == undefined || value == null) return callback();

    const reg = new RegExp(`^[\u4E00-\u9FA5A-Za-z0-9_]{${min},${max}}$`);
    if (reg.test(value)) return callback();
    return callback(
      new Error(
        i18n.t("validate.zhCode2", {
          min,
          max,
        }),
      ),
    );
  };
};

// 中文、字母、数字 _-.
export const validZhCode3 = (min, max) => {
  return (rule, value, callback) => {
    if (value == "" || value == undefined || value == null) return callback();

    const reg = new RegExp(`^[\u4E00-\u9FA5A-Za-z0-9_-.]{${min},${max}}$`);
    if (reg.test(value)) return callback();
    return callback(
      new Error(
        i18n.t("validate.zhCode3", {
          min,
          max,
        }),
      ),
    );
  };
};

// 校验最大长度
export function checkMaxLen(max) {
  return (rule, value, callback) => {
    if (value === undefined || value === null) {
      value = "";
    }
    if (value === "") {
      callback();
    }
    let min = 0;
    const reg = new RegExp(`^[\\s\\S]{${min},${max}}$`);
    if (reg.test(value)) {
      callback();
    } else {
      callback(new Error(i18n.t("tips.checkMaxLen", { min, max })));
    }
  };
}

// 校验子网掩码
export function validMask(rule, value, callback) {
  if (value == "" || value === undefined || value === null) {
    return callback();
  }
  const reg =
    /^(255|254|252|248|240|224|192|128|0)\.0\.0\.0$|^255\.(255|254|252|248|240|224|192|128|0)\.0\.0$|^255\.255\.(254|252|248|240|224|192|128|0)\.0$|^255\.255\.255\.(255|254|252|248|240|224|192|128|0)$/;
  if (reg.test(value)) {
    callback();
  } else {
    callback(new Error(i18n.t("validate.mask")));
  }
}

// 校验设备公钥 不支持中文
export const validPublicKey = (rule, value, callback) => {
  let reg = /[\u4E00-\u9FA5]+/;
  if (!reg.test(value)) {
    return callback();
  }
  return callback(new Error(i18n.t("validate.notSupportChinese")));
};

// 校验文件大小
export function checkFileSize(size, vm, unit = "KB", max = 20) {
  let flag = size / 1024 < max;
  unit = unit.toUpperCase();
  if (unit === "MB") {
    flag = size / 1024 / 1024 < max;
  }
  if (unit === "GB") {
    flag = size / 1024 / 1024 / 1024 < max;
  }
  if (!flag) {
    vm.errorMsg(i18n.t("validate.fileSize", { msg: `${max}${unit}` }));
    return false;
  }
  return true;
}

// 校验文件格式
export function checkFileSuff(file, vm, fileType) {
  const fileSuffix = file.name.substring(file.name.lastIndexOf(".") + 1);
  const whiteList = fileType;
  if (whiteList.indexOf(fileSuffix) === -1) {
    vm.errorMsg(i18n.t("validate.fileType", { type: whiteList }));
    return false;
  }
  return true;
}

/**
 * 校验姓名、公司名、机构名等 _-
 * @param {*} max
 * @returns
 */
export function checkCName(min, max) {
  return (rule, value, callback) => {
    if (!value) {
      callback();
    }
    const reg = new RegExp(`^[a-zA-Z0-9\-_\u4e00-\u9fa5]{${min},${max}}$`);
    if (reg.test(value)) {
      callback();
    } else {
      callback(new Error(i18n.t("tips.checkCName", [min, max])));
    }
  };
}

/**
 * _-.
 * @param {*} max
 * @returns
 */
export function checkCName2(min, max) {
  return (rule, value, callback) => {
    if (!value) {
      callback();
    }
    const reg = new RegExp(`^[a-zA-Z0-9\-_\.\u4e00-\u9fa5]{${min},${max}}$`);
    if (reg.test(value)) {
      callback();
    } else {
      callback(new Error(i18n.t("tips.checkCName2", [min, max])));
    }
  };
}

export function checkEnName(min, max) {
  return (rule, value, callback) => {
    if (value === undefined || value === null) {
      value = "";
    }
    if (value === "") {
      callback();
    }
    const reg = new RegExp(`^[a-zA-Z0-9\-\_]{${min},${max}}$`);
    if (reg.test(value)) {
      callback();
    } else {
      callback(new Error(i18n.t("tips.checkEnName", { min, max })));
    }
  };
}

export function checkEnCode1(min, max) {
  return (rule, value, callback) => {
    if (!value) {
      callback();
    }
    const reg = new RegExp(`^[A-Za-z0-9]{${min},${max}}$`);
    if (value === undefined || value === null) {
      value = "";
    }
    if (reg.test(value)) {
      callback();
    } else {
      callback(new Error(i18n.t("tips.checkEnCode1", [min, max])));
    }
  };
}

export function validInterfaceUrl(min, max) {
  return (rule, value, callback) => {
    if (value === undefined || value === null) {
      value = "";
    }
    if (value === "") return callback();

    let reg = new RegExp(`^(?=\/)(?=.{${min},${max}}$)(\/[a-zA-Z0-9._-]+)+$`);
    if (reg.test(value)) return callback();
    return callback(new Error(i18n.t("tips.requestParam", { min, max })));
  };
}

// 远程运维
// export function validateRemoteUser(rule, value, callback) {
//   if (value === undefined || value === null) {
//     value = "";
//   }
//   if (value === "") return callback();
//   let reg = new RegExp(
//     `[A-Za-z0-9\u4e00-\u9fa5~!@#\$%^&*\{\};,.\?\/'"]{1,256}`,
//   );
//   if (reg.test(value)) return callback();
//   return callback(new Error(i18n.t("validate.remoteUser")));
// }

// 固件
export function validateFirmwareVersion(rule, value, callback) {
  if (value === undefined || value === null) {
    value = "";
  }
  if (value === "") return callback();
  let reg = /^[A-Za-z0-9\u4e00-\u9fa5][A-Za-z0-9\u4e00-\u9fa5_\-\.]{0,31}$/;
  if (reg.test(value)) return callback();
  return callback(new Error(i18n.t("validate.firmware")));
}

export function validateFileDigest(rule, value, callback) {
  if (value === undefined || value === null) {
    value = "";
  }
  if (value === "") return callback();
  let reg = /^[A-Za-z0-9\u4e00-\u9fa5][A-Za-z0-9\u4e00-\u9fa5\s=:\-]{0,255}$/;
  if (reg.test(value)) return callback();
  return callback(new Error(i18n.t("validate.fileDigest")));
}

export function validateAlgorithmValue(rule, value, callback) {
  if (value === undefined || value === null) {
    value = "";
  }
  if (value === "") return callback();
  let reg = /^[A-Za-z0-9][A-Za-z0-9=]{0,255}$/;
  if (reg.test(value)) return callback();
  return callback(new Error(i18n.t("validate.algorithmValue")));
}

// 字典项
export function checkEntryName(min, max) {
  return (rule, value, callback) => {
    if (!value) {
      callback();
    }
    const reg = new RegExp(`^[a-zA-Z0-9\-_\u4e00-\u9fa5]{${min},${max}}$`);
    let list = value.split(",");
    let flag = list.some((item) => !reg.test(item));
    if (flag) {
      callback(new Error(i18n.t("tips.checkEntryName", [min, max])));
    } else {
      callback();
    }
  };
}
