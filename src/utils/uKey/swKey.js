import UKeyApi from "./uKey<PERSON><PERSON>";
import { base64ToHex, hexToBase64 } from "./base64util";
import i18n from "@/i18n";
//定义插件为运行状态
var activeRun = false;
//执行三未Key插件对象定义工作
var uKeyApi = null; //new UKeyApi();
var key = null;

/*************************封装系统中key控件操作wangtao ************************************/
class SwKey {
  constructor() {
    uKeyApi = new UKeyApi();
  }
  /**
   * ukey是否存在
   * @returns {string}
   */
  getVersion() {
    try {
      return uKeyApi.getVersion();
    } catch (e) {
      return "";
    }
  }

  /**
   * ukey是否存在
   * @returns {boolean}
   */
  isUKeyExist() {
    var random = uKeyApi.genRandom(1);
    return random !== "";
  }

  // 登录方法
  login(phxd) {
    var flag = -1;
    // -1 异常 0正确 其它：验证失败
    try {
      flag = uKeyApi.verifyPin(phxd, 1);
      return flag;
    } catch (e) {
      return flag;
    }
  }
  // 获取pin信息
  getPinInfo() {
    try {
      flag = uKeyApi.getPinInfo(1);
      return flag;
    } catch (e) {
      return flag;
    }
  }

  // 修改口令
  changeUkeyPin(oldPin, newPin) {
    var flag = -1;
    // -1 异常 0正确 其它：验证失败
    try {
      flag = uKeyApi.changePin(oldPin, newPin, 1);
      return flag;
    } catch (e) {
      return flag;
    }
  }
  // 获取签名证书
  getContainerName() {
    var containerName = "";
    var rv = uKeyApi.enumContainerName();
    if (rv.status === 0 && rv.value.length > 0) {
      containerName = rv.value[0];
      if (containerName === "ccsp_pt") {
        containerName = "";
        if (rv.value.length > 1) {
          containerName = rv.value[1];
        }
      }
    }
    return containerName;
  }

  getCert(containerName) {
    return uKeyApi.exportCertificate(containerName, 1);
  }

  // 断开连接
  disConnectDev() {
    return uKeyApi.disConnectDev();
  }
  /**
   * 写入文件
   * @param fileName 文件名称
   * @param data 数据
   * @returns 0成功 其他失败
   */
  writeFile(fileName, data) {
    var writeflag = "";
    writeflag = uKeyApi.writeFile(fileName, data);
    return writeflag;
  }
  // 读取文件
  readFile(fileName) {
    return uKeyApi.readFile(fileName);
  }

  /**
   * 内部ECC签名
   * @param containerName 容器名称
   * @param data Base64 编码的被签名数据
   * @returns {string} Base64 编码格式签名值 失败为“”
   */
  certSignData(containerName, data, type = 1) {
    // type sm2 1，rsa 2
    return type == 2
      ? uKeyApi.rsaSignData(containerName, data)
      : uKeyApi.eccSignData(containerName, data);
  }
  /**
   * 签名
   * @param {*} data
   * @returns
   */
  signData(data) {
    let hDev = sessionStorage.getItem("hDev");
    // 执行杂凑
    let pubKey = this.exportPublicKeyGM(this.getContainerName());
    //   获取容器类型
    let type = uKeyApi.getContainerType(this.getContainerName()) == 1 ? 2 : 1;
    let digestValue = uKeyApi.digestInit(hDev, type, pubKey);
    let rv = uKeyApi.digest(digestValue, data, 0);
    uKeyApi.closeDigestHandle(digestValue);
    data = rv;
    let b = this.certSignData(this.getContainerName(), data, type);
    return b;
  }
  //   signData(data) {
  //     let hDev = sessionStorage.getItem("hDev");
  //     //执行杂凑
  //     data = this.genDigest(data, hDev);
  //     let rv = this.certSignData(this.getContainerName(), data);
  //     return rv;
  //   }

  genDigest(data, hDev) {
    let pubKey = this.exportPublicKeyGM(this.getContainerName());
    let digestValue = uKeyApi.digestInit(hDev, pubKey);
    let rv = uKeyApi.digest(digestValue, data, 0);
    uKeyApi.closeDigestHandle(digestValue);
    return rv;
  }

  /**
   * ECC 外来公钥验签
   * @param pubKey Base64 编码的国密格式公钥
   * @param data Base64 编码的被签名数据 HASH
   * @param signData Base64 编码的签名结果
   * @returns 0成功 其他失败
   */
  verifyData(pubKey, data, signData) {
    return 0 === uKeyApi.eccVerify(pubKey, data, signData);
  }

  S4() {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  }
  SM4() {
    return (
      this.S4() +
      this.S4() +
      this.S4() +
      this.S4() +
      this.S4() +
      this.S4() +
      this.S4() +
      this.S4()
    );
  }
  guid() {
    return (
      this.S4() +
      this.S4() +
      "-" +
      this.S4() +
      "-" +
      this.S4() +
      "-" +
      this.S4() +
      "-" +
      this.S4() +
      this.S4() +
      this.S4()
    );
  }
  readSm4File() {
    return this.readFile("SM4File");
  }
  // 写SM4文件
  writeSm4File() {
    return this.writeFile("SM4File", this.SM4());
  }
  getSm4Key(mami) {
    var flag = this.login(mami);
    if (flag !== 0) {
      return -1;
    }
    var sm4 = this.readSm4File();
    if (sm4 === "") {
      this.writeSm4File();
      return this.readSm4File();
    } else {
      return sm4;
    }
  }
  //获取平台Sm2密钥对公钥
  getPtSM2PublicKey() {
    return this.readFile("PtSM2PublicKey");
  }
  //保存平台Sm2密钥对公钥
  savePtSM2PublicKey(ptSM2PublicKey) {
    //转换为国密格式
    var base64PubKeyGM = getPublicKeyGM(ptSM2PublicKey);
    return this.writeFile("PtSM2PublicKey", base64PubKeyGM);
  }

  // 生成密钥对
  genKeypair() {
    var uKeyPublicKey = this.exportSelfKeyPublicKey();
    if (uKeyPublicKey === "") {
      uKeyApi.createContainerName("ccsp_pt");
      //先判断有没有生成过密钥对，如果之前生成过用之前的，不重新生成，防止把之前生成密钥对冲掉。
      var publicKey = uKeyApi.genECCKeyPair("ccsp_pt");
      return !(publicKey === "");
    }
    return true;
  }

  // 获取ukey序列号
  getUKeySerial() {
    var devInfo = uKeyApi.getDevInfo();
    if (devInfo === "") {
      return "";
    }
    return devInfo.SerialNumber;
  }
  genRandom(rndLen) {
    return uKeyApi.genRandom(rndLen);
  }
  //ukey私钥签名
  uKeySignData(data) {
    return uKeyApi.eccSignData("ccsp_pt", data);
  }
  //导出指定容器名称内公钥
  exportPublicKeyGM(containerName) {
    return uKeyApi.exportPublicKey(containerName, 1, 0);
  }
  //导出密钥安全认证容器ccsp_pt内公钥 非国密结构
  exportSelfKeyPublicKey() {
    var publicKeyGM = uKeyApi.exportPublicKey("ccsp_pt", 1, 0);
    if (publicKeyGM === "") {
      return "";
    }
    return getPublicKey(publicKeyGM);
  }

  signByCert(data) {
    //获取容器名称
    var containerName = this.getContainerName();
    if (containerName == "") {
      return "";
    }
    var signGM = uKeyApi.eccSignData(containerName, data);
    if (signGM == "") {
      return "";
    }
    //签名值格式转换
    return getSign(signGM);
  }
  /**
   * 检测插件状态
   * @param callBack 回调函数 callBack(value) value 成功版本号，失败为""
   */
  checkPlugin(callBack) {
    uKeyApi.checkPlugin(callBack);
  }
  /**
   * 枚举所有设备
   * @return {*[]|*}
   */
  enumDev() {
    var rv = uKeyApi.enumDev();
    if (rv.status === 0) {
      return rv.value;
    }
    return [];
  }
  /**
   * 检查状态
   * @param rv
   */
  checkStatus(rv) {
    if (rv.status === 167772196) {
      alert("请确认USBKey口令是否正确");
      throw new Error("USBKey 调用失败" + rv.status);
    }
    if (rv.status === 167772161) {
      alert("请确认USBKey类型是否匹配");
      throw new Error("USBKey 调用失败" + rv.status);
    }
    if (rv.status !== 0) {
      alert("USBKey调用失败");
      throw new Error("USBKey 调用失败" + rv.status);
    }
  }
}

/** *************************END********************************* */
// 获取证书信息
function fillCert(phxd) {
  if (key === null) {
    key = new SwKey();
  }

  var uKeyInfo = {};
  if (!phxd || phxd === "") {
    layer.msg("UKey密码不可为空", {
      icon: 0,
      time: 5000,
    });
    return -1;
  }

  /*var initFlag = key.isUKeyExist();
    if (!initFlag) {
        layer.msg('证书读取失败', {
            icon: 0, time: 5000
        });
        return -1;
    }*/
  var flag = key.login(phxd);

  if (flag === 262147 || flag === 27011) {
    layer.alert("UKey密码已锁定，请使用UKey管理工具解锁", {
      title: "错误信息",
      icon: 2,
      time: 5000,
    });
    return -1;
  }

  if (flag === -1) {
    layer.msg("证书读取失败", {
      icon: 0,
      time: 5000,
    });
    return -1;
  }

  if (flag === -2) {
    layer.msg("UKey未插入，请插入UKey后操作", {
      icon: 0,
      time: 5000,
    });
    return;
  }

  var cert;
  if (flag === 0) {
    var containername = key.getContainerName();
    if (containername === "") {
      layer.msg("证书读取失败", {
        icon: 0,
        time: 5000,
      });
      return -1;
    }
    cert = key.getCert(containername);
    if (null === cert || cert === "") {
      layer.msg("证书读取失败", {
        icon: 0,
        time: 5000,
      });
      return -1;
    } else {
      var random = key.genRandom(32);
      var gmSign = key.certSignData(containername, random);
      //导出国密版本密钥
      var pubKey = key.exportPublicKeyGM(containername);

      var verifySuccess = key.verifyData(pubKey, random, gmSign);
      if (!verifySuccess) {
        layer.msg("UKey证书校验失败", {
          icon: 0,
          time: 5000,
        });
        return -1;
      }
      //签名值转换为非国密格式
      var sign = getSign(gmSign);
      uKeyInfo.random = random;
      uKeyInfo.cert = cert;
      uKeyInfo.sign = sign;
      uKeyInfo.serial = key.getUKeySerial();
      return uKeyInfo;
    }
  } else {
    layer.msg("UKey口令验证失败", {
      icon: 0,
      time: 5000,
    });
    return -1;
  }
}

// 获取ukey设备序列号和公钥
function getUKeySelfKeyInfo(phxd) {
  var uKeyInfo = {};
  if (!phxd || phxd === "") {
    layer.msg("UKey密码不可为空", {
      icon: 2,
      time: 5000,
    });
    return -1;
  }
  if (key === null) {
    key = new SwKey();
  }
  var flag = key.login(phxd);

  if (flag === 262147 || flag === 27011) {
    layer.alert("UKey密码已锁定，请使用UKey管理工具解锁", {
      title: "错误信息",
      icon: 2,
      time: 5000,
    });
    return -1;
  }

  if (flag === -1) {
    layer.msg("UKey信息读取失败", {
      icon: 2,
      time: 5000,
    });
    return -1;
  }

  if (flag === -2) {
    layer.msg("UKey未插入，请插入UKey后操作", {
      icon: 2,
      time: 5000,
    });
    return;
  }

  if (flag === 0) {
    uKeyInfo.serial = key.getUKeySerial();
    var publicKey = key.exportSelfKeyPublicKey();
    if (publicKey === null || publicKey === undefined || publicKey === "") {
      key.genKeypair();
      publicKey = key.exportSelfKeyPublicKey();
      if (publicKey === null || publicKey === undefined || publicKey === "") {
        layer.msg("证书生成密钥对失败!", { icon: 2, time: 5000 });
        return -1;
      }
    }
    uKeyInfo.publicKey = publicKey;
    return uKeyInfo;
  } else {
    layer.msg("UKey口令验证失败!", { icon: 2, time: 5000 });
    return -1;
  }
}

function initSm4Key() {
  if (key == null) {
    key = new SwKey();
  }
  var sm4File = key.readSm4File();
  if (sm4File === "" || sm4File.length !== 32) {
    var writeFlag = "";
    writeFlag = key.writeSm4File();
    return writeFlag;
  } else {
    return 0;
  }
}

/**
 * 签名值转换
 * @param sign base64非国密格式签名值
 * @return base64国密格式签名值
 */
export function getGMSign(base64Sign) {
  var hexSign = base64ToHex(base64Sign).toUpperCase();
  var hexGMSign =
    "0000000000000000000000000000000000000000000000000000000000000000" +
    hexSign.substring(0, 64) +
    "0000000000000000000000000000000000000000000000000000000000000000" +
    hexSign.substring(64);
  var base64GMSign = hexToBase64(hexGMSign);
  return base64GMSign;
}

/**
 * 签名值转换
 * @param gmSign base64国密格式签名值
 * @return base64非国密格式签名值
 */
export function getSign(base64GMSign) {
  var hexGMSign = base64ToHex(base64GMSign).toUpperCase();
  var hexSign = hexGMSign.substring(64, 128) + hexGMSign.substring(192);
  var base64Sign = hexToBase64(hexSign);
  return base64Sign;
}

/**
 * 密钥结构转换
 * @param base64PublicKeyGM  base64国密格式sm2公钥
 * @return base64非国密格式公钥
 */
function getPublicKey(base64PublicKeyGM) {
  var hexPublicKeyGM = base64ToHex(base64PublicKeyGM).toUpperCase();
  var hexPublicKey =
    hexPublicKeyGM.substring(72, 136) + hexPublicKeyGM.substring(200);
  var base64PublicKey = hexToBase64(hexPublicKey);
  return base64PublicKey;
}

/**
 * 密钥结构转换
 * @param base64PublicKeyGM base64非国密格式公钥
 * @return base64国密格式sm2公钥
 */
function getPublicKeyGM(base64PublicKey) {
  //公钥转换为国密结构体
  var hexPubKey = base64ToHex(base64PublicKey).toUpperCase();
  var hexPubKeyGM =
    "000100000000000000000000000000000000000000000000000000000000000000000000" +
    hexPubKey.substring(0, 64) +
    "0000000000000000000000000000000000000000000000000000000000000000" +
    hexPubKey.substring(64);
  var base64PubKeyGM = hexToBase64(hexPubKeyGM);
  return base64PubKeyGM;
}

/**
 * 检测插件状态
 * @param callBack 回调函数 callBack(value) value 成功版本号，失败为""
 */
function checkUKeyPlugin(callBack) {
  uKeyApi.checkPlugin(callBack);
}

export default SwKey;
