import Cookies from "js-cookie";

const TokenKey = swGlobal.isCCSP == "true" ? "Manage-Token" : "AiOpsToken";
const TenantCodeKey = "SecTenantCode";

export function getToken() {
  return localStorage.getItem(TokenKey) || Cookies.get(TokenKey);
}

export function setToken(token, flag = false) {
  if (flag) {
    if (location.href.includes("https")) {
      let sameSite = "None";
      let secure = true;
      return Cookies.set(TokenKey, token, { secure, sameSite });
    } else {
      return localStorage.setItem(TokenKey, token);
    }
  } else {
    return Cookies.set(TokenKey, token);
  }
}

export function removeToken() {
  localStorage.removeItem(TokenKey);
  Cookies.remove(TokenKey);
}

export function getTenantCode() {
  return Cookies.get(TenantCodeKey);
}

export function setTenantCode(tenantCode) {
  return Cookies.set(Tenant<PERSON>ode<PERSON><PERSON>, tenantCode);
}

export function removeTenantCode() {
  return Cookies.remove(TenantCodeKey);
}
