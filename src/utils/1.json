{
  "$schema": "http://json-schema.org/draft-04/schema#",
  "type": "object",
  "properties": {
    "documentNum": {
      "type": "string",
      "description": "单据编号，由后端接口根据规则生成，接口暂无"
    },
    "projectFollowNum": {
      "type": "string",
      "description": "项目名称，项目机会跟进编码，调用项目机会跟进接口/crm/follow/projectopportunity/queryToPage，当客户名称不为空时，将客户名称作为入参；选中数据后将项目机会跟进中的项目编号赋值给该字段"
    },
    "subjectSelectName": {
      "type": "string",
      "description": "主体选择名称，下拉框"
    },
    "subjectSelectId": {
      "type": "string",
      "description": "主体选择id，下拉带出"
    },
    "supplyType": { "type": "string", "description": "供货类型，下拉框" },
    "responsiblePerson": {
      "type": "string",
      "description": "负责人，先显示当前登录人员名称，选中项目后显示项目的销售负责人"
    },
    "department": {
      "type": "string",
      "description": "所属部门，显示项目负责人的部门"
    },
    "year": {
      "type": "string",
      "description": "年份，默认当前年，下拉框，可选"
    },
    "customerName": {
      "type": "string",
      "description": "客户名称，由项目名称列表中的数据反写；可修改，调用客户档案接口/crm/customer/page：选中数据后将客户名称赋值给该字段\n"
    },
    "customerLevel": {
      "type": "string",
      "description": "客户级别，由项目机会跟进接口或客户档案接口中的数据反写"
    },
    "reason": { "type": "string", "description": "原因说明" },
    "projectLevel": { "type": "string", "description": "项目级别" },
    "actualAssessDepartment": {
      "type": "string",
      "description": "实际考核部门"
    },
    "followInstruction": { "type": "string", "description": "跟进说明" },
    "licenseAuthDesc": { "type": "string", "description": "license授权描述" },
    "contractNum": { "type": "string", "description": "合同号" },
    "requestShipmentDate": { "type": "string", "description": "申请发货日期" },
    "customerOverdue": {
      "type": "string",
      "description": "客户是否逾期，是/否"
    },
    "borrowCredentials": { "type": "string", "description": "借用凭据" },
    "duration": { "type": "string", "description": "时长" },
    "depositPayment": { "type": "number", "description": "押金缴纳" },
    "dueReturnTransferDate": {
      "type": "string",
      "description": "应归还/转销售日期"
    },
    "departmentExceedBorrowAmount": {
      "type": "string",
      "description": "部门是否超借用额度，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算"
    },
    "marketCenterExceedBorrowAmount": {
      "type": "string",
      "description": "营销中心是否整体超借用额度，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算"
    },
    "personalBorrowAmount": {
      "type": "string",
      "description": "个人借用额度，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算"
    },
    "departmentBorrowAmount": {
      "type": "string",
      "description": "部门借用额度，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算"
    },
    "personalCurrentBorrowFee": {
      "type": "string",
      "description": "个人当前借用占用费，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算"
    },
    "departmentCurrentBorrowFee": {
      "type": "string",
      "description": "部门当前借用占用费，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算"
    },
    "borrowPercentage": {
      "type": "string",
      "description": "借用占比，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算"
    },
    "departmentExceedAdvanceSupplyAmount": {
      "type": "string",
      "description": "部门是否超提前供货额度，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算"
    },
    "marketCenterExceedAdvanceSupplyAmount": {
      "type": "string",
      "description": "营销中心是否整体超提前供货额度，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算"
    },
    "expectContractSignoffDate": {
      "type": "string",
      "description": "合同预计签回时间"
    },
    "personalAdvanceSupplyAmount": {
      "type": "string",
      "description": "个人提前供货额度，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算"
    },
    "departmentAdvanceSupplyAmount": {
      "type": "string",
      "description": "部门提前供货额度，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算"
    },
    "personalCurrentAdvanceSupplyFee": {
      "type": "string",
      "description": "个人当前提前供货占用费，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算"
    },
    "departmentCurrentAdvanceSupplyFee": {
      "type": "string",
      "description": "部门当前提前供货占用费，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算"
    },
    "advanceSupplyPercentage": {
      "type": "string",
      "description": "提前供货占比，先调用接口/crm/special/supply/amount/import，导入excel初始化人员额度，再调用接口/crm/special/supply/calculate自动计算"
    },
    "addSpecialSupplyProductDTOS": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "productManageId": { "type": "string", "description": "产品管理id" },
          "unineCode": { "type": "string", "description": "U9编码" },
          "productName": { "type": "string", "description": "产品名称" },
          "productModel": { "type": "string", "description": "产品型号" },
          "applyNum": { "type": "string", "description": "申请数量" }
        },
        "required": ["applyNum", "unineCode", "productManageId"]
      },
      "description": "产品管理信息"
    },
    "attachmentIds": {
      "type": "array",
      "items": { "type": "string" },
      "description": "附件id"
    }
  },
  "required": [
    "documentNum",
    "subjectSelectName",
    "subjectSelectId",
    "supplyType",
    "reason"
  ]
}


 <el-row :gutter="20">
            <el-col :span="12">
              <sw-form-item label="联系人姓名" prop="baseData.baseData.name">
                <el-input
                  v-model="form.baseData.name"
                  placeholder="请输入"
                  clearable
                ></el-input>
              </sw-form-item>
            </el-col>
          </el-row>