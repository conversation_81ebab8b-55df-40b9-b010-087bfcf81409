import { sm2 } from "sm-crypto";
import { SM2, SM4 } from "gm-crypto";

// sm2
//为0时在加密的串前加上04,后端才能解密成功,同样后端加密后也会返回带04的加密串给前端,cipherMode为1的话必须去掉04才能解密成功
const cipherMode = 1; // 选择加密策略，1 - C1C3C2，0 - C1C2C3，默认为1
let keypair = sm2.generateKeyPairHex();
// let publicKey = keypair.publicKey; // 公钥
// let privateKey = keypair.privateKey; // 私钥
let publicKey =
  "042e2fd8b9ff3f78fbce235a1aaca69defd66255a10a6a9ba2f45e05e17bf4bc30bbead7bd5d063faee4cb5678f09d705af9c9776a018fb14d914e8ceeea7dbdf5";
// 默认生成公钥 130 位太长，可以压缩公钥到 66 位
// const compressedPublicKey = sm2.compressPublicKeyHex(publicKey); // compressedPublicKey 和 publicKey 等价
// sm2.comparePublicKeyHex(publicKey, compressedPublicKey); // 判断公钥是否等价

// sm4
let key = "078f76b6047432f4c67d474bbb0f51b6";

export const sm = {
  encodeSM2(txt) {
    let encryptData = sm2.doEncrypt(txt, publicKey, cipherMode);
    return encryptData;
  },
  decodeSM2(data) {
    //   let dataHex = data.substring(2).toLocaleLowerCase();
    return sm2.doDecrypt(data, privateKey, cipherMode);
  },
};
export const gm = {
  encodeSM2(txt) {
    let encryptData = SM2.encrypt(txt, publicKey, {
      inputEncoding: "utf8",
      outputEncoding: "base64",
    });
    return encryptData;
  },
  decodeSM2(data) {
    let decryptData = SM2.decrypt(data, privateKey, {
      inputEncoding: "base64",
      outputEncoding: "utf8",
    });
    return decryptData;
  },
  // ECB 不需要传iv
  encodeSM4(txt) {
    let encryptData = SM4.encrypt(txt, key, {
      inputEncoding: "utf8",
      outputEncoding: "base64",
    });
    return encryptData;
  },
  decodeSM4(data) {
    let decryptedData = SM4.decrypt(data, key, {
      inputEncoding: "base64",
      outputEncoding: "utf8",
    });
    return decryptedData;
  },
};
