import i18n from "@/i18n";
const enums = {
  yesNoType: [
    {
      label: i18n.t("common.no"),
      value: "0",
    },
    {
      label: i18n.t("common.yes"),
      value: "1",
    },
  ],
  Boolean: [
    {
      label: i18n.t("common.yes"),
      value: "是",
    },
    {
      label: i18n.t("common.no"),
      value: "否",
    },
  ],
  sex: [
    {
      label: "男",
      value: "男",
    },
    {
      label: "女",
      value: "女",
    },
  ],
};

export function getEnum(keys) {
  if (keys.length == 1) {
    return enums[keys[0]];
  } else {
    let obj = {};
    keys.forEach((key) => {
      obj[key] = enums[key] || [];
    });
    return obj;
  }
}

export function getEnumLabelByValue(
  list,
  value,
  labelKey = "label",
  valueKey = "value",
) {
  if (typeof list == "string") {
    return (
      enums[list].find((item) => item[valueKey] == value)?.[labelKey] || ""
    );
  }
  return list.find((item) => item[valueKey] == value)?.[labelKey] || "";
}

// 处理字典逻辑
import { getSysDicts } from "@/api/dict/index.js";

function processDictData(result) {
  return Object.keys(result).reduce((acc, key) => {
    acc[key] = (result[key] || []).map((item) => ({
      ...item,
      //   label: item.name,
      //   value: item.code,
      //   ...(item.remark ? JSON.parse(item.remark) : {}),
      value: item.dictNameCn,
      label: item.dictNameCn,
      typeCode: item.typeCode,
      extendData: item.extendData,
    }));
    return acc;
  }, {});
}

export async function getSysDictData(data) {
  try {
    let res = await getSysDicts({ dictCodes: data });
    let result = res.result || {};
    return { data: processDictData(result) };
  } catch (error) {
    return { data: {} };
  }
}

export async function getDictList() {
  let typeCodeList = Object.keys(this.dictData);
  if (typeCodeList.length > 0) {
    typeCodeList.forEach((item) => {
      this.dictData[item] = [];
    });
    getSysDicts({ dictCodes: typeCodeList }).then((res) => {
      if (res.isSuccess) {
        typeCodeList.forEach((item) => {
          this.dictData[item] =
            res.data[item]?.map((i) => {
              return {
                ...i,
                value: i.dictNameCn,
                label: i.dictNameCn,
                typeCode: i.typeCode,
                extendData: i.extendData,
                // ...(i.remark ? JSON.parse(i.remark) : {}),
              };
            }) || [];
        });
      }
    });
  }
}
