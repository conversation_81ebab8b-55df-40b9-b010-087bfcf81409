import SparkMD5 from "spark-md5";
// 是否https开头
export const isExternal = (path) => /^(https?:|mailto:|tel:)/.test(path);

// 日期格式化
export function parseTime(time, pattern) {
  if (arguments.length === 0 || !time) {
    return null;
  }
  const format = pattern || "{y}-{m}-{d} {h}:{i}:{s}";
  let date;
  if (typeof time === "object") {
    date = time;
  } else {
    if (typeof time === "string" && /^[0-9]+$/.test(time)) {
      time = parseInt(time);
    } else if (typeof time === "string") {
      time = time
        .replace(new RegExp(/-/gm), "/")
        .replace("T", " ")
        .replace(new RegExp(/\.[\d]{3}/gm), "");
    }
    if (typeof time === "number" && time.toString().length === 10) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  };
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value];
    }
    if (result.length > 0 && value < 10) {
      value = "0" + value;
    }
    return value || 0;
  });
  return time_str;
}

// 表单重置
export function resetForm(refName) {
  if (this.$refs[refName]) {
    this.$refs[refName].resetFields();
    this.$nextTick(() => {
      this.$refs[refName].clearValidate();
    });
  }
}

// 添加日期范围
export function addDateRange(params, dateRange, propName) {
  let search = params;
  search.params =
    typeof search.params === "object" &&
    search.params !== null &&
    !Array.isArray(search.params)
      ? search.params
      : {};
  dateRange = Array.isArray(dateRange) ? dateRange : [];
  if (typeof propName === "undefined") {
    search.params["beginTime"] = dateRange[0];
    search.params["endTime"] = dateRange[1];
  } else {
    search.params["begin" + propName] = dateRange[0];
    search.params["end" + propName] = dateRange[1];
  }
  return search;
}

// 回显数据字典
export function selectDictLabel(datas, value) {
  if (value === undefined) {
    return "";
  }
  var actions = [];
  Object.keys(datas).some((key) => {
    if (datas[key].value == "" + value) {
      actions.push(datas[key].label);
      return true;
    }
  });
  if (actions.length === 0) {
    actions.push(value);
  }
  return actions.join("");
}
// keyValue代表value label代表label
export function selectDictText(
  list,
  data,
  keyValue = "value",
  label = "label",
) {
  if (data === undefined) {
    return "";
  }
  var actions = [];
  Object.keys(list).some((key) => {
    if (list[key][keyValue] == "" + data) {
      actions.push(list[key][label]);
      return true;
    }
  });
  if (actions.length === 0) {
    actions.push(data);
  }
  return actions.join("");
}

// 回显数据字典（字符串数组）
export function selectDictLabels(datas, value, separator) {
  if (value === undefined) {
    return "";
  }
  var actions = [];
  var currentSeparator = undefined === separator ? "," : separator;
  var temp = value.split(currentSeparator);
  Object.keys(value.split(currentSeparator)).some((val) => {
    var match = false;
    Object.keys(datas).some((key) => {
      if (datas[key].value == "" + temp[val]) {
        actions.push(datas[key].label + currentSeparator);
        match = true;
      }
    });
    if (!match) {
      actions.push(temp[val] + currentSeparator);
    }
  });
  return actions.join("").substring(0, actions.join("").length - 1);
}

// 字符串格式化(%s )
export function sprintf(str) {
  var args = arguments,
    flag = true,
    i = 1;
  str = str.replace(/%s/g, function () {
    var arg = args[i++];
    if (typeof arg === "undefined") {
      flag = false;
      return "";
    }
    return arg;
  });
  return flag ? str : "";
}

// 转换字符串，undefined,null等转化为""
export function parseStrEmpty(str) {
  if (!str || str == "undefined" || str == "null") {
    return "";
  }
  return str;
}

// 数据合并
export function mergeRecursive(source, target) {
  for (var p in target) {
    try {
      if (target[p].constructor == Object) {
        source[p] = mergeRecursive(source[p], target[p]);
      } else {
        source[p] = target[p];
      }
    } catch (e) {
      source[p] = target[p];
    }
  }
  return source;
}

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 */
export function handleTree(data, id, parentId, children) {
  let config = {
    id: id || "id",
    parentId: parentId || "parentId",
    childrenList: children || "children",
  };

  var childrenListMap = {};
  var nodeIds = {};
  var tree = [];

  for (let d of data) {
    let parentId = d[config.parentId];
    if (childrenListMap[parentId] == null) {
      childrenListMap[parentId] = [];
    }
    nodeIds[d[config.id]] = d;
    childrenListMap[parentId].push(d);
  }

  for (let d of data) {
    let parentId = d[config.parentId];
    if (nodeIds[parentId] == null) {
      tree.push(d);
    }
  }

  for (let t of tree) {
    adaptToChildrenList(t);
  }

  function adaptToChildrenList(o) {
    if (childrenListMap[o[config.id]] !== null) {
      o[config.childrenList] = childrenListMap[o[config.id]];
    }
    if (o[config.childrenList]) {
      for (let c of o[config.childrenList]) {
        adaptToChildrenList(c);
      }
    }
  }
  return tree;
}

/**
 * 参数处理
 * @param {*} params  参数
 */
export function tansParams(params) {
  let result = "";
  for (const propName of Object.keys(params)) {
    const value = params[propName];
    var part = encodeURIComponent(propName) + "=";
    if (value !== null && typeof value !== "undefined") {
      if (typeof value === "object") {
        for (const key of Object.keys(value)) {
          if (value[key] !== null && typeof value[key] !== "undefined") {
            let params = propName + "[" + key + "]";
            var subPart = encodeURIComponent(params) + "=";
            result += subPart + encodeURIComponent(value[key]) + "&";
          }
        }
      } else {
        result += part + encodeURIComponent(value) + "&";
      }
    }
  }
  return result;
}

export function downloadTansParams(params) {
  let result = "";
  Object.keys(params).forEach((item, index) => {
    const value = params[item];
    var part = encodeURIComponent(item) + "=";
    if (value !== null && typeof value !== "undefined") {
      if (typeof value === "object") {
        for (const key of Object.keys(value)) {
          if (value[key] !== null && typeof value[key] !== "undefined") {
            let params = item + "[" + key + "]";
            var subPart = encodeURIComponent(params) + "=";
            result += subPart + encodeURIComponent(value[key]) + "&";
          }
        }
      } else {
        let pre = index == 0 ? "" : "&";
        result += pre + part + encodeURIComponent(value);
      }
    }
  });
  return result;
}

// 验证是否为blob格式
export async function blobValidate(data) {
  try {
    const text = await data.text();
    JSON.parse(text);
    return false;
  } catch (error) {
    return true;
  }
}

// 获取两个日期相差天数
export function getDifferDay(startData = new Date().getTime(), date) {
  let endDate = new Date(date).getTime();
  let msecNum = (endDate - startData) / (1000 * 60 * 60 * 24);
  return Math.floor(msecNum);
}

// 获取路由前缀  下载uk flag为ture
export function getBasePrefix(flag) {
  return flag
    ? process.env.NODE_ENV == "production"
      ? process.env.VUE_APP_ROUTER_BASE
      : "/"
    : process.env.VUE_APP_ROUTER_BASE;
}

// 获取文件的唯一MD5标识码
// export function getFileMd5(file) {
//   let file1 = file.slice(0, 5 * 1024 * 1024);
//   return new Promise((resolve, reject) => {
//     const fileReader = new FileReader();
//     const spark = new SparkMD5.ArrayBuffer();
//     fileReader.readAsArrayBuffer(file);
//     fileReader.onload = (e) => {
//       spark.append(e.target.result);
//       let md5 = spark.end();
//       resolve(md5);
//     };
//     // 测试文件超大体积时会直接触发error
//     fileReader.onerror = (e) => {
//         console.log(e.target.error);
//     };
//   });
// }
export function getFileMd5(file, chunkSize = 5 * 1024 * 1024) {
  return new Promise((resolve, reject) => {
    let blobSlice =
      File.prototype.slice ||
      File.prototype.mozSlice ||
      File.prototype.webkitSlice;
    let chunks = Math.ceil(file.size / chunkSize);
    let currentChunk = 0;
    let spark = new SparkMD5.ArrayBuffer();
    let fileReader = new FileReader();
    fileReader.onload = (e) => {
      spark.append(e.target.result);
      currentChunk++;
      if (currentChunk < chunks) {
        // console.log(
        //   `第${currentChunk}分片解析完成, 开始第${
        //     currentChunk + 1
        //   } / ${chunks}分片解析`
        // );
        loadNext();
      } else {
        console.log("finished loading");
        let md5 = spark.end();
        // console.log(
        //   `MD5计算完成：${file.name} \nMD5：${md5} \n分片：${chunks} 大小:${file.size}`
        // );
        spark.destroy();
        file.uniqueIdentifier = md5;
        file.cmd5 = false;
        // file.resume();
        resolve(md5);
      }
    };
    fileReader.onerror = (e) => {
      console.log(e.target.error);
    };
    let loadNext = () => {
      let start = currentChunk * chunkSize,
        end = start + chunkSize >= file.size ? file.size : start + chunkSize;
      fileReader.readAsArrayBuffer(blobSlice.call(file, start, end));
    };
    loadNext();
  });
}
// 获取uid
export function getUploadUid() {
  var a = 0;
  for (var c = (+new Date()).toString(32), d = 0; 5 > d; d++)
    c += Math.floor(65535 * Math.random()).toString(32);
  return "wu_" + c + (a++).toString(32);
}

// ArrayBuffer转为字符串，参数为ArrayBuffer对象
export function ab2str(buf) {
  return String.fromCharCode.apply(null, new Uint16Array(buf));
}

// 字符串转为ArrayBuffer对象，参数为字符串
export function str2ab(str) {
  var buf = new ArrayBuffer(str.length * 2); // 每个字符占用2个字节
  var bufView = new Uint16Array(buf);
  for (var i = 0, strLen = str.length; i < strLen; i++) {
    bufView[i] = str.charCodeAt(i);
  }
  return buf;
}

//将对象中value值为String类型的进行trim处理
export function trimObjectValue(obj) {
  let resObj = {};
  Object.values(obj).forEach((item, index) => {
    if (typeof item == "string") {
      item = item.trim();
    }
    resObj[Object.keys(obj)[index]] = item;
  });
  return resObj;
}
export function getFirstPage(routes, router) {
  let firstnav;
  for (var i = 0; i < routes.length; i++) {
    let item = routes[i];
    if (item.hidden == false) {
      let nav = item;
      if (nav.children && nav.children.length > 0) {
        let childNav = getFirstPage(nav.children, router);
        if (childNav) {
          return childNav;
        }
      }
      firstnav = nav;
      break;
    }
  }
  if (firstnav) {
    let firstrouter = router.resolve({
      ...firstnav,
      query: JSON.parse(firstnav.query || "{}"),
    });
    return firstrouter.route;
  }
}

// 生成uuid
export function getUuid() {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    var r = (Math.random() * 16) | 0,
      v = c == "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16).replace(/'-'/g, "");
  });
}

export function transFormData(params) {
  let formData = new FormData();
  Object.keys(params).forEach((key) => {
    formData.append(key, params[key]);
  });
  return formData;
}

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result;

  const later = function () {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp;

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last);
    } else {
      timeout = null;
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args);
        if (!timeout) context = args = null;
      }
    }
  };

  return function (...args) {
    context = this;
    timestamp = +new Date();
    const callNow = immediate && !timeout;
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait);
    if (callNow) {
      result = func.apply(context, args);
      context = args = null;
    }

    return result;
  };
}

// 查找面板参数
export function findPanelParams(to, list) {
  let query;
  for (let i = 0; i < list.length; i++) {
    let item = list[i];
    if (item.children) {
      let currentRoute = item.children.find(
        (child) => item.path + "/" + child.path == to.path,
      );
      if (currentRoute) {
        query = JSON.parse(currentRoute.query);
        break;
      }
    }
  }
  return query;
}

export function getFullUrl(url) {
  if (!!url) {
    if (url.startsWith("/")) {
      url = url.substring(1);
    }
    return getBasePrefix() + url;
  } else {
    return "";
  }
}

// 求和
export function getSum(arr, key = "value") {
  let sum = 0;
  arr.forEach((item) => {
    let num = 0;
    if (!isNaN(Number(item[key]))) {
      num = Number(item[key]);
    }
    sum = sum + num;
  });
  if (isNaN(sum)) {
    sum = "--";
  } else if ((sum + "").includes(".")) {
    sum = sum.toFixed(2);
  }
  return sum;
}

// 随机生成颜色
export function generateRandomHexColor() {
  // 生成一个随机数，并转换为16进制颜色值
  return (
    "#" + ("00000" + ((Math.random() * 0x1000000) << 0).toString(16)).substr(-6)
  );
}

export function getMock(obj = {}, addObj = {}) {
  let mockData = {};
  let columns = [];
  let formStr = "";
  let rules = {};
  let defaultInfo = {};
  Object.keys(obj).forEach((key) => {
    if (obj[key].type == "number") {
      mockData[key] = "@integer(1,100)";
    } else {
      mockData[key] = obj[key].description + "_@uuid()";
    }
  });
  columns = Object.keys(obj).map((key) => {
    return {
      label: obj[key].description,
      prop: key,
    };
  });
  columns.push({
    label: "操作",
    prop: "operation",
    render: "operation",
    width: "120px",
    align: "center",
    fixed: "right",
  });
  Object.keys(addObj).forEach((key) => {
    rules[key] = [{ required: true, message: "请输入", trigger: "blur" }];
  });
  Object.keys(addObj).forEach((key) => {
    defaultInfo[key] = "";
  });
  Object.keys(addObj).forEach((key) => {
    formStr += `<sw-form-item label="${addObj[key].description}" prop="${key}">
            <el-input
              v-model="form.${key}"
              placeholder="请输入"
              clearable
            ></el-input>
          </sw-form-item>`;
  });
  return {
    mockData,
    columns,
    formStr,
    rules,
    defaultInfo,
  };
}
