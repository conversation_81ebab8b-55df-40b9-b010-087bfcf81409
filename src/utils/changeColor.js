// 根据颜色转换色值
export function LightenDarkenColor(color, percent) {
  if (color.includes("#") && (color.length == 4 || color.length == 7)) {
    var hexList = []
    if (color.length == 4) {
      hexList = [
        parseInt("0x" + color.slice(1, 2) + color.slice(1, 2)),
        parseInt("0x" + color.slice(2, 3) + color.slice(2, 3)),
        parseInt("0x" + color.slice(3, 4) + color.slice(3, 4)),
      ];
    } else {
      hexList = [
        parseInt("0x" + color.slice(1, 3)),
        parseInt("0x" + color.slice(3, 5)),
        parseInt("0x" + color.slice(5, 7)),
      ];
    }

    let percentList = hexList.map((val) => {
      return Math.ceil(val * percent * 0.01);
    });
    return `rgb(${percentList[0]},${percentList[1]},${percentList[2]})`;
  } else {
    return color;
  }
}

// 根据颜色转换出10个颜色
export function getRandomColor(color) {
  let m = color.match(/[\da-zA-Z]{2}/g);
  for (let i = 0; i < m.length; i++) m[i] = parseInt(m[i], 16);//rgb
  var colors = [];
  for (let i = 0; i < 4; i++) {//生成3组颜色，色差20*Math.randon
    colors[i] =
      Math.floor(m[0] + (Math.random() < 0.5 ? -1 : 1) * Math.random() * 20).toString(16) +
      Math.floor(m[1] + (Math.random() < 0.5 ? -1 : 1) * Math.random() * 20).toString(16) +
      Math.floor(m[2] + (Math.random() < 0.5 ? -1 : 1) * Math.random() * 20).toString(16);
  }
  colors = colors.map(item => {
    if (item.length > 6) {
      item = item.slice(0, 6)
    }
    return '#' + item
  })
  console.log(colors)
  return colors.filter(item => item.length== 7) 
}