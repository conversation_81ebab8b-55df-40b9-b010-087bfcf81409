{"common": {"LastUpdateTime": "Last upgrade time", "LastUpgradeStatus": "Last upgrade status", "abnormal": "abnormal", "activation": "Active", "add": "Add", "addAndSave": "Add And Save", "addAuth": "Authorize", "allow": "Allow", "approved": "Approved", "audited": "Audited", "authPassword": "Authenticate Password", "authTip": "Please set the operation permission label value", "autograph": "Sign", "backEndTip": "Back end interface connection exception", "backup": "Backup", "backupFail": "Backup Failed", "backuping": "Backup in Progress", "batchAdd": "<PERSON><PERSON> Add", "batchDelete": "<PERSON><PERSON> Delete", "batchDeleteAuth": "Batch deletion", "batchOper": "Batch Operations", "batchSign": "Batch Inspection", "batchStartService": "Batch start service", "batchStopService": "Batch stop service", "batchUnbind": "Batch unbinding", "batchUpgrade": "Batch Upgrade", "cancel": "Cancel", "checkResult": "Inspection Result ", "choosePicture": "Choose Picture", "clear": "Clear", "clickUpload": "Click Upload", "close": "Close", "code": "Code", "collectMode": "Collection method", "collectUrl": "Collection Address", "companyName": "Company Name", "config": "Config", "console": "<PERSON><PERSON><PERSON>", "contactAddress": "Contact Address", "contactPerson": "Contacts", "contactPhone": "Contact Phone", "content": "Content", "copy": "Copy", "copyPublicKey": "Copy Public Key", "createTime": "Creation time", "cropper": "C<PERSON>per", "cropperClick": "Image Cropping Area", "custom": "Custom", "default": "<PERSON><PERSON><PERSON>", "delete": "Delete", "deletePublicKey": "Clear Public Key", "desc": "Description", "description": "Description", "detail": "Detail", "detailInfo": "Information", "determine": "Submit", "disable": "Disabled", "dn": "Dn", "doEncrypt": "Encrypted", "downLoad": "DownLoad", "downLoading": "Downloading data, please wait", "downloadError": "Error downloading file, please contact the administrator!", "edit": "Edit", "email": "Email", "enable": "Enable", "encryptMode": "Encryption Mode", "encryptPassword": "Encrypted Password", "endTime": "End Time", "error": "Error", "errorItems": "Abnormal item", "errorReason": "Exception Reason", "errorUk": "USBKEY Error", "expireTime": "Expiration Time", "export": "Export", "fail": "Invalid session, or the session has expired, please log in again", "failReason": "Failure reason", "failed": "Failed", "findRule": "Matching Rules", "firstName": "First Name", "forgetPassword": "Forget Password", "functions": "Features", "ge": "piece", "generateAuthCode": "Generate Authorization Code", "getVerifyCode": "Get Code", "getVerifyCodeLater": "Refetch it after {countdown}s", "history": "History", "historyReport": "History Report", "home": "Home", "hour": "Hour", "import": "Import", "importFail": "Import Failed", "importIng": "Importing in progress", "importLog": "Import Records", "importSuccess": "Import Successful", "importTime": "Import Time", "information": "Information", "initPerson": "Init Person", "interFace": "System Interface", "ip": "IP", "isBuiltIn": "Built-in", "lastName": "Last Name", "learnMore": "Learn More", "levelMessage": "Message", "login": "<PERSON><PERSON>", "logout": "Logout", "logoutTip": "Are you sure to log off and exit the system?", "minute": "Minute", "more": "More", "name": "Name", "no": "No", "noData": "No data", "none": "nothing", "normalItems": "Normal Items", "nosupport": "Not supported", "notAllow": "Not allowed", "off": "Off", "on": "On", "oneKeyCopy": "One-Key Coping", "onlySave": "Save", "operTime": "Operating Time", "operUser": "Operator", "operatingRecord": "Operating Record", "operation": "Operation", "overview": "Overview", "password": "Password", "pending": "Pending", "personal": "Personal Center", "phoneNo": "Phone Number", "pictureType": "Image type requirements：jpeg、jpg、png", "profile": "Profile", "reLogin": "Re-login", "reLoginTip": "Your login status has expired. You can stay on this page or log in again", "refresh": "Refresh", "reject": "Reject", "remark": "Remarks", "removeOut": "Remove", "reportName": "Report Name", "reportTime": "Report Time", "requestPending": "The data is being processed. Please do not submit it again", "reset": "Reset", "resetPwd": "Reset Password", "restart": "<PERSON><PERSON>", "restartTip": "The system is restarting, please wait...", "result": "Result", "roleSetting": "Permission settings", "save": "Save", "scene": "Application Scenarios", "search": "Search", "selectAll": "Select All", "selected": "Selected", "setNetworkCardUpdateTip": "The configuration has been updated, please try modifying the IP address", "setPublicKey": "Set Public Key", "shortName": "ShortName", "signin": "Sign In", "signup": "Sign Up", "signupHasAccount": "already have an account?", "signupSuccess": "Sign up successful", "sn": "Sn", "sort": "Sort", "specifications": "Specifications", "start": "Start", "startService": "Start Service", "startTime": "Start Time", "startUp": "Start-up", "status": "State", "stop": "Stop", "stopService": "Stop Service", "submit": "Submit", "submitSuccess": "Submitted successfully", "success": "Success", "synchro": "Sync Now", "sysInterTip": "System interface request timeout", "sysTip": "System Prompt", "thematicStyle": "Theme Style", "tips": "Tips", "transferMode": "Transmission Mode", "ukBtn": "Read USBKEY", "ukControl": "No USBKEY control found, please login again", "unInsetUk": "USBKEY not inserted, please login again", "unaudited": "Unaudited", "unbinding": "Unbinding", "unit": "Unit", "updateTime": "Update Time", "updateUk": "USBKEY has been replaced, please login again", "upgrade": "Upgrade", "upgradeStatus": "Upgrade Status", "uploadPicture": "Please upload pictures", "username": "User Name", "version": "Version", "versionLog": "Version Record", "versionNumber": "Version", "view": "View", "viewDetail": "View Details", "viewPersonInfo": "Personnel information", "visitProtocol": "Access Protocol", "warn": "<PERSON><PERSON>", "yes": "Yes"}, "commonCertKey": {"authRequest": "Cert Request", "bindCert": "Bind Certificate", "blacklistData": "SecretData", "caName": "CA Name", "cert": "Cert", "certCN": "Certificate CN", "certFile": "Cert File", "certInfo": "Cert Information", "certName": "Cert Name", "certPwd": "Cert Password", "certValidDate": "Cert Validity", "commonName": "Common Name", "countryName": "Country Name", "createP10": "Create P10", "curveName": "Curve Name", "decKeyName": "decryption key ", "digestValue": "Digest Value", "encryCert": "Encryption certificate", "encryptCert": "Encryption Certificate", "importEnvelopeKey": "Envelope Key Import", "importPriKey": "Import Private Key", "importPubKey": "Import Public Key", "importPubPriKey": "Import public and private keys", "installCert": "Install Cert", "issuingAuth": "Issuing Authority", "issuingAuthCert": "Issuing Authority Cert", "keyLength": "Key Length", "keyMaterial": "Key Material", "keyName": "Key Name", "keyPurpose": "Key Purpose", "keyStatus": "Key State", "keyType": "Key Type", "manualRenovate": "Manual Rotation", "organization": "Organization Unit Name", "organizationName": "Organization Name", "privateKey": "Private<PERSON><PERSON>", "privateKeyMaterical": "Private Key Material", "publicKey": "PublicKey", "publicKeyMaterical": "Public Key Material", "publisher": "Publisher", "regionAndProvinceName": "Province", "regionName": "Region Name", "signatureRequest": "Issue certificates", "subjection": "Subjection", "symmetricKey": "SymmetricKey", "symmetricKeyType": "Symmetric Key Type", "user": "User", "weight1": "Weight 1", "weight2": "Weight 2"}, "commonNet": {"dns": "DNS", "electricPort": "Electric Port", "enableSSL": "Enable SSL", "gateway": "Gateway", "ip": "IP", "ipAddress": "IP Adress", "ipv4": "IPv4", "ipv4Address": "IPv4 Address", "ipv4Gateway": "IPv4 Gateway", "ipv6": "IPv6", "ipv6Gateway": "IPv6 Gateway", "macAddress": "MAC Adress", "mask": "Mask", "mgtPort": "Management Port", "netStatus": "Network Port Status", "param": "Request Parameters", "ping": "PING", "port": "Port", "protocol": "Protocol ", "protocolConfig": "Protocol Configuration", "protocolType": "Protocol Type", "requestIp": "Request IP", "requestPort": "Request Port", "requestTime": "Request Time", "subnetMask": "Subnet Mask", "topology": "Topology", "url": "URL", "useIpv6": "Enable IPv6", "virtualIp": "Virtual IP"}, "defaultTitleList": {"SecKMS": "Key Management System", "digestweb": "", "pkiweb": "Cryptoequipment", "secauthweb": "Unified Identity Authentication System", "secdbhsmweb": "Database Encryptor", "smsweb": "Cooperative Signature System", "svsweb": "", "tsaweb": "Time Stamp System", "vpnweb": "Integrated Security Gateway"}, "error": {"back": "Go back", "goback": "Back to home page", "pageError1": "401 error!", "pageError4": "404 error!", "tip4": "Sorry, the page you are looking for does not exist. Try checking the URL for errors, then press the refresh button on the browser or try to find something else in our application.", "tipTitle1": "You do not have access!", "tipTitle4": "Page not found!", "tips1": "Sorry, you do not have access rights, please do not carry out illegal operations! You can return to the main page"}, "freeUse": {}, "initLogin": {"ukTip": "Please install the USBKEY control first, and then access the system again after installation is complete", "ukTip2": "The current USBKEY control version is too low. After installing the new version, please re access the system", "userNameSamePwd": "Username and password, password reverse order cannot be the same"}, "login": {"logout": "Log Out", "title": "Welcome to login", "verificationCode": "Verification code"}, "msg": {"add": "Added successfully", "audit": "Audit successful", "auth": "Authorization successful", "bind": "Binding Successful", "bound": "Binding successful", "change": "Switching successful", "checkFailed": "Validation Failure", "checkSuccess": "Verification successful", "clear": "Clear successfully", "config": "Config successfully", "copy": "Copy successfully", "copyFail": "<PERSON><PERSON> Failed", "delete": "Deleted successfully", "downLoadFail": "Download Failed", "edit": "Edit successfully", "exportSuccess": "Export successful", "exportTips": "Are you sure you want to export ?", "import": "Import successfully", "install": "Install successfully", "login": "Login successful", "modify": "Modified successfully", "oper": "<PERSON><PERSON> successfully", "operError": "Operation failed", "removeOutSuccess": "Removal successful", "resetPwd": "Password Reset successfully", "rollback": "<PERSON><PERSON> successfully", "save": "Save successfully", "sendSucc": "Sent successfully, please check receipt", "set": "Set successfully", "start": "Start Successfully", "stop": "Stop successfully", "sync": "<PERSON><PERSON> successfully", "ukPinErrorTip": "Password verification failed, please check if the password is entered correctly", "unbind": "Unbind successfully", "unbinding": "Unbind successfully", "update": "Update successfully", "upgrade": "Upgrade successfully", "upload": "Upload Successfully"}, "pageTitle": {}, "placeholder": {"captcha": "Please enter captcha", "companyName": "Please enter company name", "confirmPassword": "Please enter the confirmation password", "confirmPin": "Please confirm the USBKEY password", "email": "Please enter email", "emailCode": "Please enter the email verification code", "firstName": "Please enter first name", "ip": "Please enter ip", "ipv4": "Please enter IPv4", "ipv6": "Please enter IPv6", "lastName": "Please enter last name", "netMask": "Please enter the subnet mask", "password": "Please enter password", "passwordAgain": "Please enter password again", "path": "Please enter the path", "phone": "Please enter phone", "phoneNo": "Please enter your phone number", "pin": "Please enter USBKEY pin", "place": "Please enter", "port": "Please enter port", "pwd": "Please input a password", "readKey": "Please read key", "remark": "Please enter remark", "require": "Please enter the required fields", "select": "Please select", "sslAlgorithms": "Please select the algorithms", "tellYourNeeds": "Tell us more about your needs", "uploadFile": "Please upload the file", "username": "Please enter name", "value": "Please enter value", "verificationCode": "Please enter verification code"}, "product": {"specifications": "Specifications"}, "profile": {"LoginDateDueChangePwd": "The user password has not been changed for a long time. Please change the password", "about": "About", "baseInfo": "Essential Information", "changePassword": "Change Password", "componentName": "Component Name", "componentVersion": "Component version", "firstLoginChangePwd": "The password must be changed upon the first login", "reLoginTip": "You have changed your password, please log in again", "successTip": "Password changed successfully", "tenantInfo": "Tenant Information", "userInfo": "Personal Information"}, "tagsView": {"close": "Close Page", "closeAll": "Close All", "closeCurrent": "Close Current", "closeLeft": "Close Left", "closeOther": "Close Other", "closeRight": "Close Right", "refresh": "Refresh Page"}, "themeConfig": {"addTheme": "Add Theme", "advancedConfig": "Advanced Config", "backgroundColor": "Background Color", "backgroundHoverColor": "Hover Color", "checkFileTip": "The file format is incorrect. Please upload it again.", "chooseFile": "Select Theme File", "clickUpload_icon": "Click to upload the icon picture", "clickUpload_loginCover": "Click to upload the Login Cover", "clickUpload_loginLogo": "Click to upload the Login Logo", "clickUpload_title": "Click to upload the title picture", "color": "Color", "contentArea": "Content Area", "currentTopic": "Current Topic", "customTopic": "Custom Topic", "defaultConfig": "<PERSON><PERSON><PERSON>g", "deleteThemeTip": "Determine that you want to delete the topic?", "displayArea": "Display Area", "downloadFile": "Download Template File", "emptyFileTip": "Please upload file", "existFile": "An existing file exists. Are you sure you want to overwrite it?", "fileType_json": "Please upload .json type file", "fileType_svg": "Please upload .svg type file", "fileType_zip": "Please upload .zip type file", "followTheme": "Follow the theme color", "fontSize": "Font-Size", "fontStyle": "Italicized Or Not", "goBackConfig": "Last Step", "highlights": "HightLights", "isCoverSystem": "Whether to overwrite the system default theme", "isCoverSystemTip": "If you do not select this option, the current local theme does not take effect", "layoutConfig": "Layout Configuration", "leftIcon": "The Left Icon", "leftIconTip": "The left icon is displayed when the sidebar is shrunk. Please pay attention to the size of the uploaded image; If not set, empty the file", "loginCover": "<PERSON>gin <PERSON>", "loginLogo": "Login LOGO", "loginPage": "<PERSON><PERSON>", "logoConfig": "LOGO Configuration", "logoIcon": "Icon", "menu": "<PERSON><PERSON>", "menuHightLights": "<PERSON><PERSON>tLights", "openTags": "Open Tags-Views", "rightTitle": "The Right Title", "rightTitleTip": "The sidebar expands to show the left icon and the right title. If not set, clear the file or title content", "sameFileTip": "The theme cannot be re-named, please change the file themeName attribute and re-upload", "saveAsSysTheme": "Save As System Theme", "setDefaultTheme": "Set Default Theme", "showLogo": "Show LOGO", "sidebar": "Sidebar", "sidebarLogo": "Sidebar Logo", "subMenu": "SubMenu", "svgColor": "SVG Color", "systemDefaultTopic": "<PERSON><PERSON><PERSON>", "systemTopic": "System Topic", "textColor": "Text Color", "theme": "Theme", "themeColor": "Theme Color", "themeDark": "Dark Theme", "themeLight": "Light Theme", "themeNameEn": "Topic Name(en_US)", "themeNameZh": "Topic Name(zh_CN)", "title": "Title", "titleImage": "Picture Title", "titleText": "Text Title", "topNav": "TopNav", "webTitle": "Web dynamic Title"}, "tips": {"changeLanguage": "Switching between Chinese and English will refresh the page, and currently unsubmitted or saved data will be cleared. Do you want to continue with the operation?", "checkMaxLen": "The maximum length limit is {max}.", "day": "days", "deleteTip": "After deletion, it will not be recoverable. Are you sure you want to delete it?", "downloadFile": "Click to download the template file and upload the theme file in the correct format according to the template", "exportThemeConfigTip": "Do you want to export the current property configuration file. json?", "licenseTip": "License have", "licenseTip1": "due in days", "password": "Supports lengths of 8-64, including at least uppercase letters, lowercase letters, numbers, and special characters~! @ # $%^&* {};,.?/' The three types in", "removeOutTip": "Are you sure you want to remove it?", "resetThemeConfigTip": "Are you sure to reset the current configuration?", "saveThemeConfigLoading": "Setting local theme, please wait", "setThemeConfigTip": "Set as local theme before leaving the page?", "ukeyPassword": "The value contains 8 to 16 characters and can contain uppercase letters, lowercase letters, digits, and special characters ~! @ # $% ^ & * {}; ,.? / '\""}, "userList": {"add": "Add User", "addUser": "Add User Info", "authMode": "Authentication Mode", "certificate": "Certificate", "confirmPassword": "Confirm Password", "createCert": "Create cert", "deleteBatchTip": "Are you sure you want to delete the selected user?", "deleteTip": "Are you sure you want to delete this user?", "dn": "Dn", "downloadCert": "Download cert", "edit": "Edit User", "editUser": "Edit User Info", "integrVerification": "Integrity Verification", "lastLoginTime": "Last Login Time", "newPassword": "New Password", "oldPassword": "Old Password", "pCountry": "<PERSON>ress", "pCreatetime": "Create Time", "pFax": "Email", "pName": "User Name", "pRemarks": "Remark", "pRole": "Role", "pRoleName": "Role Name", "pTel": "Phone", "resetPaw": "Reset Password", "title": "User List", "updateCert": "Update cert"}, "validate": {"XssLen": "Not allowed to input<>special characters with a length of {0}- {1} Position", "checkUKeyType": "Please check if the USBKEY type matches", "confirmPwd": "The passwords entered twice are inconsistent", "email": "Please enter the corrent email", "insertUk": "USBKEY not inserted detected", "installUk": "Please install USBKEY components first", "ip": "Please enter the correct IP", "ipv4": "Please enter the correct IPv4 format", "ipv6": "Please enter the correct IPv6 Gateway format", "mask": "Please enter the correct subnet mask", "num": "Please enter an integer", "password": "Contain at least uppercase letters, lowercase letters, numbers, and special characters~!@#$%^&*{}; ,.? /'\\\" with length {min}-{max}", "path": "Please enter the correct path", "phone": "Please enter the correct phone", "port": "The port format is incorrect", "portRange": "The range of ports is from {min} to {max}", "uKeyPasswordError": "The password is incorrect, please re-enter", "uSBKeyCertEmpty": "There is no certificate in the USBKEY, please replace the device before proceeding"}}