{"apiKey": {"apiKey": "<PERSON><PERSON><PERSON><PERSON>"}, "common": {"all": "全部", "baseInfo": "基本信息", "dataSource": "数据来源", "decrypt": "解密", "descInfo": "描述信息", "digest": "摘要", "downloadApiBook": "下载接口手册", "downloadLoginConfig": "下载登录配置", "encrypt": "加密", "execute": "执行", "executionTime": "执行时间", "family": "家族", "format": "格式", "levelFormat": "L{0}级", "maxUploadNum": "最多上传{num}个文件", "portEnd": "结束端口", "portStart": "起始端口", "preview": "预览", "rate": "百分比", "refreshStatus": "刷新状态", "source": "来源", "tags": "标签", "test": "测试", "testResult": "测试结果"}, "homePage": {}, "login": {"filePassword": "文件加密口令"}, "msg": {"connect": "连接成功", "decrypt": "解密成功", "disable": "禁用成功", "enable": "启用成功", "encrypt": "加密成功", "inProgress": "执行中", "lock": "锁定成功", "regSucc": "正则表达式匹配成功", "scanning": "正在执行扫描...", "test": "测试成功", "testError": "测试失败", "unLock": "解锁成功"}, "pageTitle": {}, "placeholder": {}, "tips": {"checkCName": "只能包含中英文、数字、字符_-且长度为{0}-{1}位", "checkCName2": "只能包含中英文、数字、字符_-.且长度为{0}-{1}位", "checkEnCode1": "只能包含大小写字母、数字且长度为{0}-{1}位", "checkEnName": "支持长度{min}-{max}, 只能包含大、小写字母、数字、特殊字符_-", "checkEntryName": "多项使用逗号隔开，每项只能包含中英文、数字、字符_-且长度为{0}-{1}位", "fileSize": "文件限制在{0}", "ipv4Andipv6": "请输入正确的IPv4或IPv6地址", "kindOper": "存在分类时才能锁定解锁分类", "modifyClass": "只有存在分类模板才可进行修改分类操作", "regErr": "正则表达式匹配失败，请检查正则表达式或测试文本是否输入正确", "requestParam": "支持长度{min}-{max},格式为/***，支持数字大小写字母、特殊字符_-.", "startServiceTip": "确认要启动服务吗？", "stopServiceTip": "确认要停止服务吗？", "suggestHandle": "如果同时建议加密和脱敏时，优先显示加密", "tags": "多个标签用英文逗号隔开", "testText": "测试文本不为空时，需要先点击测试按钮，测试输入内容是否符合正则格式", "uploadFileTip": "提示：仅允许上传{0}格式文件,且不能超过{1}！", "uploadFileType": "请上传{0}类型文件", "uploadInfo": "只能上传{type}格式文件，且不超过{size}{unit}", "uploadInfo1": "文件不能超过{size}{unit}", "uploadInfo2": "只能上传{type}格式文件"}, "validate": {"algorithmValue": "支持长度1-256，不能以特殊字符开头，支持字母数字及特殊字符=", "classTip": "请选择分类信息", "enCode1": "长度{min}-{max}位,支持大、小写字母、数字、特殊字符_-", "enCode2": "长度{min}-{max}位,支持大、小写字母、数字、特殊字符_", "enCode3": "长度{min}-{max}位,支持大、小写字母、数字、特殊字符_-.", "fileDigest": "支持长度1-256，不能以特殊字符开头，支持中文字母数字及特殊字符=-:和空格", "fileSize": "上传文件大小不能超过{msg}!", "fileType": "上传文件只能是格式{type}", "firmware": "支持长度1-32，不能以特殊字符开头，支持中文字母数字及特殊字符_-和.", "notSupportChinese": "不支持中文", "number": "请输入数字", "numberRange": "整数范围为{min}-{max}", "path1": "支持/.../...或.../...格式，长度{min}-{max}", "remoteUser": "支持长度1-256,支持中文字母数字及特殊字符~!@#$%^&*{};,.?/'\\", "specialCharRemark": "请勿包含除空格、逗号、分号、句号、 - 和 _ 以外的特殊字符", "startEndPort": "起始端口不能大于结束端口", "url": "URL格式不正确", "zhCode1": "长度{min}-{max}位,支持大、小写字母、中文、数字、特殊字符_-", "zhCode2": "长度{min}-{max}位,支持大、小写字母、中文、数字、特殊字符_", "zhCode3": "长度{min}-{max}位,支持大、小写字母、中文、数字、特殊字符_-."}}