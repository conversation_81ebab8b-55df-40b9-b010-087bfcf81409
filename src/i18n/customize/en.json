{"apiKey": {"apiKey": "<PERSON><PERSON><PERSON><PERSON>"}, "common": {"all": "All", "backupSuccess": "Backup successful", "baseInfo": "Basic Information", "dataSource": "Data Sources ", "decrypt": "Decrypt", "descInfo": "Description", "digest": "Digest", "downloadApiBook": "Download Interface Manual", "downloadLoginConfig": "Download login configuration", "encrypt": "Encrypty", "execute": "Execute", "executionTime": "Execution Time", "family": "Family", "format": "Format", "levelFormat": "Level {0}", "maxUploadNum": "Upload a maximum of {num} files", "operContent": "Operation content", "operResult": "Operation result", "portEnd": "End Port", "portStart": "Starting Port", "preview": "Preview", "rate": "Percentage", "refreshStatus": "Refresh Status", "requestIp": "Request Source IP", "source": "Source", "tags": "Tags", "test": "Test", "testResult": "Test Result"}, "homePage": {}, "login": {"filePassword": "File Encryption Password"}, "msg": {"connect": "Connection Successfully", "decrypt": "Decryption Successfully", "disable": "Disabled successfully", "enable": "Successfully enabled", "encrypt": "Encryption Successfully", "inProgress": "In Progress", "lock": "Locked Successfully", "regSucc": "Regular expression matching successful", "scanning": "Executing scan...", "test": "Connection Successful", "testError": "Test failed", "unLock": "Unlocked Successfully"}, "pageTitle": {}, "placeholder": {}, "tips": {"checkCName": "It can only contain Chinese and English, numbers, characters _ -, and has a length of {0}- {1} Position", "checkCName2": "It can only contain Chinese and English, numbers, and characters _ - And the length is {0}- {1} Position", "checkEnCode1": "Can only contain uppercase and lowercase letters, numbers, and have a length of {0}- {1} Position", "checkEnName": "Support length {min}- {max},  Can only contain uppercase and lowercase letters, numbers, and special characters_-", "checkEntryName": "Multiple items separated by commas, each item can only contain Chinese, English, numbers, and characters _- and must be {0}-{1} characters long.", "fileSize": "The file is limited to {0}", "ipv4Andipv6": "Please enter the correct IPv4 or IPv6 address", "isCoverSystemTip": "If not checked, the current local theme will not take effect", "kindOper": "Only when there is a category can it be locked and unlocked", "licenseTip": "License ", "modifyClass": "Only when there is a classification template can the classification operation be modified", "regErr": "Regular expression matching failed, please check if the regular expression or test text is entered correctly", "requestParam": "Support length {min}- {max}, The format is/* * *, supporting uppercase and lowercase letters, special characters _ -", "startServiceTip": "Are you sure you want to start the service?", "stopServiceTip": "Are you sure you want to stop the service?", "suggestHandle": "If both encryption and desensitization are recommended, priority should be given to displaying encryption", "tags": "Multiple tags separated by commas in English", "testText": "When the test text is not empty, you need to click the test button first to test whether the input content conforms to the regular format", "uploadFileTip": "Tip: Only {0} format files are allowed to be uploaded, and cannot exceed {1}!", "uploadFileType": "Please upload a file of type {0}", "uploadInfo": "Only files in {type} format can be uploaded, and they should not exceed {size} {unit}", "uploadInfo1": "The file cannot exceed {size} {unit}", "uploadInfo2": "Only files in {type} format can be uploaded"}, "validate": {"algorithmValue": "Supports lengths 1-256, cannot start with special characters, supports alphanumeric and special characters=", "classTip": "Please select category information", "enCode1": "length {min}- {max} bit, supports uppercase and lowercase letters, numbers, and special characters_-", "enCode2": "length {min}- {max} bit, supports uppercase and lowercase letters, numbers, and special characters_", "enCode3": "length {min}- {max} bit, supports uppercase and lowercase letters, numbers, and special characters _ -.", "fileDigest": "Supports lengths 1-256, cannot start with special characters, supports Chinese alphanumeric characters and special characters=-: and spaces", "fileSize": "The size of the uploaded file cannot exceed {msg}!", "fileType": "Upload files can only be in format {type}", "firmware": "Supports lengths of 1-32, cannot start with special characters, supports Chinese alphanumeric characters and special characters _ - and", "notSupportChinese": "Not supporting Chinese", "number": "please enter a number", "numberRange": "The integer range is {min}- {max}", "path1": "Support/.../ Or.../ Format, length {min}- {max}", "remoteUser": "Supports lengths 1-256, supports Chinese alphanumeric and special characters~! @ # $%^&* {};,.?/'\\", "specialCharRemark": "Please do not include special characters other than spaces, commas, semicolons, periods, -, and _", "startEndPort": "The starting port cannot be greater than the ending port", "url": "The URL format is incorrect", "zhCode1": "length {min}- {max} bit, supports uppercase and lowercase letters, Chinese characters, numbers, and special characters_-", "zhCode2": "length {min}- {max} bit, supports uppercase and lowercase letters, Chinese characters, numbers, and special characters_", "zhCode3": "length {min}- {max} bit, supports uppercase and lowercase letters, Chinese characters, numbers, and special characters _ -."}}