<template>
  <div
    :class="classObj"
    class="app-wrapper"
    :style="{ '--current-color': theme }"
  >
    <div class="navbar-box">
      <navbar2 />
      <!-- <top-nav id="topmenu-container" class="topmenu-container" /> -->
    </div>
    <div
      v-if="device === 'mobile' && sidebar.opened"
      class="drawer-bg"
      @click="handleClickOutside"
    />
    <div
      class="sidebar-container"
      :style="{
        backgroundColor:
          settings.sideTheme == 'theme-custom'
            ? settings.menuBackgroundCustom
            : settings.sideTheme === 'theme-dark'
            ? variables.menuBackground
            : settings.sideTheme === 'theme-theme'
            ? settings.theme
            : variables.menuLightBackground,
      }"
    >
      <sidebar style="height: 100%" />
    </div>
    <div :class="{ hasTagsView: needTagsView }" class="main-container">
      <tags-view v-if="needTagsView" />
      <app-main />
    </div>
  </div>
</template>

<script>
// import RightPanel from "@/components/RightPanel";
import { AppMain, Navbar2, Sidebar, TagsView } from "./components";
import ResizeMixin from "./mixin/ResizeHandler";
import { mapState } from "vuex";
import variables from "@/assets/styles/variables.scss";
// import { getSession } from "@/utils/session";
// import { getToken } from "@/utils/auth";

export default {
  name: "Layout2",
  components: {
    AppMain,
    Navbar2,
    // RightPanel,
    Sidebar,
    TagsView,
  },
  mixins: [ResizeMixin],
  data() {
    return {};
  },
  computed: {
    ...mapState({
      settings: (state) => state.settings,
      theme: (state) => state.settings.theme,
      sideTheme: (state) => state.settings.sideTheme,
      sidebar: (state) => state.app.sidebar,
      device: (state) => state.app.device,
      needTagsView: (state) => state.settings.tagsView,
      fixedHeader: (state) => state.settings.fixedHeader,
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === "mobile",
        fixedTopLayout: true,
      };
    },
    variables() {
      return variables;
    },
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch("app/closeSideBar", { withoutAnimation: false });
    },
    // 全局postMessage事件
    // messageEvt(event) {
    //   // token 和 withoutDownload 目前用于下载请求 分别表示 接收下载状态的凭证 和 是否不需要直接下载
    //   const {
    //     type,
    //     path,
    //     label,
    //     query,
    //     token,
    //     withoutDownload = false,
    //     alert,
    //     initSuccData,
    //     dialogValue,
    //     initValue,
    //     loadingData,
    //     reLoginType,
    //   } = event.data;

    //   const { oldPath, newPath } = event.data;

    //   if (type === "openNewView") {
    //     this.$router.push({
    //       path,
    //       meta: { title: label },
    //       query: { ...query, title: label },
    //     });
    //   } else if (type === "openNewIFrame") {
    //     this.$router.push({
    //       path: `/system/page/iframe/url=${encodeURIComponent(path)}`,
    //       meta: { title: "用户管理" },
    //     });
    //   } else if (type === "openNewWindow") {
    //     const otherWindow = window.open();
    //     otherWindow.opener = null;
    //     otherWindow.location = path;
    //   } else if (type === "closeTag") {
    //     window.$eventBus.$emit("closeTag", {
    //       path: `/common/page__iframeUrl=${encodeURIComponent(
    //         path,
    //       )}&title=${label}`,
    //     });
    //   } else if (type === "closeCurrentTag") {
    //     window.$eventBus.$emit("closeCurrentTag");
    //   } else if (type === "replaceTagView") {
    //     window.$eventBus.$emit("replaceCurrentTag", {
    //       oldView: { path: oldPath },
    //       newView: { path: newPath, fullPath: newPath, title: label, query },
    //     });
    //   } else if (type === "toggleScreenFull") {
    //     if (document.body.classList.contains("fullscreen-main")) {
    //       document.body.classList.remove("fullscreen-main");
    //     } else {
    //       document.body.classList.add("fullscreen-main");
    //     }
    //   } else if (type === "sessionTimeout") {
    //     window.$eventBus.$emit("sessionTimeout");
    //   } else if (type === "download") {
    //     window.$eventBus.$emit("download", {
    //       path,
    //       withoutDownload,
    //     });
    //   } else if (type === "downloadResponse") {
    //     // 本处做测试用，需要各个业务线集成响应返回数据
    //     console.log("响应下载结果", event.data);
    //   } else if (type === "loginUser") {
    //     window.$eventBus.$emit("loginUser", {
    //       token,
    //     });
    //   } else if (type === "alert") {
    //     this.$message({
    //       message: alert.message || "",
    //       duration: alert.duration || 2000,
    //       type: alert.type || "info",
    //     });
    //   } else if (type === "initSuccData") {
    //     window.$eventBus.$emit("initSuccData", {
    //       initSuccData,
    //     });
    //   } else if (type === "dialog") {
    //     window.$eventBus.$emit("dialog", {
    //       dialogValue,
    //     });
    //   } else if (type === "LicenceSucc") {
    //     window.$eventBus.$emit("LicenceSucc");
    //   } else if (type === "reLogin") {
    //     let isUk = getSession("isUkLogin");
    //     let msg = this.$t("common.reLoginTip");
    //     if (isUk && reLoginType) {
    //       if (reLoginType == 1) {
    //         // reLoginType: 1：未下载uk控件 2： 未插入uk  3：更换uk
    //         msg = this.$t("common.ukControl");
    //       } else if (reLoginType == 2) {
    //         msg = this.$t("common.unInsetUk");
    //       } else if (reLoginType == 3) {
    //         msg = this.$t("common.updateUk");
    //       }
    //     }
    //     this.confirmMessage(
    //       msg,
    //       "warning",
    //       false,
    //       false,
    //       false,
    //       this.$t("common.reLogin"),
    //       this.$t("common.sysTip"),
    //       false,
    //     )
    //       .then(() => {
    //         let url = this.getLogoutURL();
    //         if (getToken()) {
    //           this.$store.dispatch("LogOut").then((res) => {
    //             if (url) {
    //               this.$store.dispatch("app/setGlobalLoading", true);
    //               location.href = url;
    //             } else {
    //               location.href = `${this.getBasePrefix()}login`;
    //             }
    //           });
    //         } else {
    //           this.$store.dispatch("FedLogOut").then((res) => {
    //             if (url) {
    //               this.$store.dispatch("app/setGlobalLoading", true);
    //               location.href = url;
    //             } else {
    //               location.href = `${this.getBasePrefix()}login`;
    //             }
    //           });
    //         }
    //       })
    //       .catch(() => {});
    //   } else if (type === "fullLoad") {
    //     // 全局loading
    //     let loading = this.$loading({
    //       lock: true,
    //       background: loadingData?.background || "hsla(0,0%,100%,.9)",
    //       text: loadingData?.text || "Loading",
    //     });
    //     if (!loadingData.show) {
    //       loading.close();
    //     }
    //     window.$eventBus.$emit("fullIframeFixed", {
    //       fullFixed: loadingData.fullFixed,
    //     });
    //   } else if (type == "downloadUkControl") {
    //     // 下载uk控件
    //     this.confirmMessage(this.$t("initLogin.ukTip"))
    //       .then(() => {
    //         window.location.href = `${this.getBasePrefix(
    //           true,
    //         )}safe-usbkey_3.1.4.exe`;
    //       })
    //       .catch(() => {});
    //   }
    // },
  },
  // mounted() {
  //   window.addEventListener("message", this.messageEvt);
  //   // 调用消息通知接口，取出未读的列表
  //   // this.getUnReadMessage();
  // },

  // beforeDestroy() {
  //   window.removeEventListener("message", this.messageEvt);
  // },
};
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/mixin.scss";
@import "~@/assets/styles/variables.scss";

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$base-sidebar-width});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.mobile .fixed-header {
  width: 100%;
}
</style>
<style lang="scss">
#app {
  //安全平台 顶部浮动样式
  .fixedTopLayout {
    .navbar-box {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      z-index: 1002;
      .navbar {
        display: flex;
        justify-content: space-between;
        .sidebar-logo-container {
          background: none !important;
        }
        color: #fff !important;
        .right-menu {
          flex: 1;
          text-align: right;
          .right-menu-item {
            color: #fff !important;
          }
        }
      }
      .topmenu-container {
        background: rgb(28, 36, 57);
        border-bottom: 1px solid #e8e8e8;
        &.el-menu--horizontal {
          & > .el-menu-item,
          .el-submenu .el-submenu__title {
            height: 45px !important;
            line-height: 45px !important;
          }
        }
      }
    }
    .main-container {
      padding-top: 55px;
      &.hasTagsView {
        .app-main {
          height: calc(100vh - 95px);
          background-color: #ecf1f7;
        }
      }
    }

    .sidebar-logo-container {
      min-width: 224px;
      width: auto;
      text-align: center;
      padding: 0px 10px;
      border-bottom: none;
      &.collapse {
        .sidebar-logo-link {
          justify-content: center;
        }
      }
      .sidebar-logo-link {
        align-items: center;
        justify-content: center;
        padding: 0px;
      }
    }
    .sidebar-container {
      top: 55px;
      box-shadow: none;
      box-shadow: 1px 0px 4px 0 rgba(0, 0, 0, 0.1);
      overflow: inherit;
    }
  }
}
</style>
