<template>
  <section class="app-main" id="scrollAppMain">
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view :key="key" />
      </keep-alive>
    </transition>
  </section>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  name: "AppMain",
  computed: {
    ...mapGetters(["licenseInfo"]),
    cachedViews() {
      return this.$store.state.tagsView.cachedViews;
    },
    key() {
      return this.$route.path;
    },
  },
  watch: {
    licenseInfo: {
      handler(value) {
        if (value) {
          this.getLicenseNotice();
        }
      },
      immediate: true,
    },
  },
  methods: {
    getLicenseNotice() {
      if (this.licenseInfo.licenceExpirationTips) {
        let msg = `<strong>${this.$t(
          "tips.licenseTip",
        )}<i style="color: #fe6c6f; margin: 0 5px;">${
          this.licenseInfo.licenceExpirationTips
        }</i>${this.$t("tips.licenseTip1")}</strong>`;
        if (this.licenseInfo.licenceExpirationTips == "0") {
          msg = `<strong style="color: #fe6c6f;">${this.$t(
            "tips.licenseExpingTip",
          )}</strong>`;
        } else if (Number(this.licenseInfo.licenceExpirationTips) < 0) {
          msg = `<strong>${this.$t(
            "tips.licenseExpingTip",
          )}<i style="color: #fe6c6f; margin: 0 5px;">${-Number(
            this.licenseInfo.licenceExpirationTips,
          )}</i>${this.$t("tips.day")}</strong>`;
        }
        this.$notify({
          customClass: "licenceNotice",
          title: this.$t("common.warn"),
          type: "warning",
          duration: 0,
          dangerouslyUseHTMLString: true,
          message: msg,
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  // min-height: calc(100vh - 50px);
  height: calc(100vh - 56px);
  width: 100%;
  position: relative;
  overflow: hidden;
  overflow-y: auto;
  // background-color: #ecf1f7;
  background-color: #eee;
  // padding: 24px
  padding: 16px;
}

.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    // min-height: calc(100vh - 84px);
    height: calc(100vh - 96px);
  }

  .fixed-header + .app-main {
    padding-top: 84px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 17px;
  }
}

.app-main:has(.vue-grid-layout) {
  padding: 0px;
}
</style>
