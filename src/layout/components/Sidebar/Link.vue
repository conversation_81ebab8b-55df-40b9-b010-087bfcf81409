<template>
  <component :is="type" @click="openMenu" v-bind="linkProps(to)">
    <slot />
  </component>
</template>

<script>
import { isExternal } from "@/utils/validate";

export default {
  props: {
    to: {
      type: [String, Object],
      required: true,
    },
  },
  computed: {
    isExternal() {
      return isExternal(this.to);
    },
    type() {
      if (this.isExternal) {
        return "a";
      }
      return "router-link";
    },
  },
  methods: {
    openMenu() {
      let cur = this.$route;
      let to = this.to;
      let curIframeUrl = cur?.query?.url || "";
      let toIframeUrl = to?.query?.url || "";
      if (toIframeUrl && cur.path == to.path && curIframeUrl != toIframeUrl) {
        this.$router.replace(this.to);
        window.$eventBus.$emit("openMenu");
        return;
      }
      if (toIframeUrl.indexOf("target=_blank") > -1) {
        window.open(toIframeUrl);
      } else {
        this.$router.push(this.to);
      }
    },
    linkProps(to) {
      if (this.isExternal) {
        return {
          href: to,
          target: "_blank",
          rel: "noopener",
        };
      }
      return {
        to: to,
      };
    },
  },
};
</script>
