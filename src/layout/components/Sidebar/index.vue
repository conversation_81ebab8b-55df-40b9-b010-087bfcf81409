<template>
  <div
    :class="{ 'has-logo': showLogo }"
    :style="{
      backgroundColor:
        settings.sideTheme == 'theme-custom'
          ? settings.menuBackgroundCustom
          : settings.sideTheme === 'theme-dark'
          ? variables.menuBackground
          : settings.sideTheme === 'theme-theme'
          ? settings.theme
          : variables.menuLightBackground,
    }"
  >
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar :class="settings.sideTheme" wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="
          settings.sideTheme == 'theme-custom'
            ? settings.menuBackgroundCustom
            : settings.sideTheme === 'theme-dark'
            ? variables.menuBackground
            : settings.sideTheme === 'theme-theme'
            ? settings.theme
            : variables.menuLightBackground
        "
        :text-color="
          settings.sideTheme == 'theme-custom'
            ? settings.menuColorCustom
            : settings.sideTheme === 'theme-dark' ||
              settings.sideTheme === 'theme-theme'
            ? variables.menuColor
            : variables.menuLightColor
        "
        :unique-opened="true"
        :active-text-color="settings.theme"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item
          v-for="(route, index) in sidebarRouters"
          :key="route.path + index"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
    <sidebar-footer :collapse="isCollapse" />
    <span class="slidebar-collage" @click="toggleSideBar">
      <i class="el-icon el-icon-caret-right" v-show="isCollapse"></i>
      <i class="el-icon el-icon-caret-left" v-show="!isCollapse"></i>
    </span>
  </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import Logo from "./Logo";
import SidebarItem from "./SidebarItem";
import variables from "@/assets/styles/variables.scss";
import sidebarFooter from "./siderFooter.vue";

export default {
  components: { SidebarItem, Logo, sidebarFooter },
  data() {
    return {
      // 自定义颜色
      menuColorCustom: this.$store.state.settings.menuColorCustom,
      menuColorActiveCustom: this.$store.state.settings.menuColorActiveCustom,
      menuBackgroundCustom: this.$store.state.settings.menuBackgroundCustom,
    };
  },
  computed: {
    ...mapState(["settings"]),
    ...mapGetters(["sidebarRouters", "sidebar"]),
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo;
    },
    variables() {
      return variables;
    },
    isCollapse() {
      return !this.sidebar.opened;
    },
  },
  data() {
    return {
      isNestIframe: !(window.self === window.top), // web平台是否被iframe嵌套
    };
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },
  },
};
</script>
<style lang="scss" scoped>
.slidebar-collage {
  position: absolute;
  right: -12px;
  top: 50%;
  margin-top: -90px;
  display: inline-block;
  height: 80px;
  width: 12px;
  border-radius: 0px 10px 10px 0px;
  background: #fff;
  cursor: pointer;
  font-size: 12px;
  text-align: right;
  line-height: 80px;
  color: #252b3a;
  box-shadow: 1px 0px 4px 0 rgba(0, 0, 0, 0.1);
  .el-icon {
    transform: scale(1.5);
  }
}
</style>
