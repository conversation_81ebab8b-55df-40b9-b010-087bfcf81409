<template>
  <el-dialog
    :title="$t('common.downloadLoginConfig')"
    :visible.sync="dialogVisible"
    width="600px"
    append-to-body
    :close-on-click-modal="false"
    :before-close="cancelClick"
    @open="onOpenDig"
  >
    <el-form
      class="mgr40"
      v-loading="loading"
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="auto"
      label-position="right"
      @submit.native.prevent
    >
      <sw-form-item :label="$t('login.filePassword')" prop="filePassword">
        <el-input
          :type="form.isOpen ? 'text' : 'password'"
          v-model="form.filePassword"
          :placeholder="$t('placeholder.place')"
          maxlength="64"
          @keyup.enter.native="submitClick"
        >
          <span
            slot="suffix"
            @click="form.isOpen = !form.isOpen"
            class="eye-switch-btn"
          >
            <svg-icon
              :icon-class="!form.isOpen ? 'eyeOpen' : 'eyeClose'"
            ></svg-icon>
          </span>
        </el-input>
      </sw-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button
        class="oper-btn cancel-btn mgr10"
        @click="cancelClick"
        :loading="loading"
        >{{ $t("common.cancel") }}</el-button
      >
      <el-button
        type="primary"
        class="oper-btn"
        :loading="loading"
        @click="submitClick"
        >{{ $t("common.submit") }}</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
let defaultOptions = { filePassword: "", isOpen: false };
import {} from "@/api/login";
import { downBackupUkFile } from "@/api/login";

export default {
  name: "downloadLoginConfig",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      form: { ...defaultOptions },
      loading: false,
      rules: {
        filePassword: [
          {
            required: true,
            message: this.$t("placeholder.place"),
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    onOpenDig() {},

    cancelClick() {
      this.form.isOpen = false;
      this.$refs.formRef?.resetFields();
      this.$emit("eventClose");
    },
    submitClick() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          downBackupUkFile(this.form.filePassword).then((res) => {
            this.$emit("eventClose");
          });
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.eye-switch-btn {
  cursor: pointer;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 4px;
  &:hover {
    color: #444;
  }
}
</style>
